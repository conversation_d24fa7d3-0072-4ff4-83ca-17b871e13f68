module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'node',
    moduleNameMapper: {
      "^@shared/(.*)$": "<rootDir>/src/shared/$1",
      "^@utils/(.*)$": "<rootDir>/src/utils/$1",
      "^@instructor-api/(.*)$": "<rootDir>/src/instructor-api/$1",
      "^@member-api/(.*)$": "<rootDir>/src/member-api/$1",
      "^@studio-cms/(.*)$": "<rootDir>/src/studio-cms/$1",
      "^@backoffice-cms/(.*)$": "<rootDir>/src/backoffice-cms/$1",
      "^@public-api/(.*)$": "<rootDir>/src/public-api/$1",
      "^@database/(.*)$": "<rootDir>/src/database/$1",
      "^@config/(.*)$": "<rootDir>/src/config/$1",
      "^@mixins/(.*)$": "<rootDir>/src/mixins/$1",
    },
  }