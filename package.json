{"name": "app", "version": "1.0.0", "main": "src/index.ts", "scripts": {"clean": "rm -rf dist", "copy-specs": "cp -r src/specs dist/", "build": "npm run clean && tsc && tsc-alias && npm run copy-specs", "start": "node dist/index.js", "start-cron": "node dist/scheduled-tasks/run.js", "dev": "nodemon -r tsconfig-paths/register src/index.ts", "cron": "nodemon -r tsconfig-paths/register src/scheduled-tasks/run.ts", "make:migration": "sequelize-cli migration:generate --name", "migrate": "sequelize-cli db:migrate", "migrate:status": "sequelize-cli db:migrate:status", "migrate:rollback": "sequelize-cli db:migrate:undo", "make:seeder": "sequelize-cli seed:generate --name", "seed": "sequelize-cli db:seed:all", "seed:one": "sequelize-cli db:seed --seed", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "@aws-sdk/client-ses": "^3.716.0", "@cometchat-pro/chat": "^3.0.13", "@cometchat/chat-uikit-react": "^6.1.0", "@googlemaps/google-maps-services-js": "^3.4.0", "@types/sequelize": "^4.28.20", "axios": "^1.7.9", "big.js": "^6.2.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.5", "express": "^4.21.1", "express-openapi-validator": "^5.3.9", "firebase-admin": "^13.0.1", "luxon": "^3.6.1", "mysql2": "^3.14.1", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "tsconfig-paths": "^4.2.0", "twilio": "^5.7.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@swc/core": "^1.10.0", "@types/big.js": "^6.2.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.10.1", "concurrently": "^9.1.0", "jest": "^29.7.0", "nodemon": "^3.1.7", "ts-jest": "^29.3.0", "ts-node": "^10.9.2", "tsc-alias": "^1.8.10", "typescript": "^5.7.2"}}