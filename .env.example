NODE_ENV=production
PORT=3000

BO_ID=***
DATABASE_HOST=mysql8
DATABASE_NAME=***
DATABASE_USERNAME=***
DATABASE_PASSWORD=***
DATABASE_TIMEZONE=+08:00
DATABASE_POOL_CONNECTION_LIMIT=5

# Mock auth
MOCK_AUTH_UID=***
MOCK_AUTH_EMAIL=***

# Comma-separated
CORS_ALLOWED_ORIGINS=https://api01.1215242.xyz,https://api02.1215242.xyz

# Firebase credentials in JSON format
FIREBASE_APP_CREDENTIALS_JSON=***
FIREBASE_APP_CREDENTIALS_JSON_STUDIOS=***
FIREBASE_APP_CREDENTIALS_JSON_BACKOFFICE=***

# Google APIs
GOOGLE_MAPS_API_KEY=***

# File uploads
FILE_UPLOADS_DIR=/app/file-uploads
FILE_STORE_DIR=/app/file-store

# AWS
AWS_REGION=ap-southeast-1
AWS_ACCESS_KEY_ID=***
AWS_SECRET_ACCESS_KEY=***
AWS_SES_REGION=ap-southeast-1
AWS_SECRETS_NAME=
AWS_RDS_SECRETS_NAME=
AWS_S3_BUCKET=resources.1215242.xyz

# HitPay API keys
HITPAY_API_URL=https://api.sandbox.hit-pay.com
HITPAY_API_KEY=***
HITPAY_API_SALT=***
HITPAY_WEBHOOK_URL=https://api01.1215242.xyz

# Pictures
PIC_URL_PREFIX=https://api01.1215242.xyz

# Twilio
TWILIO_ACCOUNT_SID=***
TWILIO_AUTH_TOKEN=***
TWILIO_VERIFY_SID=***

# Studio CMS
# STUDIO_USER_INVITE_EMAIL_LIVE_MODE
# - Determine whether to send the invite email
# - true (case sensitive) means send, anything else means do not send.
# STUDIO_USER_INVITE_EMAIL_CC
# - Comma-delimited list of email addresses, can be blank.
# STUDIO_USER_INVITE_EMAIL_TEMPLATE_NAME
# - AWS SES template name
STUDIO_USER_INVITE_EMAIL_LIVE_MODE=false
STUDIO_USER_INVITE_EMAIL_FROM=Do Not Reply<<EMAIL>>
STUDIO_USER_INVITE_EMAIL_CC=
STUDIO_USER_INVITE_EMAIL_TEMPLATE_NAME=StudioUser_Invite
STUDIO_USER_INVITE_EMAIL_SIGNUP_URL=https://studios.1215242.xyz/signup


# APP_ALLOW_BOOK_SELF
# - Determine whether allowed to book self
#   (for development convenience).
# - true (case sensitive) means allowed, anything else means not allowed.
APP_ALLOW_BOOK_SELF=true

# BOOKING_FREE_HOURS
# - Number of hours below which cancellation is no longer free
BOOKING_FREE_HOURS=12

SESSION_EXERCISE_MODE=2

# Months of Credit Topup Extends
CREDITS_TOPUP_EXTENDS_MONTHS=6
CREDITS_RENEW_PER_MONTH=100

WELCOME_FREE_CREDIT=500

# OTP Rate Limiting
OTP_SEND_LIMIT_PER_HOUR=1

# CometChat
COMETCHAT_API_KEY=***
COMETCHAT_APP_ID=***
COMETCHAT_REGION=us
COMETCHAT_USERNAME=***
COMETCHAT_PASSWORD=***

# Tawk.to
TAWK_API_KEY=***

IGLOO_CLIENT_ID=***
IGLOO_CLIENT_SECRET=***
IGLOO_TOKEN_URL=https://auth.igloohome.co/oauth2/token
IGLOO_API_ROOT=https://api.igloodeveloper.co/igloohome