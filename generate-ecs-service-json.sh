#!/bin/bash

# Usage:
#   ./generate-ecs-service-json.sh [api|cron] [CONFIG_SECRET_NAME]
# The script will load variables from AWS Secrets Manager if CONFIG_SECRET_NAME is provided, prompt for any missing, and throw an error if any required variable is still missing.

set -e

SERVICE_TYPE=${1:-api}
CONFIG_SECRET_NAME=${2:-$CONFIG_SECRET_NAME}

# Helper function to prompt for a variable if not set
prompt_var() {
  local var_name="$1"
  local prompt_text="$2"
  if [ -z "${!var_name}" ]; then
    read -p "$prompt_text: " $var_name
    export $var_name
  fi
}

# Helper function to check if a variable is set, else throw error
require_var() {
  local var_name="$1"
  if [ -z "${!var_name}" ]; then
    echo "Error: $var_name is required but not set."
    exit 1
  fi
}

# If CONFIG_SECRET_NAME is provided, load variables from AWS Secrets Manager
if [ -n "$CONFIG_SECRET_NAME" ]; then
  echo "Loading environment variables from AWS Secrets Manager secret: $CONFIG_SECRET_NAME..."
  SECRET_JSON=$(aws secretsmanager get-secret-value --secret-id "$CONFIG_SECRET_NAME" --query SecretString --output text)
  if [ -z "$SECRET_JSON" ]; then
    echo "Error: Could not retrieve secret $CONFIG_SECRET_NAME from AWS Secrets Manager."
    exit 1
  fi
  # Export each key in the JSON as an environment variable
  for key in $(echo "$SECRET_JSON" | jq -r 'keys[]'); do
    export "$key"="$(echo "$SECRET_JSON" | jq -r ".$key")"
  done
fi

# Common required variables
prompt_var ECS_CLUSTER_NAME "Enter ECS cluster name"
prompt_var SUBNET_1 "Enter Subnet 1 ID"
prompt_var SUBNET_2 "Enter Subnet 2 ID"
prompt_var SUBNET_3 "Enter Subnet 3 ID"
prompt_var SECURITY_GROUP "Enter Security Group ID"

# Require all common variables
require_var ECS_CLUSTER_NAME
require_var SUBNET_1
require_var SUBNET_2
require_var SUBNET_3
require_var SECURITY_GROUP

if [ "$SERVICE_TYPE" = "api" ]; then
  prompt_var TARGET_GROUP_ARN "Enter Target Group ARN"
  require_var TARGET_GROUP_ARN
  echo "Generating ecs-servicep-api.json from ecs-service.template.json..."
  envsubst < ecs-service.template.json > ecs-service-api.json
  echo "ecs-service-api.json generated."
else
  echo "Generating ecs-service-cron.json from ecs-service-cron.template.json..."
  envsubst < ecs-service-cron.template.json > ecs-service-cron.json
  echo "ecs-service-cron.json generated."
fi

echo "Done!" 