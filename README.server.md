### Server

- Hosting Provider: AWS
- OS: Amazon Linux 2023

### Remote Access

SSH / SFTP
- ```PermitRootLogin no```
- ```PasswordAuthentication no```
- ```PermitEmptyPasswords no```

```ec2-user``` has sudo rights.

### ```crontab```

- (Daily) Backup MySQL database
- (Daily) Cleanup daily backups (keep max 3 recent)
- (Daily) Renew LetsEncrypt certificates

### Layout

Base folder: ```/home/<USER>/docker```

- ```httpd```: apache httpd, LetsEncrypt certbot
  - ```conf```: apache config files
  - ```letsencrypt```: LetsEncrypt / certbot
  - ```webroot```: webroot of the domains
- ```mysql```: mysql server
- ```api01```: deployment folder for ```vifit-nodejs``` (API)
- ```cron```: deployment folder for ```vifit-nodejs``` (scheduled tasks)
  - ```FcmSender``` to send push notifications
  - ```Release Hold``` to release ```PENDING_PAYMENT``` holds
  - ```NotifyComplete``` to remind instructors to complete session (not running currently)
  - ```NotifyFeedback``` to remind members to feedback (not running currently)
- ```studios```: deployment folder for ```vifit-studios```
- ```backoffice```: deployment folder for ```vifit-backoffice```

### Domains

- api01.vifit.ai
- studios.vifit.ai
- backoffice.vifit.ai
