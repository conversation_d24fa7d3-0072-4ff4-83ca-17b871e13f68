# Stage 1: Development dependencies & Build
FROM node:22-slim AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install ALL dependencies (including devDependencies)
RUN npm install

# Copy source code
COPY . .

# Build the application (assuming you have a build script)
RUN npm run build

# Stage 2: Production dependencies
FROM node:22-slim AS production-deps

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install ONLY production dependencies
RUN npm ci --only=production

# Stage 3: Final stage
FROM node:22-slim AS runner

WORKDIR /app

# Copy built assets from builder
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/src ./src

# Copy production node_modules
COPY --from=production-deps /app/node_modules ./node_modules

# Copy package.json files
COPY package*.json ./

# Default environment variables (minimal required defaults)
ENV NODE_ENV=production \
    PORT=3000

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nodejs \
    && chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose the port
EXPOSE ${PORT}

# Start the application
CMD ["npm", "start"]