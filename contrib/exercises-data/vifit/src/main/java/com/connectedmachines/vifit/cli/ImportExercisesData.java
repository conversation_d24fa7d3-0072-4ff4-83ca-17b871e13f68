package com.connectedmachines.vifit.cli;

import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicReference;

import static com.google.common.base.Preconditions.checkState;
import static java.lang.System.out;
import static java.nio.charset.StandardCharsets.ISO_8859_1;
import static java.nio.charset.StandardCharsets.UTF_8;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class ImportExercisesData {

  private static final Splitter tsvSplitter
    = Splitter.on("\t").trimResults();

  private final Map<String, String> env;
  private final Connection conn;
  private final String importFile = "../Data for ViFit - Exercises.tsv";

  private int categoryIdRef = 1; // (!) 1-based
  private int exerciseIdRef = 1; // (!) 1-based
  private int modIdRef = 1;      // (!) 1-based

  // key = name, value = id
  private final Map<String, String> categoryMap = new HashMap<>();
  private final Map<String, String> exerciseMap = new HashMap<>();
  private final Map<String, String> modMap      = new HashMap<>();

  private ImportExercisesData() throws Exception {
    this.env = readEnv();
    this.conn = getConnection();
  }

  private void execute() throws Exception {
    truncateExercisesTable();
    try (
      var reader = Files.newBufferedReader(Paths.get(importFile), ISO_8859_1);
    ) {
      var previous = new AtomicReference<Row>();
      reader.lines()
        .skip(1)
        .filter(StringUtils::isNotBlank)
        .forEach(line -> handleLine(line, previous));
    }
    out.println("Done!");
  }

  private void handleLine(String line, AtomicReference<Row> previous) {
    var tokens = tsvSplitter.splitToList(line);
    checkState(tokens.size() == 12,
      "Expected 12 tokens, got %s", tokens.size());

    int idx = 0;
    var no          = tokens.get(idx++).trim();
    var category    = tokens.get(idx++).trim();
    var exercise    = tokens.get(idx++).trim();
    var startingPos = tokens.get(idx++).trim();
    var actionType  = tokens.get(idx++).trim();
    var actionName  = tokens.get(idx++).trim();
    var repsMin     = tokens.get(idx++).trim();
    var repsMax     = tokens.get(idx++).trim();
    var focus       = tokens.get(idx++).trim();
    var essence     = tokens.get(idx++).trim();
    var mod         = tokens.get(idx++).trim();
    var modDesc     = tokens.get(idx++).trim();

    category = isBlank(category) ? previous.get().category() : category;
    final String categoryId;
    if (categoryMap.containsKey(category)) {
      categoryId = categoryMap.get(category);
    } else {
      categoryId = String.valueOf(categoryIdRef++);
      categoryMap.put(category, categoryId);
    }

    exercise = isBlank(exercise) ? previous.get().exercise() : exercise;
    var catId_Exercise = categoryId + "-" + exercise;
    final String exerciseId;
    if (exerciseMap.containsKey(catId_Exercise)) {
      exerciseId = exerciseMap.get(catId_Exercise);
    } else {
      exerciseId = String.valueOf(exerciseIdRef++);
      exerciseMap.put(catId_Exercise, exerciseId);
    }

    var modId = isNotBlank(mod) ? String.valueOf(modIdRef++) : null;
    /*
    mod = isBlank(mod) ? previous.get().mod() : mod;
    var modId = String.valueOf(modIdRef++);
    */
    /*
    final String modId;
    if (modMap.containsKey(mod)) {
      modId = modMap.get(mod);
    } else {
      modId = String.valueOf(modIdRef++);
      modMap.put(mod, modId);
    }
    */

    var row = new Row(
      isBlank(no) ? previous.get().no() : no,
      categoryId,
      category,
      exerciseId,
      exercise,
      catId_Exercise,
      isBlank(startingPos) ? previous.get().startingPos() : startingPos,
      actionType,
      actionName,
      repsMin,
      repsMax,
      focus,
      essence,
      modId,
      mod,
      modDesc
    );
    try {
      insertRow(row);
      previous.set(row);
    } catch (SQLException e) {
      throw new RuntimeException(e);
    }
  }

  private void truncateExercisesTable() throws SQLException {
    var sql = "truncate app04exercises";
    var ps = conn.prepareStatement(sql);
    ps.execute();
    ps.close();
    out.println(sql);
  }

  private void insertRow(Row row) throws SQLException {
    var sql = """
      insert into app04exercises (
        no, category, name, start_pos, action_type, action_name,
        reps_min, reps_max, focus, essence, mod_name, mod_descr,
        category_id, exercise_id, mod_id,
        created_by) values (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?, ?
        )""";
    var ps = conn.prepareStatement(sql);
    int args = 1;
    ps.setObject(args++, row.no());
    ps.setObject(args++, row.category());
    ps.setObject(args++, row.exercise());
    ps.setObject(args++, row.startingPos());
    ps.setObject(args++, row.actionType());
    ps.setObject(args++, row.actionName());
    ps.setObject(args++, isBlank(row.repsMin()) ? null : row.repsMin());
    ps.setObject(args++, isBlank(row.repsMax()) ? null : row.repsMax());
    ps.setObject(args++, row.focus());
    ps.setObject(args++, row.essence());
    ps.setObject(args++, row.mod());
    ps.setObject(args++, row.modDescr());
    ps.setObject(args++, row.categoryId());
    ps.setObject(args++, row.exerciseId());
    ps.setObject(args++, row.modId());
    ps.setObject(args++, importFile);
    ps.execute();
    ps.close();
  }

  private Connection getConnection() throws SQLException {
    return DriverManager.getConnection(
      "jdbc:mysql://%s:%s/%s".formatted(
        env.get("db_host"),
        env.get("db_port"),
        env.get("db_name")
      ),
      env.get("db_username"),
      env.get("db_password")
    );
  }

  private Map<String, String> readEnv() throws IOException {
    try (
      var reader = Files.newBufferedReader(Paths.get(".env"), UTF_8);
    ) {
      var props = new Properties();
      props.load(reader);
      return Maps.newHashMap(Maps.fromProperties(props));
    }
  }

  public static void main(String[] args) throws Exception {
    new ImportExercisesData().execute();
  }
}

record Row(
  String no,
  String categoryId,
  String category,
  String exerciseId,
  String exercise,
  String catId_Exercise,
  String startingPos,
  String actionType,
  String actionName,
  String repsMin,
  String repsMax,
  String focus,
  String essence,
  String modId,
  String mod,
  String modDescr
) { }
