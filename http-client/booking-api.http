@apiServer = http://localhost:8888
@apiPrefix = {{apiServer}}/api/booking
@authToken = ***

### @name makeBooking
POST {{apiPrefix}}/make-booking
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "date": "2024-12-21",
  "time": "20:00",
  "equipmentId": "e01",
  "instructorId": "instr02"
}

### @name bookings
GET {{apiPrefix}}/bookings/1/10
  ?upcomingOnly=true
Authorization: Bearer {{authToken}}

### @name bookingById
GET {{apiPrefix}}/bookings/get/4500edcb-37a0-497d-85d8-888e20e7e3b9
Authorization: Bearer {{authToken}}

### @name exerciseCategories
GET {{apiPrefix}}/exercises/categories
Authorization: Bearer {{authToken}}

### @name exercisesByCategoryId
GET {{apiPrefix}}/exercises/byCategoryId/4
Authorization: Bearer {{authToken}}

### @name exerciseModsByExerciseId
GET {{apiPrefix}}/exercises/modsByExerciseId/11
Authorization: Bearer {{authToken}}

### @name sessions
GET {{apiPrefix}}/sessions/1/10
  ?upcomingOnly=true
Authorization: Bearer {{authToken}}

### @name sessionById
GET {{apiPrefix}}/sessions/get/4500edcb-37a0-497d-85d8-888e20e7e3b9
  ?upcomingOnly=true
Authorization: Bearer {{authToken}}

### @name search
GET {{apiPrefix}}/search
  ?lat=1.3178811186437729
  &lng=103.89322706943177
  &distance=40
  &date=2025-01-03
  &time=14:00

### @name searchEquipment
GET {{apiPrefix}}/search/equipment
  ?lat=1.3178811186437729
  &lng=103.89322706943177
  &distance=40
  &date=2025-01-03
  &time=14:00
  &studioId=01

### @name searchEquipmentChoose
GET {{apiPrefix}}/search/equipment/choose
  ?lat=1.3178811186437729
  &lng=103.89322706943177
  &distance=40
  &date=2025-01-03
  &time=17:00
  &studioId=01
  &instrId=01
  &equipId=EQ-01a

### @name hitpayNotify
POST {{apiPrefix}}/hitpay-notify
Content-Type: application/x-www-form-urlencoded

payment_id=92965a2d-ece3-4ace-1245-494050c9a3c1
  &payment_request_id=92965a20-dae5-4d89-a452-5fdfa382dbe1
  &reference_number=ABC123
  &phone=
  &amount=599.00
  &currency=SGD
  &status=completed
  &hmac=330c34a6a8fb9ddb75833620dedb94bf4d4c2e51399d346cbc2b08c381a1399c

### @name equipmentTypes
GET {{apiPrefix}}/equipment-types
Content-Type: application/x-www-form-urlencoded
