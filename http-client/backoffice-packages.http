### Backoffice Credit Packages API Tests

### Variables
@baseUrl = http://localhost:3000
@authToken = your-firebase-auth-token-here

### 1. List Credit Packages
GET {{baseUrl}}/cms/backoffice/packages/list/1/10
Authorization: Bearer {{authToken}}

### 2. List Credit Packages with filters
GET {{baseUrl}}/cms/backoffice/packages/list/1/10?currency=SGD&status=true&sort=sequence&sortDir=asc
Authorization: Bearer {{authToken}}

### 3. Get Package by ID
GET {{baseUrl}}/cms/backoffice/packages/get/package-id-here
Authorization: Bearer {{authToken}}

### 4. Create New Package
POST {{baseUrl}}/cms/backoffice/packages/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "name": "Premium Package",
  "currency": "SGD",
  "price": 150.00,
  "credits": 150,
  "bonusCredits": 25,
  "status": true,
  "firstTimeOnly": false,
  "instructorOnly": false,
  "validFrom": "2024-01-01T00:00:00Z",
  "validTo": "2024-12-31T23:59:59Z",
  "purchaseLimit": 5,
  "sequence": 1
}

### 5. Update Package
POST {{baseUrl}}/cms/backoffice/packages/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "id": "package-id-here",
  "name": "Updated Premium Package",
  "currency": "SGD",
  "price": 175.00,
  "credits": 175,
  "bonusCredits": 30,
  "status": true,
  "firstTimeOnly": false,
  "instructorOnly": false,
  "validFrom": "2024-01-01T00:00:00Z",
  "validTo": "2024-12-31T23:59:59Z",
  "purchaseLimit": 10,
  "sequence": 1
}

### 6. Set Package Status (Activate)
POST {{baseUrl}}/cms/backoffice/packages/setStatus
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "packageId": "package-id-here",
  "status": true
}

### 7. Set Package Status (Deactivate)
POST {{baseUrl}}/cms/backoffice/packages/setStatus
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "packageId": "package-id-here",
  "status": false
}

### 8. Move Package Sequence Up
POST {{baseUrl}}/cms/backoffice/packages/moveSequence
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "packageId": "package-id-here",
  "direction": "up"
}

### 9. Move Package Sequence Down
POST {{baseUrl}}/cms/backoffice/packages/moveSequence
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "packageId": "package-id-here",
  "direction": "down"
}

### 10. Reorder Sequences for Currency
POST {{baseUrl}}/cms/backoffice/packages/reorderSequences/SGD
Authorization: Bearer {{authToken}}

### Example Package Data for Testing

### Basic Package
{
  "name": "Basic Package",
  "currency": "SGD",
  "price": 50.00,
  "credits": 50,
  "bonusCredits": 0,
  "status": true,
  "firstTimeOnly": false,
  "instructorOnly": false
}

### First-Time Only Package
{
  "name": "Welcome Package",
  "currency": "SGD",
  "price": 25.00,
  "credits": 50,
  "bonusCredits": 10,
  "status": true,
  "firstTimeOnly": true,
  "instructorOnly": false,
  "purchaseLimit": 1
}

### Instructor Only Package
{
  "name": "Instructor Special",
  "currency": "SGD",
  "price": 200.00,
  "credits": 250,
  "bonusCredits": 50,
  "status": true,
  "firstTimeOnly": false,
  "instructorOnly": true
}

### Limited Time Package
{
  "name": "Holiday Special",
  "currency": "SGD",
  "price": 100.00,
  "credits": 120,
  "bonusCredits": 20,
  "status": true,
  "firstTimeOnly": false,
  "instructorOnly": false,
  "validFrom": "2024-12-01T00:00:00Z",
  "validTo": "2024-12-31T23:59:59Z"
} 