@apiServer = http://localhost:8888
@apiPrefix = {{apiServer}}/cms/studio
@authToken = ***

### @name accountStatus
GET {{apiPrefix}}/account/status
Authorization: Bearer {{authToken}}

### @name register
POST {{apiPrefix}}/register
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "mainUser": {
    "name": "<PERSON>",
    "contactNo": "********",
    "jobTitle": "Studio Owner"
  },
  "studio": {
    "name": "Studio Alpha",
    "descr": "Description for Studio Alpha",
    "contactNo": "********",
    "email": "<EMAIL>",
    "address": "80 Marine Parade Rd, Singapore 449269",
    "placeId": "ChIJ2WUXmnEY2jERoGOSkJVgnrY",
    "usagePolicies": "Our usage policies",
    "pictures": [
      "https://www.google.com/01.png",
      "https://www.google.com/02.png"
    ],
    "socialMedia": {
      "facebook": "facebook-alpha",
      "instagram": "instagram-alpha"
    }
  }
}

### @name myProfile
GET {{apiPrefix}}/myProfile
Authorization: Bearer {{authToken}}

### @name myProfileSave
POST {{apiPrefix}}/myProfile/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "name": "Jane Doe (u)",
  "contactNo": "******** (u)",
  "jobTitle": "Studio Owner (u)"
}

### @name myStudio
GET {{apiPrefix}}/myStudio
Authorization: Bearer {{authToken}}

### @name myStudio
POST {{apiPrefix}}/myStudio/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "name": "Studio Alpha (u)",
  "descr": "Description for Studio Alpha (u)",
  "contactNo": "******** (u)",
  "email": "<EMAIL> (u)",
  "address": "80 Marine Parade Rd, Singapore 449269 (u)",
  "placeId": "ChIJ2WUXmnEY2jERoGOSkJVgnrY (u)",
  "usagePolicies": "Our usage policies (u)",
  "pictures": [
    "https://www.google.com/11.png",
    "https://www.google.com/12.png"
  ],
  "socialMedia": {
    "facebook": "facebook-alpha (u)",
    "instagram": "instagram-alpha (u)"
  }
}

### @name myStudio
### {{apiPrefix}}/myStudio/picture/get/{{pictureId}}
POST {{apiPrefix}}/myStudio/picture/upload
Authorization: Bearer {{authToken}}
Content-Type: multipart/form-data; boundary=abcde12345

--abcde12345
Content-Disposition: form-data; name="file"; filename="google_PNG19644.png"
Content-Type: image/png

< ./cmpl-logo.png
--abcde12345--

### @name userList
GET {{apiPrefix}}/users/list/1/10
  ?inclDisabled=true
Authorization: Bearer {{authToken}}

### @name userGetById
GET {{apiPrefix}}/users/get/zzz
Authorization: Bearer {{authToken}}

### @name userSave (new)
POST {{apiPrefix}}/users/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "name": "John Doe",
  "contactNo": "99999999",
  "jobTitle": "Studio Manager",
  "isAdmin": false
}

### @name userSave (existing)
POST {{apiPrefix}}/users/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "id": "@<EMAIL>",
  "email": "<EMAIL>",
  "name": "John Doe (u)",
  "contactNo": "99999999 (u)",
  "jobTitle": "Studio Manager (u)",
  "isAdmin": false,
  "isDisabled": true
}

### @name userSetDisabled = false (ie, active = true)
GET {{apiPrefix}}/users/setDisabled/@<EMAIL>/false
Authorization: Bearer {{authToken}}

### @name userSetDisabled = true (ie, active = false)
GET {{apiPrefix}}/users/setDisabled/@<EMAIL>/true
Authorization: Bearer {{authToken}}

### @name equipmentTypes
GET {{apiPrefix}}/equipment/types
Authorization: Bearer {{authToken}}

### @name equipmentAll
GET {{apiPrefix}}/equipment/all
Authorization: Bearer {{authToken}}

### @name equipmentList
GET {{apiPrefix}}/equipment/list/1/10
  ?inclDisabled=false
  &typeId=t02
  &availableNow=true
  &searchTerm=corner
Authorization: Bearer {{authToken}}

### @name equipmentGetById (non existent)
GET {{apiPrefix}}/equipment/get/zzz
Authorization: Bearer {{authToken}}

### @name equipmentGetById (e01)
GET {{apiPrefix}}/equipment/get/e01
Authorization: Bearer {{authToken}}

### @name equipmentGetById (e02)
GET {{apiPrefix}}/equipment/get/e02
Authorization: Bearer {{authToken}}

### @name equipmentSave (new)
POST {{apiPrefix}}/equipment/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "type": {
    "id": "t01",
    "name": "Reformer"
  },
  "color": "#000999",
  "code": "TEST-01",
  "shortName": "TEST 01 (short)",
  "name": "TEST 01 (name)",
  "descr": "TEST 01 (descr)",
  "startDate": "2024-01-01",
  "endDate": "2042-12-31",
  "availability": [{
    "type": "PH", "slots": [
      {"start": "09:30", "end": "18:30", "price": "100.50"},
      {"start": "18:30", "end": "21:30", "price": "200.50"}
    ]
  }, {
    "type": "WD", "slots": [
      {"start": "09:30", "end": "18:30", "price": "100.50"},
      {"start": "18:30", "end": "21:30", "price": "200.50"}
    ]
  }, {
    "type": "WE", "slots": [
      {"start": "09:30", "end": "18:30", "price": "150.50"},
      {"start": "18:30", "end": "21:30", "price": "250.50"}
    ]
  }]
}

### @name equipmentSave (existing)
POST {{apiPrefix}}/equipment/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "id": "e01",
  "type": {
    "id": "t01",
    "name": "Reformer"
  },
  "color": "#999000",
  "code": "TEST-01 (u)",
  "shortName": "TEST 01 (short)(u)",
  "name": "TEST 01 (name)(u)",
  "descr": "TEST 01 (descr)(u)",
  "startDate": null,
  "endDate": null,
  "availability": [{
    "type": "ALL", "slots": [
      {"start": "09:30", "end": "18:30", "price": "100.50"},
      {"start": "18:30", "end": "21:30", "price": "200.50"}
    ]
  }]
}

### @name equipmentSetDeleted (deleted = true)
GET {{apiPrefix}}/equipment/set-deleted/e01/true
Authorization: Bearer {{authToken}}

### @name equipmentSetDeleted (deleted = false)
GET {{apiPrefix}}/equipment/set-deleted/e01/false
Authorization: Bearer {{authToken}}

### @name equipmentAvailabilityGet (e01)
GET {{apiPrefix}}/equipment/e01/getAvailability
Authorization: Bearer {{authToken}}

### @name equipmentAvailabilityGet (e02)
GET {{apiPrefix}}/equipment/e02/getAvailability
Authorization: Bearer {{authToken}}

### @name equipmentAvailabilitySave (e02)
POST {{apiPrefix}}/equipment/e02/saveAvailability
Authorization: Bearer {{authToken}}
Content-Type: application/json

[{
  "type": "PH", "slots": [
    {"start": "09:30", "end": "18:30", "price": "100.50"},
    {"start": "18:30", "end": "21:30", "price": "200.50"}
  ]
}, {
  "type": "WD", "slots": [
    {"start": "09:30", "end": "18:30", "price": "100.50"},
    {"start": "18:30", "end": "21:30", "price": "200.50"}
  ]
}, {
  "type": "WE", "slots": [
    {"start": "09:30", "end": "18:30", "price": "150.50"},
    {"start": "18:30", "end": "21:30", "price": "250.50"}
  ]
}]

### @name schedule
### equipmentId
### inclCancelled
### searchTerm
GET {{apiPrefix}}/schedule
  ?start=2024-12-01
  &end=2024-12-31
Authorization: Bearer {{authToken}}

### @name schedule (inclCancelled=true)
GET {{apiPrefix}}/schedule
  ?start=2024-12-01
  &end=2024-12-31
  &inclCancelled=true
Authorization: Bearer {{authToken}}

### @name eventGetById
GET {{apiPrefix}}/schedule/getEvent/evt01
Authorization: Bearer {{authToken}}

### @name eventSave (new)
POST {{apiPrefix}}/schedule/saveEvent
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "type": "StudioBlocked",
  "name": "Test StudioBlocked event",
  "internalRemarks": "Remarks for StudioBlocked",
  "startDate": "2024-12-20",
  "startTime": "12:00",
  "endDate": "2024-12-20",
  "endTime": "14:00",
  "isFullDay": false,
  "color": "@aaa000",
  "equipmentId": "na"
}

### @name eventSave (existing)
POST {{apiPrefix}}/schedule/saveEvent
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "id": "evt11",
  "type": "StudioBlocked",
  "name": "Aircon Maintenance (u)",
  "internalRemarks": "Remarks for Aircon Maintenance (u)",
  "startDate": "2024-12-03",
  "startTime": "14:00",
  "endDate": "2024-12-03",
  "endTime": "17:00",
  "isFullDay": false,
  "color": "@bbb000",
  "equipmentId": "na"
}

### @name eventCancel
POST {{apiPrefix}}/schedule/cancelEvent/evt11
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "reason": "Cancel test event"
}
