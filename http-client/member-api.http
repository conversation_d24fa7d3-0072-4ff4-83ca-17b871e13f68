@apiServer = http://localhost:8888
@apiPrefix = {{apiServer}}/api/member
@authToken = ***

### @name exists
GET {{apiPrefix}}/exists
Authorization: Bearer {{authToken}}

### @name register
POST {{apiPrefix}}/register
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "fullName": "Jane Doe",
  "displayName": "Jane<PERSON>",
  "gender": "RatherNotSay",
  "mobileNo": {
    "countryCode": "+65",
    "number": "********"
  },
  "emergencyContact": {
    "name": "<PERSON>",
    "contactNo": {
      "countryCode": "+65",
      "number": "********"
    }
  }
}

### @name myAccount
GET {{apiPrefix}}/myAccount
Authorization: Bearer {{authToken}}
Content-Type: application/json

### @name myAccountSave
POST {{apiPrefix}}/myAccount/save
Authorization: Bear<PERSON> {{authToken}}
Content-Type: application/json

{
  "fullName": "<PERSON> (u)",
  "displayName": "Jane<PERSON> (u)",
  "gender": "Female",
  "mobileNo": {
    "countryCode": "+1",
    "number": "********"
  },
  "emergencyContact": {
    "name": "John Doe (u)",
    "contactNo": {
      "countryCode": "+1",
      "number": "********"
    }
  }
}

#############
### Faves ###
#############

### @name faveStudioAdd
GET {{apiPrefix}}/faveStudios/add/stu001
Authorization: Bearer {{authToken}}

### @name faveStudioRemove
GET {{apiPrefix}}/faveStudios/remove/stu001
Authorization: Bearer {{authToken}}

### @name faveStudioHas
GET {{apiPrefix}}/faveStudios/has/stu001
Authorization: Bearer {{authToken}}

### @name faveStudios
GET {{apiPrefix}}/faveStudios
Authorization: Bearer {{authToken}}

### @name faveInstructorAdd
GET {{apiPrefix}}/faveInstructors/add/instr02
Authorization: Bearer {{authToken}}

### @name faveInstructorRemove
GET {{apiPrefix}}/faveInstructors/remove/instr02
Authorization: Bearer {{authToken}}

### @name faveInstructorHas
GET {{apiPrefix}}/faveInstructors/has/instr02
Authorization: Bearer {{authToken}}

### @name faveInstructors
GET {{apiPrefix}}/faveInstructors
Authorization: Bearer {{authToken}}

###########
### FCM ###
###########

### @name fcmTokenSet
POST {{apiPrefix}}/fcmToken/set
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "token": "xxx"
}
