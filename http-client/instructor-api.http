@apiServer = http://localhost:8888
@apiPrefix = {{apiServer}}/api/instructor
@authToken = ***

### @name exists
GET {{apiPrefix}}/exists
Authorization: Bearer {{authToken}}

### @name register
POST {{apiPrefix}}/register
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "name": "<PERSON><PERSON><PERSON>",
  "descr": "Description for G.I. Doe",
  "facebook": "fb-gidoe",
  "instagram": "insta-gidoe",
  "certifications": "Intl Pilates Instructor",
  "specialisations": "Reformer training"
}

### @name myProfile
GET {{apiPrefix}}/myProfile
Authorization: Bearer {{authToken}}
Content-Type: application/json

### @name myProfileSave
POST {{apiPrefix}}/myProfile/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "name": "<PERSON><PERSON><PERSON> (u)",
  "descr": "Description for <PERSON><PERSON><PERSON><PERSON> (u)",
  "facebook": "fb-gidoe-u",
  "instagram": "insta-gidoe-u",
  "certifications": "Intl Pilates Instructor (u)",
  "specialisations": "Reformer training (u)"
}

#############
### Rates ###
#############

### @name rates
GET {{apiPrefix}}/rates
Authorization: Bearer {{authToken}}

### @name ratesSave
POST {{apiPrefix}}/rates/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

[{
  "type": "PH", "rates": [
    {"start": "09:30", "end": "18:30", "price": "100.50"},
    {"start": "18:30", "end": "21:30", "price": "200.50"}
  ]
}, {
  "type": "WD", "rates": [
    {"start": "09:30", "end": "18:30", "price": "100.50"},
    {"start": "18:30", "end": "21:30", "price": "200.50"}
  ]
}, {
  "type": "WE", "rates": [
    {"start": "09:30", "end": "18:30", "price": "150.50"},
    {"start": "18:30", "end": "21:30", "price": "250.50"}
  ]
}]

##################
### Work Hours ###
##################

### @name workHours
GET {{apiPrefix}}/work-hours
Authorization: Bearer {{authToken}}

### @name workHoursSave
POST {{apiPrefix}}/work-hours/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

[{
  "type": "PH", "slots": [
    {"start": "09:30", "end": "18:30"},
    {"start": "18:30", "end": "21:30"}
  ]
}, {
  "type": "WD", "slots": [
    {"start": "09:30", "end": "18:30"},
    {"start": "18:30", "end": "21:30"}
  ]
}, {
  "type": "WE", "slots": [
    {"start": "09:30", "end": "18:30"},
    {"start": "18:30", "end": "21:30"}
  ]
}]

################
### Mobility ###
################

### @name mobility
GET {{apiPrefix}}/mobility
Authorization: Bearer {{authToken}}

### @name mobilitySave
POST {{apiPrefix}}/mobility/save
Authorization: Bearer {{authToken}}
Content-Type: application/json

[
  { "dow": 1, "locationId": "loc2", "distance": 10 },
  { "dow": 2, "locationId": "loc2", "distance": 10 },
  { "dow": 3, "locationId": "loc2", "distance": 10 },
  { "dow": 4, "locationId": "loc2", "distance": 10 },
  { "dow": 5, "locationId": "loc2", "distance": 10 },
  { "dow": 6, "locationId": "loc2", "distance": 10 },
  { "dow": 7, "locationId": "loc2", "distance": 10 }
]

#################
### Blackouts ###
#################

### @name blackoutsCalendar
GET {{apiPrefix}}/blackouts/calendar
  ?year=2024
  &month=12
Authorization: Bearer {{authToken}}

### @name blackoutsByDay
GET {{apiPrefix}}/blackouts/byDay
  ?day=2024-12-20
Authorization: Bearer {{authToken}}

#########################
### Preferred Studios ###
#########################

### @name prefStudioAdd
GET {{apiPrefix}}/prefStudios/add/stu001
Authorization: Bearer {{authToken}}

### @name prefStudioRemove
GET {{apiPrefix}}/prefStudios/remove/stu001
Authorization: Bearer {{authToken}}

### @name prefStudioHas
GET {{apiPrefix}}/prefStudios/has/stu001
Authorization: Bearer {{authToken}}

### @name prefStudios
GET {{apiPrefix}}/prefStudios
Authorization: Bearer {{authToken}}
