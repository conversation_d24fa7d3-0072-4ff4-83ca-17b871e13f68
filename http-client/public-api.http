### (!) These APIs are public, no need bearer token.
@apiServer = http://localhost:8888
@apiPrefix = {{apiServer}}/api/public

### @name studios
GET {{apiPrefix}}/studios/1/10

### @name studioById
GET {{apiPrefix}}/studios/get/stu001

### @name studioById (204)
GET {{apiPrefix}}/studios/get/zzz

### @name instructors
GET {{apiPrefix}}/instructors/1/10

### @name instructorById
GET {{apiPrefix}}/instructors/get/instr02

### @name instructorById (204)
GET {{apiPrefix}}/instructors/get/zzz

### @name messageByCode (204)
GET {{apiPrefix}}/messageByCode
  ?code=zzz

### @name messageByCode
GET {{apiPrefix}}/messageByCode
  ?code=hello.world

### @name locationAutocomplete
GET {{apiPrefix}}/location/autocomplete
  ?input=parkway

### @name locationReverseGeocode
GET {{apiPrefix}}/location/reverseGeocode
  ?lat=1.3071625
  &lng=103.9065279

### @name locationToPlace
GET {{apiPrefix}}/location/toPlace
  ?placeId=ChIJoUytPCgZ2jERyOSAJNIpYww
