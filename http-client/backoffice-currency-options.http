### Get All Countries
GET {{BO_API_URL}}/cms/backoffice/options/countries
Authorization: Bearer {{authToken}}
Content-Type: application/json

### Get Currency Options
GET {{BO_API_URL}}/cms/backoffice/options/currencies
Authorization: Bearer {{authToken}}
Content-Type: application/json

### Expected Countries Response:
# {
#   "success": true,
#   "data": [
#     {
#       "code": "SG",
#       "name": "Singapore",
#       "timezone": "Asia/Singapore",
#       "currency": "SGD",
#       "currencyName": "Singapore Dollar",
#       "symbol": "$"
#     },
#     {
#       "code": "US",
#       "name": "United States",
#       "timezone": "America/New_York",
#       "currency": "USD",
#       "currencyName": "US Dollar",
#       "symbol": "$"
#     }
#   ]
# }

### Expected Currency Options Response:
# {
#   "success": true,
#   "data": [
#     {
#       "code": "SGD",
#       "name": "Singapore Dollar",
#       "symbol": "$",
#       "countryCode": "SG",
#       "countryName": "Singapore"
#     },
#     {
#       "code": "USD",
#       "name": "US Dollar",
#       "symbol": "$",
#       "countryCode": "US",
#       "countryName": "United States"
#     },
#     {
#       "code": "MYR",
#       "name": "Malaysian Ringgit",
#       "symbol": "RM",
#       "countryCode": "MY",
#       "countryName": "Malaysia"
#     },
#     {
#       "code": "GBP",
#       "name": "Pound Sterling",
#       "symbol": "£",
#       "countryCode": "GB",
#       "countryName": "United Kingdom"
#     },
#     {
#       "code": "JPY",
#       "name": "Yen",
#       "symbol": "¥",
#       "countryCode": "JP",
#       "countryName": "Japan"
#     }
#   ]
# } 