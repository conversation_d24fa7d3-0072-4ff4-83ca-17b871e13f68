import logger from "@utils/logger";
import mysql, { PoolOptions } from "mysql2/promise";

const pool = mysql.createPool({
  connectionLimit : process.env.DATABASE_POOL_CONNECTION_LIMIT || 10,
  host            : process.env.DATABASE_HOST,
  database        : process.env.DATABASE_NAME,
  user            : process.env.DATABASE_USERNAME,
  password        : process.env.DATABASE_PASSWORD,
  timezone        : process.env.DATABASE_TIMEZONE,
  dateStrings     : true,
} as PoolOptions);

logger.debug(`database pool initialised with timezone: ${process.env.DATABASE_TIMEZONE}`);

export const closePool = async (): Promise<void> => {
  try {
    await pool.end();
    logger.info('Database pool closed successfully');
  } catch (error) {
    logger.error('Error closing database pool:', error);
  }
};

export default pool;
