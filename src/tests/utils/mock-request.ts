import { Request } from "express";
import { auth } from "firebase-admin";
import DecodedIdToken = auth.DecodedIdToken;

class MockRequest {
    body: any;
    headers: { [key: string]: string };
    method: string;
    originalUrl: string;
    params: { [key: string]: any };
    query: { [key: string]: any };
    idToken: any; // Adjust the type as necessary

    constructor(idToken: DecodedIdToken, method: string = "GET", originalUrl: string = "/", body: any = {}, params: any = {}, query: any = {}) {
        this.body = {...body};
        this.headers = {
            authorization: "Bearer fakers",
        };
        this.method = method;
        this.originalUrl = originalUrl;
        this.params = {...params};
        this.query = {...query};
        this.idToken = idToken; // Assign the idToken
    }
}

// Export the mock request
export const mockRequest = (idToken: DecodedIdToken, method: string = "GET", originalUrl: string = "/", body: any = {}, params: any = {}, query: any = {}): Request => {
    return new MockRequest(idToken, method, originalUrl, body, params, query) as unknown as Request;
};