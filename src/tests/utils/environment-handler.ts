import logger from "@utils/logger"; // Adjust the import path as necessary

export const environmentHandle = (describeCallback: () => void, seedingCallback?: () => void) => {
    if (process.env.JEST_WORKER_ID) {
        logger.info("Running in Jest environment");
        describeCallback();
    } else if (process.env.NODE_ENV === 'ts-node') {
        logger.info("Running in ts-node environment");
        if (seedingCallback) {
            seedingCallback(); // Call the register function if provided
        }
    } else {
        logger.info("Running in a different environment");
    }
};