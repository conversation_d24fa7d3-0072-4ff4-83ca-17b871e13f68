import { Request, Response } from "express";

export class MockResponse {
    statusCode: number;
    private response: any;

    constructor() {
        this.statusCode = 200; // Default status code
        this.response = {};
    }

    status(code: number) {
        this.statusCode = code;
        return this; // Allow chaining
    }

    json(data: any) {
        this.response = data;
        return this; // Allow chaining
    }

    send(data?: any) {
        this.response = data;
        return this; // Allow chaining
    }

    end() {
        // Optional: Add any additional logic here if needed
        return this; // Allow chaining
    }

    getResponse() {
        return {
            statusCode: this.statusCode,
            body: this.response,
        };
    }
}

// Export the mock response
export const mockResponse = (): Response => new MockResponse() as unknown as Response;