import 'dotenv/config';
import { Request, Response } from "express";
import { mockResponse, MockResponse } from "../utils/mock-response";
import { mockRequest } from "../utils/mock-request";
import { environmentHandle } from "../utils/environment-handler";
import register from "@instructor-api/routes/register";
import pool from "../../init-pool";
import logger from "@utils/logger";
import { faker } from "@faker-js/faker";
import { auth } from "firebase-admin";
import DecodedIdToken = auth.DecodedIdToken;

async function registerInstructor() {
    const uid = faker.string.uuid();
    const equipmentTypes = ["t01", "t02", "t03", "t04", "t05", "t06", "t07", "t08", "t09"];
    const randomEquipmentTypes = getRandomItems(equipmentTypes, Math.floor(Math.random() * (9 - 1 + 1)) + 1); // Pick random equipment types
    const userData = {
        name: faker.person.fullName(),
        descr: faker.person.bio(),
        facebook: faker.internet.url(),
        instagram: faker.internet.url(),
        certifications: faker.lorem.words({ min: 5, max: 10 }),
        specialisations: faker.lorem.words({ min: 5, max: 20 }),
        pictures: [],
        equipmentTypes: randomEquipmentTypes,
    };

    const req = mockRequest({
        uid: uid,
        email: faker.internet.exampleEmail(),
        email_verified: true,
    } as DecodedIdToken, "POST", "/api/instructor/register", userData);

    const res = mockResponse();
    await register(req, res, (error) => {
      logger.error(`request error: ${JSON.stringify(error)}`);
    });
    return res; // Return the response
}

function getRandomItems<T>(array: T[], n: number): T[] {
    // Shuffle the array
    const shuffled = array.sort(() => 0.5 - Math.random());
    // Return the first n items from the shuffled array
    return shuffled.slice(0, n);
}

// batch to register instructors
async function registerInstructors() {
    const totalRecords = 2; // Total number of records to register
    const batchSize = 1; // Number of records to register in each batch

    for (let i = 0; i < totalRecords; i += batchSize) {
        const promises: Promise<Response>[] = [];

        // Calculate the number of records to register in this batch
        const currentBatchSize = Math.min(batchSize, totalRecords - i);

        for (let j = 0; j < currentBatchSize; j++) {
            promises.push(registerInstructor()); // Call the function
        }

        const responses = await Promise.all(promises);
        logger.info(`Inserted ${responses.length} records...`);

        responses.forEach(res => {
          logger.info(`Response status code: ${res.statusCode}`);
        });
    }
    logger.info(`Finished inserting instructor registration.`);
    process.exit(0);
}

// Use the environment handler
environmentHandle(() => {
  describe("Instructor Registration", () => {
      it("should register an instructor successfully", async () => {
          const res = await registerInstructor() as unknown as MockResponse;
          expect(res.getResponse().statusCode).toBe(200);
      });

      afterAll(async () => {
          await pool.end(); // Close the pool after all tests
      });
  });
}, registerInstructors); // Pass the registerInstructors function
