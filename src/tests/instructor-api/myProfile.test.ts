import 'dotenv/config';
import { Request, Response } from "express";
import { mockResponse, MockResponse } from "../utils/mock-response";
import { mockRequest } from "../utils/mock-request";
import myAccount from "@instructor-api/routes/myProfile";
import pool from "../../init-pool";
import logger from "@utils/logger";
import { auth } from "firebase-admin";
import DecodedIdToken = auth.DecodedIdToken;
import { InstructorRepo } from "@instructor-api/repo/instructor-repo";

async function getMyProfile(decodedIdToken: DecodedIdToken) {
    const req = mockRequest(decodedIdToken, "GET", "/api/instructor/myProfile", {});
    const res = mockResponse();
    await myAccount(req, res, (error) => {
        logger.error(`request error: ${JSON.stringify(error)}`);
    });
    return res; // Return the response
}

describe("My Profile", () => {
    it("should return the instructor's profile successfully", async () => {
        const conn = await pool.getConnection();
        const instructorRepo = new InstructorRepo();

        const randomInstructor = await instructorRepo.getRandomInstructorByCreator(conn, "%@example%");
        expect(randomInstructor).toBeDefined();

        const res = await getMyProfile({
            uid: randomInstructor?.firebaseUid,
            email: randomInstructor?.createdBy,
            email_verified: true,
        } as DecodedIdToken) as unknown as MockResponse;

        expect(res.getResponse().statusCode).toBe(200);
        expect(res.getResponse().body).toEqual(expect.any(Object));
    });

    it("should return 204 if the instructor is not found", async () => {
        const res = await getMyProfile({
            uid: "non-existent-uid",
            email: "<EMAIL>",
            email_verified: true,
        } as DecodedIdToken) as unknown as MockResponse;
        expect(res.getResponse().statusCode).toBe(204);
    });

    afterAll(async () => {
        await pool.end();
    });
});