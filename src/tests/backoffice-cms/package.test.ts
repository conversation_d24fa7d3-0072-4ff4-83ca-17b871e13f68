import 'dotenv/config';
import { PackageService } from "@backoffice-cms/services/package-service";
import { Package, PackageSaveReq, PackageSetStatusReq, PackageMoveSequenceReq } from "@backoffice-cms/models/package";
import { By } from "@shared/models";
import { initSequelize, closeSequelize } from "../../init-sequelize";
import { closePool } from "../../init-pool";
import logger from "@utils/logger";
import { faker } from "@faker-js/faker";

describe('PackageService Integration Tests', () => {
  let packageService: PackageService;
  let testBy: By;
  let createdPackageIds: string[] = [];

  beforeAll(async () => {
    // Initialize Sequelize before running tests
    await initSequelize();
    packageService = new PackageService();
    testBy = {
      uid: faker.string.uuid(),
      username: faker.internet.email(),
    };
  });

  describe('Package CRUD Operations', () => {
    it('should create a basic package successfully', async () => {
      const testPackage: Package = {
        id: '',
        name: faker.commerce.productName(),
        currency: 'SGD',
        price: parseFloat(faker.commerce.price({ min: 10, max: 500 })),
        credits: parseInt(faker.number.int({ min: 10, max: 500 }).toString()),
        bonusCredits: parseInt(faker.number.int({ min: 0, max: 50 }).toString()),
        status: true,
        firstTimeOnly: false,
        instructorOnly: false,
        validFrom: null,
        validTo: null,
        purchaseLimit: null,
        sequence: null
      };

      const req: PackageSaveReq = {
        package: testPackage,
        by: testBy
      };

      const packageId = await packageService.create(req);
      expect(packageId).toBeDefined();
      expect(typeof packageId).toBe('string');
      createdPackageIds.push(packageId);
    });

    it('should create a package with all optional fields', async () => {
      const testPackage: Package = {
        id: '',
        name: 'Premium Package with All Features',
        currency: 'USD',
        price: 299.99,
        credits: 500,
        bonusCredits: 100,
        status: true,
        firstTimeOnly: true,
        instructorOnly: true,
        validFrom: '2024-01-01 00:00:00',
        validTo: '2024-12-31 23:59:59',
        purchaseLimit: 5,
        sequence: 10
      };

      const req: PackageSaveReq = {
        package: testPackage,
        by: testBy
      };

      const packageId = await packageService.create(req);
      expect(packageId).toBeDefined();
      expect(typeof packageId).toBe('string');
      createdPackageIds.push(packageId);

      // Verify all fields were saved correctly
      const savedPackage = await packageService.findById(packageId);
      expect(savedPackage).toBeDefined();
      expect(savedPackage?.name).toBe('Premium Package with All Features');
      expect(savedPackage?.currency).toBe('USD');
      expect(savedPackage?.price).toBe(299.99);
      expect(savedPackage?.credits).toBe(500);
      expect(savedPackage?.bonusCredits).toBe(100);
      expect(savedPackage?.status).toBe(true);
      expect(savedPackage?.firstTimeOnly).toBe(true);
      expect(savedPackage?.instructorOnly).toBe(true);
      expect(savedPackage?.validFrom).toBe('2024-01-01 00:00:00');
      expect(savedPackage?.validTo).toBe('2024-12-31 23:59:59');
      expect(savedPackage?.purchaseLimit).toBe(5);
      expect(savedPackage?.sequence).toBeGreaterThan(0); // Sequence is managed by the system
    });

    it('should create instructor-only package', async () => {
      const testPackage: Package = {
        id: '',
        name: 'Instructor Special Package',
        currency: 'SGD',
        price: 150,
        credits: 200,
        bonusCredits: 50,
        status: true,
        firstTimeOnly: false,
        instructorOnly: true,
        validFrom: null,
        validTo: null,
        purchaseLimit: null,
        sequence: null
      };

      const req: PackageSaveReq = {
        package: testPackage,
        by: testBy
      };

      const packageId = await packageService.create(req);
      expect(packageId).toBeDefined();
      createdPackageIds.push(packageId);

      // Verify instructor-only field
      const savedPackage = await packageService.findById(packageId);
      expect(savedPackage?.instructorOnly).toBe(true);
      expect(savedPackage?.firstTimeOnly).toBe(false);
    });

    it('should create first-time-only package', async () => {
      const testPackage: Package = {
        id: '',
        name: 'First Time User Package',
        currency: 'SGD',
        price: 50,
        credits: 100,
        bonusCredits: 25,
        status: true,
        firstTimeOnly: true,
        instructorOnly: false,
        validFrom: null,
        validTo: null,
        purchaseLimit: 1,
        sequence: null
      };

      const req: PackageSaveReq = {
        package: testPackage,
        by: testBy
      };

      const packageId = await packageService.create(req);
      expect(packageId).toBeDefined();
      createdPackageIds.push(packageId);

      // Verify first-time-only field
      const savedPackage = await packageService.findById(packageId);
      expect(savedPackage?.firstTimeOnly).toBe(true);
      expect(savedPackage?.instructorOnly).toBe(false);
      expect(savedPackage?.purchaseLimit).toBe(1);
    });

    it('should create package with validity period', async () => {
      const testPackage: Package = {
        id: '',
        name: 'Limited Time Package',
        currency: 'SGD',
        price: 200,
        credits: 300,
        bonusCredits: 75,
        status: true,
        firstTimeOnly: false,
        instructorOnly: false,
        validFrom: '2024-06-01 00:00:00',
        validTo: '2024-08-31 23:59:59',
        purchaseLimit: 3,
        sequence: null
      };

      const req: PackageSaveReq = {
        package: testPackage,
        by: testBy
      };

      const packageId = await packageService.create(req);
      expect(packageId).toBeDefined();
      createdPackageIds.push(packageId);

      // Verify validity period fields
      const savedPackage = await packageService.findById(packageId);
      expect(savedPackage?.validFrom).toBe('2024-06-01 00:00:00');
      expect(savedPackage?.validTo).toBe('2024-08-31 23:59:59');
      expect(savedPackage?.purchaseLimit).toBe(3);
    });

    it('should find created package by ID', async () => {
      if (createdPackageIds.length > 0) {
        const packageId = createdPackageIds[0];
        const found = await packageService.findById(packageId);
        expect(found).toBeDefined();
        expect(found?.id).toBe(packageId);
        expect(found?.name).toBeDefined();
        expect(found?.currency).toBe('SGD');
      }
    });

    it('should list packages with basic criteria', async () => {
      const results = await packageService.findAll({
        currency: 'SGD',
        status: true
      });

      expect(results).toBeDefined();
      expect(Array.isArray(results.items)).toBe(true);
      expect(typeof results.totalItems).toBe('number');
      expect(typeof results.totalPages).toBe('number');
      expect(results.totalItems).toBeGreaterThan(0);
    });

    it('should list instructor-only packages', async () => {
      const results = await packageService.findAll({
        currency: 'SGD',
        status: true
      });

      expect(results).toBeDefined();
      expect(Array.isArray(results.items)).toBe(true);
      
      // Filter instructor-only packages from results and verify they exist
      const instructorOnlyPackages = results.items.filter(pkg => pkg.instructorOnly === true);
      expect(instructorOnlyPackages.length).toBeGreaterThanOrEqual(0);
      
      // Verify all instructor-only packages have the correct flag
      instructorOnlyPackages.forEach(pkg => {
        expect(pkg.instructorOnly).toBe(true);
      });
    });

    it('should list first-time-only packages', async () => {
      const results = await packageService.findAll({
        currency: 'SGD',
        status: true
      });

      expect(results).toBeDefined();
      expect(Array.isArray(results.items)).toBe(true);
      
      // Filter first-time-only packages from results and verify they exist
      const firstTimeOnlyPackages = results.items.filter(pkg => pkg.firstTimeOnly === true);
      expect(firstTimeOnlyPackages.length).toBeGreaterThanOrEqual(0);
      
      // Verify all first-time-only packages have the correct flag
      firstTimeOnlyPackages.forEach(pkg => {
        expect(pkg.firstTimeOnly).toBe(true);
      });
    });

    it('should list packages with purchase limits', async () => {
      const results = await packageService.findAll({
        currency: 'SGD',
        status: true
      });

      expect(results).toBeDefined();
      expect(Array.isArray(results.items)).toBe(true);
      
      // Check that packages with purchase limits are properly returned
      const packagesWithLimits = results.items.filter(pkg => pkg.purchaseLimit !== null);
      expect(packagesWithLimits.length).toBeGreaterThanOrEqual(0);
    });

    it('should list packages with validity periods', async () => {
      const results = await packageService.findAll({
        currency: 'SGD',
        status: true
      });

      expect(results).toBeDefined();
      expect(Array.isArray(results.items)).toBe(true);
      
      // Check that packages with validity periods are properly returned
      const packagesWithValidity = results.items.filter(pkg => 
        pkg.validFrom !== null || pkg.validTo !== null
      );
      expect(packagesWithValidity.length).toBeGreaterThanOrEqual(0);
    });

    it('should update package successfully', async () => {
      if (createdPackageIds.length > 0) {
        const packageId = createdPackageIds[0];
        const originalPackage = await packageService.findById(packageId);
        expect(originalPackage).toBeDefined();

        const updateReq: PackageSaveReq = {
          package: {
            ...originalPackage!,
            name: 'Updated Package Name',
            price: 175.50
          },
          by: testBy
        };

        const affectedRows = await packageService.update(updateReq);
        expect(affectedRows).toBe(1);

        // Verify the update
        const updatedPackage = await packageService.findById(packageId);
        expect(updatedPackage?.name).toBe('Updated Package Name');
        expect(updatedPackage?.price).toBe(175.50);
      }
    });

    it('should update package with all optional fields', async () => {
      if (createdPackageIds.length > 0) {
        const packageId = createdPackageIds[0];
        const originalPackage = await packageService.findById(packageId);
        expect(originalPackage).toBeDefined();

        const updateReq: PackageSaveReq = {
          package: {
            ...originalPackage!,
            name: 'Completely Updated Package',
            firstTimeOnly: true,
            instructorOnly: true,
            validFrom: '2024-09-01 00:00:00',
            validTo: '2024-11-30 23:59:59',
            purchaseLimit: 10
          },
          by: testBy
        };

        const affectedRows = await packageService.update(updateReq);
        expect(affectedRows).toBe(1);

        // Verify all field updates
        const updatedPackage = await packageService.findById(packageId);
        expect(updatedPackage?.name).toBe('Completely Updated Package');
        expect(updatedPackage?.firstTimeOnly).toBe(true);
        expect(updatedPackage?.instructorOnly).toBe(true);
        expect(updatedPackage?.validFrom).toBe('2024-09-01 00:00:00');
        expect(updatedPackage?.validTo).toBe('2024-11-30 23:59:59');
        expect(updatedPackage?.purchaseLimit).toBe(10);
      }
    });

    it('should set package status successfully', async () => {
      if (createdPackageIds.length > 0) {
        const packageId = createdPackageIds[0];
        const statusReq: PackageSetStatusReq = {
          packageId: packageId,
          status: false,
          by: testBy
        };

        const affectedRows = await packageService.setStatus(statusReq);
        expect(affectedRows).toBe(1);

        // Verify the status change
        const updatedPackage = await packageService.findById(packageId);
        expect(updatedPackage?.status).toBe(false);
      }
    });
  });

  describe('Sequence Management', () => {
    it('should create packages with sequences', async () => {
      // Create multiple packages to test sequence operations
      const packages = [
        {
          name: 'Package A',
          sequence: 1
        },
        {
          name: 'Package B', 
          sequence: 2
        },
        {
          name: 'Package C',
          sequence: 3
        }
      ];

      for (const pkg of packages) {
        const testPackage: Package = {
          id: '',
          name: pkg.name,
          currency: 'SGD',
          price: 100,
          credits: 100,
          bonusCredits: 10,
          status: true,
          firstTimeOnly: false,
          instructorOnly: false,
          validFrom: null,
          validTo: null,
          purchaseLimit: null,
          sequence: pkg.sequence
        };

        const req: PackageSaveReq = {
          package: testPackage,
          by: testBy
        };

        const packageId = await packageService.create(req);
        createdPackageIds.push(packageId);
      }

      expect(createdPackageIds.length).toBeGreaterThan(1);
    });

    it('should move package sequence up', async () => {
      if (createdPackageIds.length > 1) {
        const packageId = createdPackageIds[1]; // Use the second package
        const moveReq: PackageMoveSequenceReq = {
          packageId: packageId,
          direction: 'up',
          by: testBy
        };

        try {
          await packageService.moveSequence(moveReq);
          // If it succeeds, that's good
        } catch (error) {
          // If it fails with "No package available to move up", that's also expected
          expect(error.message).toMatch(/No package available to move up/);
        }
      }
    });

    it('should move package sequence down', async () => {
      if (createdPackageIds.length > 1) {
        const packageId = createdPackageIds[0]; // Use the first package
        const moveReq: PackageMoveSequenceReq = {
          packageId: packageId,
          direction: 'down',
          by: testBy
        };

        try {
          await packageService.moveSequence(moveReq);
          // If it succeeds, that's good
        } catch (error) {
          // If it fails with "No package available to move down", that's also expected
          expect(error.message).toMatch(/No package available to move down/);
        }
      }
    });

    it('should reorder sequences for currency', async () => {
      await expect(packageService.reorderSequences('SGD', testBy.username)).resolves.not.toThrow();
    });

    it('should get max sequence for currency', async () => {
      const maxSeq = await packageService.getMaxSequence('SGD');
      expect(typeof maxSeq).toBe('number');
      expect(maxSeq).toBeGreaterThanOrEqual(0);
    });
  });

  afterAll(async () => {
    // Always close Sequelize connection
    await closeSequelize();
    await closePool();
  });
}); 