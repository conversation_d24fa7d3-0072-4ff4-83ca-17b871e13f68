import 'dotenv/config';
import { CountryService } from "@backoffice-cms/services/country-service";
import { initSequelize, closeSequelize } from "../../init-sequelize";
import logger from "@utils/logger";

describe('Countries Integration Tests', () => {
  let countryService: CountryService;

  beforeAll(async () => {
    // Initialize Sequelize before running tests
    await initSequelize();
    countryService = new CountryService();
  });

  describe('Country List Operations', () => {
    it('should fetch all countries successfully', async () => {
      const countries = await countryService.getAllCountries();

      expect(countries).toBeDefined();
      expect(Array.isArray(countries)).toBe(true);
      expect(countries.length).toBeGreaterThan(0);

      // Verify the structure of each country
      countries.forEach(country => {
        expect(country).toHaveProperty('code');
        expect(country).toHaveProperty('name');
        expect(country).toHaveProperty('timezone');
        expect(country).toHaveProperty('currency');
        expect(country).toHaveProperty('currency_name');
        expect(country).toHaveProperty('symbol');
      });
    });

    it('should return countries sorted by name', async () => {
      const countries = await countryService.getAllCountries();

      // Check if sorted by name
      for (let i = 1; i < countries.length; i++) {
        const prev = countries[i - 1];
        const curr = countries[i];
        expect(prev.name.localeCompare(curr.name)).toBeLessThanOrEqual(0);
      }
    });

    it('should have valid country codes and names', async () => {
      const countries = await countryService.getAllCountries();

      countries.forEach(country => {
        expect(country.code.length).toBeLessThanOrEqual(2);
        expect(country.name.length).toBeGreaterThan(0);
        expect(country.timezone.length).toBeGreaterThan(0);
        expect(country.currency.length).toBeLessThanOrEqual(3);
        expect(country.currency_name.length).toBeGreaterThan(0);
        expect(country.symbol.length).toBeGreaterThan(0);
      });
    });

    it('should contain expected countries', async () => {
      const countries = await countryService.getAllCountries();
      const countryCodes = countries.map(country => country.code);
      const countryNames = countries.map(country => country.name);

      // Check for expected countries
      expect(countryCodes).toContain('SG');
      expect(countryCodes).toContain('US');
      expect(countryCodes).toContain('MY');
      expect(countryCodes).toContain('GB');
      expect(countryCodes).toContain('JP');

      expect(countryNames).toContain('Singapore');
      expect(countryNames).toContain('United States');
      expect(countryNames).toContain('Malaysia');
      expect(countryNames).toContain('United Kingdom');
      expect(countryNames).toContain('Japan');
    });
  });

  describe('Country Options Operations', () => {
    it('should fetch country options successfully', async () => {
      const options = await countryService.getCountryOption();

      expect(options).toBeDefined();
      expect(Array.isArray(options)).toBe(true);
      expect(options.length).toBeGreaterThan(0);

      // Verify the structure of each option
      options.forEach(option => {
        expect(option).toHaveProperty('value');
        expect(option).toHaveProperty('label');
        expect(typeof option.value).toBe('string');
        expect(typeof option.label).toBe('string');
        expect(option.value.length).toBeLessThanOrEqual(2); // Country codes are 2 chars max
      });
    });

    it('should return unique country codes', async () => {
      const options = await countryService.getCountryOption();
      const countryCodes = options.map(option => option.value);
      const uniqueCodes = [...new Set(countryCodes)];

      expect(uniqueCodes.length).toBe(countryCodes.length);
    });

    it('should have valid country option data', async () => {
      const options = await countryService.getCountryOption();

      options.forEach(option => {
        expect(option.value.length).toBeGreaterThan(0);
        expect(option.label.length).toBeGreaterThan(0);
        expect(option.value.length).toBeLessThanOrEqual(2);
      });
    });

    it('should contain expected country options', async () => {
      const options = await countryService.getCountryOption();
      const countryCodes = options.map(option => option.value);
      const countryNames = options.map(option => option.label);

      // Check for expected countries
      expect(countryCodes).toContain('SG');
      expect(countryCodes).toContain('US');
      expect(countryCodes).toContain('MY');
      expect(countryCodes).toContain('GB');
      expect(countryCodes).toContain('JP');

      expect(countryNames).toContain('Singapore');
      expect(countryNames).toContain('United States');
      expect(countryNames).toContain('Malaysia');
      expect(countryNames).toContain('United Kingdom');
      expect(countryNames).toContain('Japan');
    });

    it('should return country options in expected format', async () => {
      const options = await countryService.getCountryOption();

      // Verify the format of the first option
      expect(options.length).toBeGreaterThan(0);
      expect(options[0]).toHaveProperty('value');
      expect(options[0]).toHaveProperty('label');
      expect(typeof options[0].value).toBe('string');
      expect(typeof options[0].label).toBe('string');
    });
  });

  describe('Data Consistency Tests', () => {
    it('should have consistent data between country list and country options', async () => {
      const countries = await countryService.getAllCountries();
      const options = await countryService.getCountryOption();

      // Both should have the same number of entries
      expect(countries.length).toBe(options.length);

      // Create maps for easy lookup
      const countryMap = new Map(countries.map(c => [c.code, c]));
      const optionMap = new Map(options.map(o => [o.value, o]));

      // Check that all country codes in options exist in countries
      options.forEach(option => {
        const country = countryMap.get(option.value);
        expect(country).toBeDefined();
        expect(country.name).toBe(option.label);
      });

      // Check that all countries have corresponding options
      countries.forEach(country => {
        const option = optionMap.get(country.code);
        expect(option).toBeDefined();
        expect(option.label).toBe(country.name);
      });
    });
  });

  afterAll(async () => {
    // Close Sequelize connection after tests
    await closeSequelize();
  });
}); 