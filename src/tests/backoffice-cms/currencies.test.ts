import 'dotenv/config';
import { CountryService } from "@backoffice-cms/services/country-service";
import { initSequelize, closeSequelize } from "../../init-sequelize";
import logger from "@utils/logger";
import { CurrencyOption } from '@database/models/country';

describe('Currencies Integration Tests', () => {
  let countryService: CountryService;

  beforeAll(async () => {
    // Initialize Sequelize before running tests
    await initSequelize();
    countryService = new CountryService();
  });

  describe('Currency Options', () => {
    it('should fetch currency options successfully', async () => {
      const currencies = await countryService.getCurrencyOptions();

      expect(currencies).toBeDefined();
      expect(Array.isArray(currencies)).toBe(true);
      expect(currencies.length).toBeGreaterThan(0);

      // Verify the structure of each option
      currencies.forEach(currency => {
        expect(currency).toHaveProperty('value');
        expect(currency).toHaveProperty('label');
        expect(typeof currency.value).toBe('string');
        expect(typeof currency.label).toBe('string');

        expect(currency.value.length).toBeLessThanOrEqual(3);
      });

      // Check for expected currencies from the migration
      const currencyCodes = currencies.map(currency => currency.value);
      expect(currencyCodes).toContain('SGD');
      expect(currencyCodes).toContain('USD');
      expect(currencyCodes).toContain('MYR');
      expect(currencyCodes).toContain('GBP');
      expect(currencyCodes).toContain('JPY');
    });

    it('should return unique currency codes', async () => {
      const options = await countryService.getCurrencyOptions();
      const currencyCodes = options.map(opt => opt.value);
      const uniqueCodes = [...new Set(currencyCodes)];

      expect(uniqueCodes.length).toBe(currencyCodes.length);
    });

    it('should have valid currency data', async () => {
      const options = await countryService.getCurrencyOptions();

      options.forEach(option => {
        expect(option.value.length).toBeGreaterThan(0);
        expect(option.label.length).toBeGreaterThan(0);
      });
    });
  });

  afterAll(async () => {
    // Close Sequelize connection after tests
    await closeSequelize();
  });
}); 