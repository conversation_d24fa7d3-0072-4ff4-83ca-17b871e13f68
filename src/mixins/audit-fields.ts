import { DbTransactionOptions } from '@shared/constants';
import { Model, ModelStatic } from 'sequelize';


export const applyAuditFields = (Model: ModelStatic<Model>) => {
  Model.beforeCreate((instance, options: DbTransactionOptions) => {
    if (options.authId) {
      instance.set('created_by', options.authId);
      instance.set('updated_by', options.authId);
    }
  });

  Model.beforeUpdate((instance, options: DbTransactionOptions) => {
    if (options.authId) {
      const changedFields = instance.changed();
      if (changedFields && changedFields.length > 0) {
        instance.set('updated_by', options.authId);
      }
    }
  });
};
