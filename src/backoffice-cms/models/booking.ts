import { By } from "@shared/models";
import { EventCancelBy } from "@studio-cms/models/event";

export interface BookingCancelReq {
  id: string;
  eventId: string;
  reqBy: string;
  free?: boolean;
  userId?: string;
  reason?: string;
  approved: boolean;
  approvedAt?: Date;
  approvedBy?: string;
  at: Date;
}

export interface Booking {
  id: string,
  refNo: string,
  status: string,
  paymentStatus: string,
  fees?: BookingFees,
  startDate: Date,
  startTime: string,
  endDate: Date,
  endTime: string,
  studio: BookingStudio,
  instructor?: BookingInstructor,
  member: BookingMember,
  equipment: BookingEquipment,
  cancelled?: boolean,
  cancelledAt?: Date,
  cancelledBy?: string,
  cancelFree?: boolean,
  cancelReason?: string,
  cancelRefundedAmount?: number,
  noShow?: boolean,
  noShowAt?: Date,
  noShowRemarks?: string,
  backofficeRemarks?: string,
  starred?: boolean,
  deleted?: boolean,
  cancelReq?: BookingCancelReq,
  updatedAt?: Date,
  timezone?: string,
}

export interface BookingFees {
  items: BookingFeeItem[];
  total: string;
  totalCredits?: string;
  currency?: string;
}

export interface BookingFeeItem {
  name: string,
  price: string,
  credit?: string,
  currency?: string,
  sign?: string,
}

export interface BookingStudio {
  id: string,
  name: string,
}

export interface BookingInstructor {
  id: string,
  name: string,
}

export interface BookingMember {
  id: string,
  displayName: string,
  fullName: string,
}

export interface BookingEquipment {
  id: string,
  name: string,
  code?: string,
}

export interface BookingFindCriteria {
  bookingId: string,
  adminId: string,
  inclCancelled?: boolean,
  pendingCancelReq?: boolean,
}

export interface BookingSetCancelledReq {
  reqBy: EventCancelBy;
  bookingId: string,
  userId?: string,
  free: boolean,
  reason: string,
  refundCredits: number,
  by: By,
}

export interface BookingSaveRemarksReq {
  bookingId: string,
  value: string,
  by: By,
}

export interface BookingSetStarredReq {
  bookingId: string,
  starred: boolean,
  by: By,
}

export interface BookingCountResponse {
  pendingCancelCount: number;
}
