import { By } from "@shared/models";

export enum PromotionType {
  BOOKING = "booking",
  TOPUP = "topup",
}

export enum PromotionStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export interface Promotion {
  id: number;
  name: string;
  description: string;
  type: PromotionType;
  value: number;
  start_date: string;
  end_date: string;
  per_user_limit: number;
  global_limit: number;
  status: PromotionStatus;
  created_by: string;
  created_at: string;
  updated_by?: string;
  updated_at?: string;
  deleted_by?: string;
  deleted_at?: string;
}

export interface PromoCode {
  id: number;
  promotion_id: number;
  code: string;
  code_limit: number;
  assigned_user_id: string;
  status: PromotionStatus;
  created_by: string;
  created_at: string;
  updated_by?: string;
  updated_at?: string;
  deleted_by?: string;
  deleted_at?: string;
}

export interface PromotionWithCounts extends Promotion {
  code_count: number;
  applied_count: number;
}

export interface PromoCodeWithDetails extends PromoCode {
  applied_count: number;
  assigned_user_fullname: string;
}

export interface PromotionSaveReq {
  promotion: Promotion;
  by: By;
}

export interface PromoCodeSaveReq {
  promotionId: number;
  code?: string;
  count?: number;
  prefix?: string;
  code_limit?: number;
  by: By;
}

export interface PromoCodeAssignMemberReq {
  promoCodeId: number;
  memberId: string;
  by: By;
}