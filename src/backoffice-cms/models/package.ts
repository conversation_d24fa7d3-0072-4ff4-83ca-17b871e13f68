import { By } from "@shared/models";

export interface Package {
  id: string;
  name: string;
  currency: string;
  price: number;
  credits: number;
  bonusCredits: number;
  status: boolean;
  firstTimeOnly: boolean;
  instructorOnly: boolean;
  validFrom?: string;
  validTo?: string;
  purchaseLimit?: number;
  sequence?: number;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface PackageFindCriteria {
  packageId?: string;
  currency?: string;
  status?: boolean; // undefined = all statuses, true = active only, false = inactive only
}

export interface PackageSaveReq {
  package: Package;
  by: By;
}

export interface PackageSetStatusReq {
  packageId: string;
  status: boolean;
  by: By;
}

export interface PackageMoveSequenceReq {
  packageId: string;
  direction: 'up' | 'down';
  by: By;
} 