import { By } from "@shared/models";

export interface Instructor {
  id: string;
  firebaseUid: string;
  name: string;
  descr?: string;
  facebook?: string;
  instagram?: string;
  certifications?: string;
  specialisations?: string;
  pictures: string[];
  registeredAt: string;
  onboarded?: boolean;
  pendingApproval?: boolean,
  backofficeRemarks?: string,
  starred?: boolean,
  deleted?: boolean,
}

export interface InstructorFindCriteria {
  instrId: string,
  adminId: string,
  inclDisabled?: boolean,
}

export interface InstructorSetActiveReq {
  instrId: string,
  active: boolean,
  by: By,
}

export interface InstructorCopyToLiveReq {
  instrId: string,
  by: By,
}

export interface InstructorSetApprovedReq {
  instrId: string,
  by: By,
}

export interface InstructorSaveRemarksReq {
  instrId: string,
  value: string,
  by: By,
}

export interface InstructorSetStarredReq {
  instrId: string,
  starred: boolean,
  by: By,
}
