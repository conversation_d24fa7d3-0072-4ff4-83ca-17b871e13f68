import { By } from "@shared/models";

export interface StudioUser {
  id?: string,
  firebaseUid: string,
  firebaseEmail: string,
  studioId: string,
  name: string,
  jobTitle: string,
  contactNo: string,
  admin?: boolean,
  active?: boolean,
  emailVerified?: boolean,
}

/*
export interface StudioUserFindCriteria {
  studioId: string,
  inclDisabled?: boolean,
}

export interface UserSaveReq {
  user: User,
  by: By,
}
*/

// (!) this is for 1 studio user
export interface StudioUserSetActiveReq {
  userId: string,
  studioId: string,
  active: boolean,
  by: By,
}

// (!) this is for all studio users
export interface StudioUsersSetActiveReq {
  studioId: string,
  active: boolean,
  by: By,
}

/*
export interface FirebaseUidSetReq {
  userId: string,
  firebaseUid: string,
  firebaseEmail: string,
  by: By,
}
*/
