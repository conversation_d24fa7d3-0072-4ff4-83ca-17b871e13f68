import { GeocodeR<PERSON>ult } from "@googlemaps/google-maps-services-js";
import { LatLng } from "@booking-api/models/search";
import { By } from "@shared/models";
import { SocialMedia } from "./shared";

export interface Studio {
  id: string,
  name: string,
  descr: string,
  contactNo: string,
  email: string,
  address: string,
  placeId: string,
  geocode?: GeocodeResult,
  latLng?: LatLng,
  socialMedia: SocialMedia,
  usagePolicies: string,
  instrLess?: boolean,
  pictures: string[];
  videos: string[];
  active?: boolean,
  pendingApproval?: boolean,
  hasDoorPin?: boolean,
  backofficeRemarks?: string,
  starred?: boolean,
  deleted?: boolean,
  mainUser?: StudioMainUser;
}

export interface StudioMainUser {
  id: string,
  name: string,
  email: string,
  jobTitle: string,
  contactNo: string,
}

export interface StudioFindCriteria {
  studioId: string,
  adminId: string,
  inclDisabled?: boolean,
}

export interface StudioSetActiveReq {
  studioId: string,
  active: boolean,
  by: By,
}

export interface StudioCopyToLiveReq {
  studioId: string,
  by: By,
}

export interface StudioSetApprovedReq {
  studioId: string,
  by: By,
}

export interface StudioSaveRemarksReq {
  studioId: string,
  value: string,
  by: By,
}

/*
export interface StudioSaveReq {
  studio: Studio,
  by: By,
}
*/

export interface StudioSetStarredReq {
  studioId: string,
  starred: boolean,
  by: By,
}
