import { By } from "@shared/models";

export interface Admin {
  id: string;
  firebaseUid: string;
  firebaseEmail: string;
  name: string;
  internalNotes: string;
  lastAt: AdminLastAt,
  deleted?: boolean;
}

export interface AdminLastAt {
  at: string;
  byUid: string;
  by: string;
}

export interface AdminFindCriteria {
  adminId: string,
  inclDeleted?: boolean,
}

export interface AdminSaveReq {
  admin: Admin,
  by: By,
}

export interface AdminSetDeletedReq {
  adminId: string,
  deleted: boolean,
  by: By,
}

export interface AdminSaveRemarksReq {
  adminId: string,
  value: string,
  by: By,
}
