import { Response, NextFunction, RequestHandler } from "express";

import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { ApiRequest } from "@shared/constants";
import { ServiceError } from "@shared/services";
import env from '@config/env.config';

export const requireBackofficeAccess = (handler: RequestHandler): RequestHandler => {
  return async (req: ApiRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const idToken = getIdToken(req);

      const authorised = await isAuthorisedForBackoffice(idToken);
      if (!authorised) {
        throw new ServiceError("401", `not authorised to access backoffice`);
      }

      req.idToken = idToken;
      req.auth = { boId: env.get('BO_ID')};

      await handler(req, res, next);
    } catch (err) {
      res.status(err.code || 500).json({ success: false, message: err.message });
      throw err;
    }
  };
}
