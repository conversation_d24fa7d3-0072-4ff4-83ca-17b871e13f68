import { Request<PERSON><PERSON><PERSON> } from "express";
import { requireBackofficeAccess } from "../middlewares/backoffice-access";
import { CountryService } from "../services/country-service";

const countryService = new CountryService();

const getCurrencyOptions: RequestHandler = async (req, res) => {
  try {
    const options = await countryService.getCurrencyOptions();
    res.status(200).json({
      success: true,
      data: options
    });
  } catch (error) {
    console.error('Error fetching currency options:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch currency options'
    });
  }
};

export default requireBackofficeAccess(getCurrencyOptions); 