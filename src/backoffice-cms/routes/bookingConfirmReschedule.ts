import { Request<PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { BookingService as BookingServiceBO } from "@backoffice-cms/services/booking-service";
import { BookingService as BookingApiService } from "@booking-api/services/booking-service";
import { EquipmentService } from "@studio-cms/services/equipment-service";
import { InstructorService } from "@instructor-api/services/instructor-service";
import { MemberService } from "@member-api/services/member-service";
import { StudioService } from "@backoffice-cms/services/studio-service";
import { SearchService } from "@booking-api/services/search-service";
import { SearchAt } from "@booking-api/models/search";
import { toBy } from "@shared/request-parsers";
import { BookingFlowService } from "@booking-api/services/booking-flow-service";

const bookingServiceBO = new BookingServiceBO();
const bookingApiService = new BookingApiService();
const studioService = new StudioService();
const bookingFlow = new BookingFlowService();

const bookingConfirmReschedule: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const by = idToken.email;
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }
  const bookingId = req.params.bookingId + "";
  const { date, time } = req.body;
  if (!date || !time) {
    res.status(400).end("Missing date or time");
    return;
  }
  try {
    // Fetch original booking
    const booking = await bookingServiceBO.findById(bookingId);
    if (!booking || !booking.equipment?.id || !booking.studio?.id || !booking.member?.id) {
      throw new Error("Booking missing required details");
    }
    const studio = await studioService.findById(booking.studio.id);
    if (!studio || !studio.latLng) {
      throw new Error("studio not found or missing latLng");
    }
    const at = new SearchAt(date, time);
    // Validation
    const validation = await bookingFlow.validateSlotAndCredits({
      latLng: studio.latLng,
      distance: 50,
      at,
      studioId: booking.studio.id,
      equipId: booking.equipment.id,
      instrId: booking.instructor?.id,
      memberId: booking.member.id,
    });
    if (!validation.valid) throw new Error(validation.reason);
    // Booking creation
    const { bookingId: newBookingId, event } = await bookingFlow.createBooking({
      equip: validation.equip,
      instr: validation.instr,
      at,
      studioId: booking.studio.id,
      equipId: booking.equipment.id,
      instrId: booking.instructor?.id,
      memberId: booking.member.id,
      by: toBy(idToken),
      bookedByAdmin: true,
      bookingFees: validation.bookingFees,
    });
    // Post-booking actions
    const newBooking = await bookingApiService.bookingById(newBookingId);
    if (newBooking) {
      bookingFlow.emitSessionConfirmed({ by, booking: newBooking });
      await bookingFlow.handleDoorPinIfNeeded({ booking: newBooking, event, by });
    }
    res.status(200).json({ success: true, data: { bookingId: newBookingId } });
  } catch (error) {
    res.status(error instanceof Error ? 200 : 400).json({
      success: false,
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
};

export default bookingConfirmReschedule;