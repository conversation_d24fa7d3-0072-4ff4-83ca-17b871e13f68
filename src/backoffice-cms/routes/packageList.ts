import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { requireBackofficeAccess } from "../middlewares/backoffice-access";
import { PackageService } from "../services/package-service";
import { toPageOfPackagesJson } from "./shared/response-output";
import { Page, Sort } from "@shared/services";
import { PackageFindCriteria } from "../models/package";
import { toBoolean } from "@shared/request-parsers";

const packageService = new PackageService();

const packageList: RequestHandler = async (req, res) => {
  const sortQuery = req.query.sort?.toString().trim();
  const sortDirQuery = req.query.sortDir?.toString().trim() ?? 'asc';
  const sort = sortQuery
    ? new Sort(sortQuery, sortDirQuery === 'asc')
    : undefined;

  const page = new Page(+req.params.page, +req.params.pageSize);

  const criteria: PackageFindCriteria = {
    currency: req.query.currency?.toString(),
    status: toBoolean(req.query.status),
  };

  const results = await packageService.findAll(criteria, sort, page);
  res.status(200).json(toPageOfPackagesJson(results));
};

export default requireBackofficeAccess(packageList); 