import { AdminFindCriteria } from "@backoffice-cms/models/admin";
import { AdminService } from "@backoffice-cms/services/admin-service";
import { Page, Sort } from "@shared/services";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { toPageOfAdminsJson } from "./shared/response-output";

const adminService = new AdminService();

const adminList: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const sortQuery = req.query.sort?.toString().trim();
  const sortDirQuery = req.query.sortDir?.toString().trim() ?? 'asc';
  const sort = sortQuery
    ? new Sort(sortQuery, sortDirQuery === 'asc')
    : undefined;

  const inclDeleted = req.query['inclDeleted'] + "";
  const page = new Page(+req.params.page, +req.params.pageSize);
  const ret = await adminService.findAll({
    inclDeleted: (inclDeleted === "true"),
  } as AdminFindCriteria, sort, page);
  res.status(200).json(toPageOfAdminsJson(ret));
};

export default adminList;
