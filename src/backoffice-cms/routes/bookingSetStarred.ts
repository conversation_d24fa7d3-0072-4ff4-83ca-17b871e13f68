import { BookingSetStarredReq } from "@backoffice-cms/models/booking";
import { BookingService } from "@backoffice-cms/services/booking-service";
import { toBy } from "@shared/request-parsers";
import { RequestHand<PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";

const bookingService = new BookingService();

const bookingSetStarred: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const bookingId = req.params.bookingId + "";
  const starred = req.params.starred + "";
  await bookingService.setStarred({
    bookingId: bookingId,
    starred: !(starred === "true"),
    by: toBy(idToken),
  } as BookingSetStarredReq);
  res.status(200).end();
};

export default bookingSetStarred;
