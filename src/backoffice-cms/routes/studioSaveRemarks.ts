import { StudioSaveRemarksReq } from "@backoffice-cms/models/studio";
import { StudioService } from "@backoffice-cms/services/studio-service";
import { toBy } from "@shared/request-parsers";
import { RequestHand<PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";

const studioService = new StudioService();

const studioSaveRemarks: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const body = req.body;
  const studioId = body.id!.toString();
  const value = body.value!.toString();
  await studioService.saveRemarks({
    studioId: studioId,
    value: value,
    by: toBy(idToken),
  } as StudioSaveRemarksReq);
  res.status(200).end();
};

export default studioSaveRemarks;
