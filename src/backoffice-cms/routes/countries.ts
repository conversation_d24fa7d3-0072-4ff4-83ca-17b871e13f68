import { RequestHand<PERSON> } from "express";
import { requireBackofficeAccess } from "../middlewares/backoffice-access";
import { CountryService } from "../services/country-service";

const countryService = new CountryService();

const getCountries: RequestHandler = async (req, res) => {
  try {
    const countries = await countryService.getCountryOption();
    res.status(200).json({
      success: true,
      data: countries
    });
  } catch (error) {
    console.error('Error fetching countries:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch countries'
    });
  }
};

export default requireBackofficeAccess(getCountries); 