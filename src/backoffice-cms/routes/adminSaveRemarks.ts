import { AdminSaveRemarksReq } from "@backoffice-cms/models/admin";
import { AdminService } from "@backoffice-cms/services/admin-service";
import { toBy } from "@shared/request-parsers";
import { RequestHand<PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";

const adminService = new AdminService();

const adminSaveRemarks: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const body = req.body;
  const instrId = body.id!.toString();
  const value = body.value!.toString();
  await adminService.saveRemarks({
    adminId: instrId,
    value: value,
    by: toBy(idToken),
  } as AdminSaveRemarksReq);
  res.status(200).end();
};

export default adminSaveRemarks;
