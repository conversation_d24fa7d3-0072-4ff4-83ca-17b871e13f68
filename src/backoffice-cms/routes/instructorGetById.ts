import { Request<PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toInstructor<PERSON><PERSON> } from "./shared/response-output";

const instructorService = new InstructorService();

const instructorGetById: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const instructorId = req.params.instructorId;
  const ret = await instructorService.findById(instructorId);
  if (!ret) {
    res.status(204).end(); // no content
  } else {
    res.status(200).json(toInstructorJson(ret));
  }
};

export default instructorGetById;
