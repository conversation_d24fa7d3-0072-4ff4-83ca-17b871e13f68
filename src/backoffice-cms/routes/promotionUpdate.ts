import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { PromotionService } from "@backoffice-cms/services/promotion-service";
import { toPromotionSaveReq } from "./shared/request-parsers";

const promotionService = new PromotionService();

const promotionUpdate: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const { promotionId } = req.params;

  try {
    await promotionService.updatePromotion(Number(promotionId), toPromotionSaveReq(req.body, idToken));
    res.status(200).json({ success: true, message: "Promotion updated successfully" });
  } catch (err) {
    res.status(400).json({ success: false, message: err.message });
  }
};

export default promotionUpdate;