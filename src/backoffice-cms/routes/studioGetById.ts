import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { StudioService } from "../services/studio-service";
import { toStudioJson } from "./shared/response-output";

const studioService = new StudioService();

const studioGetById: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const studioId = req.params.studioId;
  const ret = await studioService.findById(studioId);
  if (!ret) {
    res.status(204).end(); // no content
  } else {
    res.status(200).json(toStudioJson(ret));
  }
};

/*
async function isEmailVerified(firebaseEmail: string)  {
  let verified = false;
  try {
    const user = await FirebaseAuth.getUserByEmail(firebaseEmail);
    verified = user.emailVerified;
  } catch (e) {
    logger.info(`Error @ FirebaseAuth.getUserByEmail(${firebaseEmail}), ${e}`);
  }
  return verified;
}
*/

export default studioGetById;
