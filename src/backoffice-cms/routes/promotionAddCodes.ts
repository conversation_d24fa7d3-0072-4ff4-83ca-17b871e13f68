import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { PromotionService } from "@backoffice-cms/services/promotion-service";
import { toPromoCodeSaveReq } from "./shared/request-parsers";

const promotionService = new PromotionService();

const promotionAddCodes: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const { promotionId } = req.params;
  const saveReq = toPromoCodeSaveReq(req.body, Number(promotionId), idToken);

  try {
    let codes: string[] = [];
    if (saveReq.code) {
      // Single public code
      await promotionService.addCode(saveReq);
      codes = [saveReq.code];
    } else if (saveReq.count) {
      // Batch code generation
      codes = await promotionService.generateCodes(saveReq);
    } else {
      throw new Error("Either 'code' or 'count' must be provided.");
    }
    res.status(201).json({ success: true, data: codes });
  } catch (err) {
    if (err.code === 'ER_DUP_ENTRY' || err.message.includes('already exists')) {
      res.status(409).json({ success: false, message: "Code already exists" });
    } else {
      res.status(400).json({ success: false, message: err.message });
    }
  }
};

export default promotionAddCodes;
