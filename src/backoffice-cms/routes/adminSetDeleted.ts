import { AdminSetDeletedReq } from "@backoffice-cms/models/admin";
import { AdminService } from "@backoffice-cms/services/admin-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";

const adminService = new AdminService();

const adminSetDeleted: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  // get self admin record
  const admin = await adminService.findById(idToken.uid);
  if (!admin) {
    res.status(409).end("Admin record not found");
    return;
  }

  const adminId = req.params.adminId + "";
  const deleted = req.params.deleted + "";
  await adminService.setDeleted({
    adminId: adminId,
    deleted: (deleted === "true"),
    by: toBy(idToken),
  } as AdminSetDeletedReq);
  res.status(200).end();
};

export default adminSetDeleted;
