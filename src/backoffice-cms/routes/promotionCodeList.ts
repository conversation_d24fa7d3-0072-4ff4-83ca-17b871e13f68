import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { PromotionService } from "@backoffice-cms/services/promotion-service";
import { toPageOfPromoCodesJson } from "./shared/response-output";

const promotionService = new PromotionService();

const promotionCodeList: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const { promotionId, page, pageSize } = req.params;

  try {
    const result = await promotionService.listPromoCodes(Number(promotionId), Number(page), Number(pageSize));
    res.status(200).json({ success: true, data: toPageOfPromoCodesJson(result) });
  } catch (err) {
    res.status(400).json({ success: false, message: err.message });
  }
};

export default promotionCodeList; 