import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { PromotionService } from "@backoffice-cms/services/promotion-service";
import { toPromoCodeAssignMemberReq } from "./shared/request-parsers";
import { toBy } from "@shared/request-parsers";

const promotionService = new PromotionService();

const promoCodeAssignMember: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }
  const assignReq = toPromoCodeAssignMemberReq(req.body, idToken);
  try {
    await promotionService.assignMemberToPromoCode(assignReq.promoCodeId, assignReq.memberId, toBy(idToken));
    res.status(200).json({ success: true, message: "member assigned to promo code" });
  } catch (err) {
    res.status(400).json({ success: false, message: err instanceof Error ? err.message : err });
  }
};

export default promoCodeAssignMember; 