import { InstructorSaveRemarksReq } from "@backoffice-cms/models/instructor";
import { InstructorService } from "@backoffice-cms/services/instructor-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";

const instructorService = new InstructorService();

const instructorSaveRemarks: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const body = req.body;
  const instrId = body.id!.toString();
  const value = body.value!.toString();
  await instructorService.saveRemarks({
    instrId: instrId,
    value: value,
    by: toBy(idToken),
  } as InstructorSaveRemarksReq);
  res.status(200).end();
};

export default instructorSaveRemarks;
