import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { requireBackofficeAccess } from "../middlewares/backoffice-access";
import { PackageService } from "../services/package-service";
import { toPackageJson } from "./shared/response-output";

const packageService = new PackageService();

const packageGetById: RequestHandler = async (req, res) => {
  const packageId = req.params.packageId;
  const packageData = await packageService.findById(packageId);

  if (!packageData) {
    res.status(204).send();
    return;
  }

  res.status(200).json(toPackageJson(packageData));
};

export default requireBackofficeAccess(packageGetById);