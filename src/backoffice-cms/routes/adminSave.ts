import { ServiceError } from "@shared/services";
import logger from "@utils/logger";
import { RequestHandler } from "express";
import { auth } from "firebase-admin";
import { FirebaseAuthBackOffice } from "../../init-firebase";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { AdminService } from "../services/admin-service";
import { toAdminSaveReq } from "./shared/request-parsers";
import UserRecord = auth.UserRecord;

const adminService = new AdminService();

const adminSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const body = req.body;

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  // (!) check that firebase email exists in FirebaseAuth,
  // and email has been verified.
  let firebaseUser: UserRecord|undefined = undefined;
  let email = body.firebaseEmail!.trim();
  try {
    firebaseUser = await FirebaseAuthBackOffice.getUserByEmail(email);
  } catch (e) {
    logger.info(`Error @ FirebaseAuthBackOffice.getUserByEmail(${email}), ${e}`);
  }
  if (!firebaseUser) {
    res.status(409).end("Email address has not signed up yet!");
    return;
  }
  if (!firebaseUser.emailVerified) {
    res.status(409).end("Email address has not been verified yet!");
    return;
  }

  // get self admin record
  const admin = await adminService.findById(idToken.uid);
  if (!admin) {
    res.status(409).end("Admin record not found");
    return;
  }

  const toSave = toAdminSaveReq(body, idToken);
  toSave.admin.firebaseUid = firebaseUser.uid;      // (!) firebaseUid
  toSave.admin.firebaseEmail = firebaseUser.email!; // (!) firebaseEmail
  if (!toSave.admin.id?.trim()) {
    // new admin -> create
    adminService.create(toSave)
      .then(() => res.status(200).end())
      .catch((error) => handleCreateError(error));
  } else {
    // existing admin -> update
    await adminService.update(toSave);
    res.status(200).end();
  }

  const handleCreateError = (error: any) => {
    if (error instanceof ServiceError) {
      if (error.code === "409") {
        res.status(409).end("Email address already exists!");
      } else {
        res.status(500).end();
      }
    } else {
      res.status(500).end();
    }
  }
};

export default adminSave;
