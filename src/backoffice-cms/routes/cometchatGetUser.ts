import { getCometChatUser } from "@utils/cometchat";
import { Response } from "express";
import { ApiRequest } from "@shared/constants";
import { requireBackofficeAccess } from "@backoffice-cms/middlewares/backoffice-access";

export default requireBackofficeAccess(async (req: ApiRequest, res: Response) => {
  const { auth: { boId } } = req;
  const result = await getCometChatUser(boId);
  if (!result.success) {
    res.status(404).json(result);
    return;
  }
  res.status(200).json(result);
  return;
});