import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { requireBackofficeAccess } from "../middlewares/backoffice-access";
import { PackageService } from "../services/package-service";
import { getIdToken } from "../../init-openapi";

const packageService = new PackageService();

const packageReorderSequences: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const currency = req.params.currency;

  if (!currency) {
    res.status(400).json({ message: "Currency parameter is required" });
    return;
  }

  try {
    await packageService.reorderSequences(currency, idToken.email);
    res.status(200).json({ message: `Sequences reordered successfully for currency ${currency}` });
  } catch (error: any) {
    res.status(500).json({ message: error.message || "Internal server error" });
  }
};

export default requireBackofficeAccess(packageReorderSequences);