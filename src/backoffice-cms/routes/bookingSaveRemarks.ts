import { BookingSaveRemarksReq } from "@backoffice-cms/models/booking";
import { BookingService } from "@backoffice-cms/services/booking-service";
import { toBy } from "@shared/request-parsers";
import { RequestHand<PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";

const bookingService = new BookingService();

const bookingSaveRemarks: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const body = req.body;
  const bookingId = body.id!.toString();
  const value = body.value!.toString();
  await bookingService.saveRemarks({
    bookingId: bookingId,
    value: value,
    by: toBy(idToken),
  } as BookingSaveRemarksReq);
  res.status(200).end();
};

export default bookingSaveRemarks;
