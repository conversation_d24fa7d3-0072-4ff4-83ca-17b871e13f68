import { Request<PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { PromotionService } from "@backoffice-cms/services/promotion-service";
import { Page, Sort } from "@shared/services";
import { toPageOfPromotionsJson } from "./shared/response-output";

const promotionService = new PromotionService();

const promotionList: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const { page, pageSize } = req.params;
  // TODO: Add sorting and filtering from query params

  try {
    const result = await promotionService.listPromotions(Number(page), Number(pageSize));
    res.status(200).json({ success: true, data: toPageOfPromotionsJson(result) });
  } catch (err) {
    res.status(400).json({ success: false, message: err.message });
  }
};

export default promotionList; 