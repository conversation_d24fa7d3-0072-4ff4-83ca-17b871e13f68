import { StudioFindCriteria } from "@backoffice-cms/models/studio";
import { StudioService } from "@backoffice-cms/services/studio-service";
import { Page, Sort } from "@shared/services";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { toPageOfStudiosJson } from "./shared/response-output";

const studioService = new StudioService();

const studioList: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const sortQuery = req.query.sort?.toString().trim();
  const sortDirQuery = req.query.sortDir?.toString().trim() ?? 'asc';
  const sort = sortQuery
    ? new Sort(sortQuery, sortDirQuery === 'asc')
    : undefined;

  const inclDisabled = req.query['inclDisabled'] + "";
  const page = new Page(+req.params.page, +req.params.pageSize);
  const ret = await studioService.findAll({
    adminId: idToken.uid,
    inclDisabled: (inclDisabled === "true"),
  } as StudioFindCriteria, sort, page);
  res.status(200).json(toPageOfStudiosJson(ret));
};

export default studioList;
