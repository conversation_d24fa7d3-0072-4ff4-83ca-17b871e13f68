
import { createCometChatAuthToken, createComet<PERSON>hatUser, getCometChatUser } from "@utils/cometchat";
import { CometChatUser } from "@public-api/models/cometchat";
import { Response } from "express";
import { ApiRequest } from "@shared/constants";
import { requireBackofficeAccess } from "@backoffice-cms/middlewares/backoffice-access";


export default requireBackofficeAccess(async (req: ApiRequest, res: Response) => {
  const { auth: { boId } } = req;
  // 1. Check if CometChat user exists
  let userResult = await getCometChatUser(boId);
  if (!userResult.success) {
    // If not exists, create user
    userResult = await createCometChatUser({
      uid: boId,
      email: "<EMAIL>",
      name: "ViFit Admin",
      tags: ["backoffice"],
      userType: "backoffice",
    } as CometChatUser);

    if (!userResult.success) {
      res.status(500).json(userResult);
      return;
    }
  }
  // 2. Create auth token
  const tokenResult = await createCometChatAuthToken(boId);
  if (!tokenResult.success) {
    res.status(500).json(tokenResult);
    return;
  }
  res.status(200).json(tokenResult);
  return;
});
