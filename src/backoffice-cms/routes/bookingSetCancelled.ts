import { BookingSetCancelledReq } from "@backoffice-cms/models/booking";
import { BookingService as BookingServiceBO } from "@backoffice-cms/services/booking-service";
import { BookingService } from "@booking-api/services/booking-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { EventCancelBy } from "@studio-cms/models/event";
import eventEmitter from "@utils/event-emitter";

const bookingServiceBO = new BookingServiceBO();
const bookingService = new BookingService();

const bookingSetCancelled: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const bookingId = req.params.bookingId + "";
  const { reason, free = false, refundCredits = 0 } = req.body;

  if (!reason || typeof reason !== "string" || !reason.trim()) {
    res.status(400).end("Missing or invalid reason");
    return;
  }

  await bookingServiceBO.setCancelled({
    reqBy: EventCancelBy.Backoffice,
    bookingId: bookingId,
    userId: idToken.uid,
    reason: reason.trim(),
    free: true,
    refundCredits: +refundCredits,
    by: toBy(idToken),
  } as BookingSetCancelledReq);

  const event = await bookingService.bookingById(bookingId);
  if(event) {
    eventEmitter.emit('session-cancelled', {
      by: idToken.email,
      id: bookingId,
      data: event,
    });
  }

  res.status(200).end();
};

export default bookingSetCancelled;
