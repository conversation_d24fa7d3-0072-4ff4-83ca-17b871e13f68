import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { requireBackofficeAccess } from "../middlewares/backoffice-access";
import { PackageService } from "../services/package-service";
import { toPackageMoveSequenceReq } from "./shared/request-parsers";
import { getIdToken } from "../../init-openapi";

const packageService = new PackageService();

const packageMoveSequence: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const reqData = toPackageMoveSequenceReq(req.body, idToken);
  
  try {
    await packageService.moveSequence(reqData);
    const directionText = reqData.direction === 'up' ? 'up' : 'down';
    res.status(200).json({ message: `Package moved ${directionText} successfully` });
  } catch (error: any) {
    if (error.message?.includes("not found")) {
      res.status(404).json({ message: error.message });
    } else if (error.message?.includes("No package available")) {
      res.status(400).json({ message: error.message });
    } else {
      res.status(500).json({ message: error.message || "Internal server error" });
    }
  }
};

export default requireBackofficeAccess(packageMoveSequence); 