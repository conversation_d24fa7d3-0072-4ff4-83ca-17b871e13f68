import { BookingCountResponse } from "@backoffice-cms/models/booking";
import { BookingService } from "@backoffice-cms/services/booking-service";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";

const bookingService = new BookingService();

export const bookingPendingCancelCount: RequestHandler = async (req, res) => {
    const idToken = getIdToken(req);
    const authorised = await isAuthorisedForBackoffice(idToken);
    if (!authorised) {
        res.status(401).end("not authorised to access backoffice");
        return;
    }

    const pendingCancelCount = await bookingService.getPendingCancelCount();
    const response: BookingCountResponse = { pendingCancelCount };
    res.status(200).json(response);

};

export default bookingPendingCancelCount;
