import { StudioSetStarredReq } from "@backoffice-cms/models/studio";
import { StudioService } from "@backoffice-cms/services/studio-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";

const studioService = new StudioService();

const studioSetStarred: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const studioId = req.params.studioId + "";
  const starred = req.params.starred + "";
  await studioService.setStarred({
    studioId: studioId,
    starred: !(starred === "true"),
    by: toBy(idToken),
  } as StudioSetStarredReq);
  res.status(200).end();
};

export default studioSetStarred;
