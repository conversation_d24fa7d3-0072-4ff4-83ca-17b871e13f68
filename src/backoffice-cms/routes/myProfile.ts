import { Response } from "express";
import { ApiRequest } from "@shared/constants";
import { requireBackofficeAccess } from "@backoffice-cms/middlewares/backoffice-access";
import { getIdToken } from "../../init-openapi";
import { AdminService } from "@backoffice-cms/services/admin-service";
import { toAdminJson } from "@backoffice-cms/routes/shared/response-output";


const adminService = new AdminService();


export default requireBackofficeAccess(async (req: ApiRequest, res: Response) => {
  const idToken = getIdToken(req);
  const ret = await adminService.findById(idToken.uid);
  if (!ret) {
    res.status(204).end(); // no content
  } else {
    res.status(200).json(toAdminJson(ret));
  }
  return;
});
