import { MemberFindCriteria } from "@backoffice-cms/models/member";
import { MemberService } from "@backoffice-cms/services/member-service";
import { Page, Sort } from "@shared/services";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { toPageOfMembersJson } from "./shared/response-output";

const memberService = new MemberService();

const memberList: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }
  const sortQuery = req.query.sort?.toString().trim();
  const sortDirQuery = req.query.sortDir?.toString().trim() ?? 'asc';
  const sort = sortQuery
    ? new Sort(sortQuery, sortDirQuery === 'asc')
    : undefined;
  const inclDeleted = req.query['inclDeleted'] + "";
  const searchTerm = req.query['searchTerm']?.toString().trim();
  const page = new Page(+req.params.page, +req.params.pageSize);
  const ret = await memberService.findAll({
    searchTerm,
    inclDeleted: (inclDeleted === "true"),
  } as MemberFindCriteria, sort, page);
  res.status(200).json({ success: true, data: toPageOfMembersJson(ret) });
};

export default memberList; 