import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { BookingService } from "../services/booking-service";
import { toBooking<PERSON><PERSON> } from "./shared/response-output";

const bookingService = new BookingService();

const bookingGetById: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const bookingId = req.params.bookingId;
  const ret = await bookingService.findById(bookingId);
  if (!ret) {
    res.status(204).end(); // no content
  } else {
    res.status(200).json(toBookingJson(ret, { timezone: req.userTimezone }));
  }
};

export default bookingGetById;
