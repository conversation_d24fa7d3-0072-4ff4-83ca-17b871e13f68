import { InstructorSetActiveReq } from "@backoffice-cms/models/instructor";
import { InstructorService } from "@backoffice-cms/services/instructor-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";

const instructorService = new InstructorService();

const instructorSetDisabled: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const instructorId = req.params.instructorId + "";
  const disabled = req.params.disabled + "";
  await instructorService.setActive({
    instrId: instructorId,
    active: !(disabled === "true"),
    by: toBy(idToken),
  } as InstructorSetActiveReq);
  res.status(200).end();
};

export default instructorSetDisabled;
