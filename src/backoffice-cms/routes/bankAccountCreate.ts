import { Response } from "express";

import { BankService } from "@backoffice-cms/services/bank-account-service";
import { requireBackofficeAccess } from "@backoffice-cms/middlewares/backoffice-access";
import { ApiRequest, ENTITY_TYPES } from "@shared/constants";

const bankService = new BankService();

export default requireBackofficeAccess(async (req: ApiRequest, res: Response) => {
  const { auth: { boId } } = req;
  const result = await bankService.createBankAccount(req, ENTITY_TYPES.PLATFORM, boId);
  res.status(200).json({
    success: true,
    data: result
  });
});
