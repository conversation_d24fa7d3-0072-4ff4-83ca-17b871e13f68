import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { AdminService } from "../services/admin-service";
import { toAdmin<PERSON>son } from "./shared/response-output";

const adminService = new AdminService();

const adminGetById: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const adminId = req.params.adminId;
  const ret = await adminService.findById(adminId);
  if (!ret) {
    res.status(204).end(); // no content
  } else {
    res.status(200).json(toAdminJson(ret));
  }
};

export default adminGetById;
