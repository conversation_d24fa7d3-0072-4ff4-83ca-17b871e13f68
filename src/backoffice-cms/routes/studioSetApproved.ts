import { StudioSetActiveReq } from "@backoffice-cms/models/studio";
import { StudioService } from "@backoffice-cms/services/studio-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";

const studioService = new StudioService();

const studioSetApproved: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const studioId = req.params.studioId + "";
  await studioService.setApproved({
    studioId: studioId,
    by: toBy(idToken),
  } as StudioSetActiveReq);
  res.status(200).end();
};

export default studioSetApproved;
