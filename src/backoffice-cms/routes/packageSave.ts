import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { requireBackofficeAccess } from "../middlewares/backoffice-access";
import { PackageService } from "../services/package-service";
import { toPackageSaveReq } from "./shared/request-parsers";
import { getIdToken } from "../../init-openapi";

const packageService = new PackageService();

const packageSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const reqData = toPackageSaveReq(req.body, idToken);
  
  try {
    if (reqData.package.id) {
      // Update existing package
      const affectedRows = await packageService.update(reqData);
      if (affectedRows !== 1) {
        res.status(400).json({ message: "Package not found or no changes made" });
        return;
      }
      res.status(200).json({ message: "Package updated successfully" });
    } else {
      // Create new package
      const packageId = await packageService.create(reqData);
      res.status(200).json({ 
        message: "Package created successfully",
        packageId: packageId 
      });
    }
  } catch (error: any) {
    if (error.message?.includes("duplicate") || error.message?.includes("unique")) {
      res.status(409).json({ message: "Package with this name already exists" });
    } else {
      res.status(500).json({ message: error.message || "Internal server error" });
    }
  }
};

export default requireBackofficeAccess(packageSave); 