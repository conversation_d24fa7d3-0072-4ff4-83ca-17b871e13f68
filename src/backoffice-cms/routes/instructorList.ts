import { InstructorFindCriteria } from "@backoffice-cms/models/instructor";
import { InstructorService } from "@backoffice-cms/services/instructor-service";
import { Page, Sort } from "@shared/services";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { toPageOfInstructorsJson } from "./shared/response-output";

const instructorService = new InstructorService();

const instructorList: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const sortQuery = req.query.sort?.toString().trim();
  const sortDirQuery = req.query.sortDir?.toString().trim() ?? 'asc';
  const sort = sortQuery
    ? new Sort(sortQuery, sortDirQuery === 'asc')
    : undefined;

  const inclDisabled = req.query['inclDisabled'] + "";
  const page = new Page(+req.params.page, +req.params.pageSize);
  const ret = await instructorService.findAll({
    adminId: idToken.uid,
    inclDisabled: (inclDisabled === "true"),
  } as InstructorFindCriteria, sort, page);
  res.status(200).json(toPageOfInstructorsJson(ret));
};

export default instructorList;
