import { BookingFindCriteria } from "@backoffice-cms/models/booking";
import { BookingService } from "@backoffice-cms/services/booking-service";
import { Page, Sort } from "@shared/services";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { toPageOfBookingsJson } from "./shared/response-output";

const bookingService = new BookingService();

const bookingList: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const sortQuery = req.query.sort?.toString().trim();
  const sortDirQuery = req.query.sortDir?.toString().trim() ?? 'asc';
  const sort = sortQuery
    ? new Sort(sortQuery, sortDirQuery === 'asc')
    : undefined;

  const inclCancelled = req.query['inclCancelled'] + "";
  const pendingCancelReq = req.query['pendingCancelReq'] + "";

  const page = new Page(+req.params.page, +req.params.pageSize);
  const ret = await bookingService.findAll({
    adminId: idToken.uid,
    inclCancelled: (inclCancelled === "true"),
    pendingCancelReq: (pendingCancelReq === "true"),
  } as BookingFindCriteria, sort, page);
  res.status(200).json(toPageOfBookingsJson(ret, { timezone: req.userTimezone }));
};

export default bookingList;
