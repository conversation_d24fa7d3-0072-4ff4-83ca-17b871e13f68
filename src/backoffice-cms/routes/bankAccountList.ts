import { Response } from "express";

import { BankService } from "@backoffice-cms/services/bank-account-service";
import { requireBackofficeAccess } from "@backoffice-cms/middlewares/backoffice-access";
import { ApiRequest, ENTITY_TYPES } from "@shared/constants";

const bankService = new BankService();

export default requireBackofficeAccess(async (req: ApiRequest, res: Response) => {
  const { auth: { boId } } = req;
  const { items, meta } = await bankService.getBankAccountList(req, ENTITY_TYPES.PLATFORM, boId);
  const formOptions = await bankService.getListingOptions(req);
  res.status(200).json({
    success: true,
    data: items,
    meta: {
      ...meta,
      options: formOptions
    }
  });
});
