import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { PromotionService } from "@backoffice-cms/services/promotion-service";
import { toBy } from "@shared/request-parsers";
const promotionService = new PromotionService();

const promoCodeToggleStatus: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const { promoCodeId } = req.params;
  const { status } = req.body;

  try {
    await promotionService.togglePromoCodeStatus(Number(promoCodeId), status, toBy(idToken));
    res.status(200).json({ success: true, message: "Promo code status updated successfully" });
  } catch (err) {
    res.status(400).json({ success: false, message: err.message });
  }
};

export default promoCodeToggleStatus; 