import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { PromotionService } from "@backoffice-cms/services/promotion-service";
import { Promotion } from "@backoffice-cms/models/promotion";
import { toPromotionSaveReq } from "./shared/request-parsers";

const promotionService = new PromotionService();

const promotionCreate: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // Authorisation check
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  try {
    await promotionService.createPromotion(toPromotionSaveReq(req.body, idToken));
    res.status(200).json({ success: true, message: "Promotion created successfully" });
  } catch (err) {
    res.status(400).json({ success: false, message: err.message });
  }
};

export default promotionCreate;
