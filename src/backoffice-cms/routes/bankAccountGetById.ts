import { Response } from "express";

import { BankService } from "@backoffice-cms/services/bank-account-service";
import { requireBackofficeAccess } from "@backoffice-cms/middlewares/backoffice-access";
import { ApiRequest } from "@shared/constants";

const bankService = new BankService();

export default requireBackofficeAccess(async (req: ApiRequest, res: Response) => {
  const result = await bankService.getBankAccountById(req);
  res.status(200).json({
    success: true,
    data: result
  });
});
