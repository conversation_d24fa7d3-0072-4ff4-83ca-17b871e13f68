import { Request, Response } from "express";

import { requireBackofficeAccess } from "@backoffice-cms/middlewares/backoffice-access";
import { BankService } from "@backoffice-cms/services/bank-account-service";

const bankService = new BankService();

export default requireBackofficeAccess(async (req: Request, res: Response) => {
  const { items, meta } = await bankService.getBankList(req);
  res.status(200).json({
    success: true,
    data: items,
    meta
  });
});
