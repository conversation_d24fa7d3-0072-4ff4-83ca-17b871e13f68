import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { BookingService as BookingServiceBO } from "@backoffice-cms/services/booking-service";
import { SearchService } from "@booking-api/services/search-service";
import { StudioService } from "@backoffice-cms/services/studio-service";
import { LatLng, SearchAt, SearchCriteria } from "@booking-api/models/search";
import { formatDate } from "date-fns";
import { toSearchResultJson } from "@booking-api/routes/shared/response-output";
import { EquipmentService } from "@studio-cms/services/equipment-service";

const bookingServiceBO = new BookingServiceBO();
const searchService = new SearchService();
const studioService = new StudioService();
const equipmentService = new EquipmentService();

const bookingSearchRescheduleSlots: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }
  const bookingId = req.params.bookingId + "";
  const { date } = req.body;
  if (!date) {
    res.status(400).end("Missing date");
    return;
  }
  try {
    const booking = await bookingServiceBO.findById(bookingId);
    if (!booking) {
      throw new Error("Booking not found");
    }
    if (!booking.equipment?.id || !booking.studio?.id) {
      throw new Error("Booking missing equipment or studio");
    }

    const equipment = await equipmentService.findById(booking.equipment.id);
    if (!equipment) {
      throw new Error("Equipment not found");
    }

    const studio = await studioService.findById(booking.studio.id);
    if (!studio) {
      throw new Error("Studio not found");
    }

    const at = new SearchAt(date, '00:00');
    const criteria: SearchCriteria = {
      latLng: studio.latLng!,
      distance: 50,
      at,
      studioId: booking.studio.id,
      instrId: booking.instructor?.id,
      equipTypes: [equipment.type.id],
      equipId: equipment.id,
      instrLessOnly: false,
      faveStudioOnly: false,
      faveInstrOnly: false,
      preferredStudioFromFaveInstrOnly: false,
      byId: booking.member?.id,
      timezone: booking.timezone,
    };

    const results = await searchService.searchExtended(criteria);
    res.status(200).json({
      success: true,
      data: {
        extendStart: formatDate(results.extendStart, "yyyy-MM-dd HH:mm:ss"),
        extendEnd: formatDate(results.extendEnd, "yyyy-MM-dd HH:mm:ss"),
        results: results.results
          .filter(o => !!o.studio.instrLess || o.groups.length !== 0)
          .map(o => toSearchResultJson(o))
      }
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error instanceof Error ? error.message : "Unknown error"
    });
  }
};

export default bookingSearchRescheduleSlots; 