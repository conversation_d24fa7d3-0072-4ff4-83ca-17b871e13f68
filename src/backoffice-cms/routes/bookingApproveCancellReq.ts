import { BookingSetCancelledReq } from "@backoffice-cms/models/booking";
import { BookingCancelFindCriteria } from "@booking-api/models/booking";
import { BookingService as BookingServiceBO } from "@backoffice-cms/services/booking-service";
import { BookingService } from "@booking-api/services/booking-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import { EventCancelBy } from "@studio-cms/models/event";
import eventEmitter from "@utils/event-emitter";

const bookingServiceBO = new BookingServiceBO();
const bookingService = new BookingService();

const bookingApproveCancellReq: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const bookingId = req.params.bookingId + "";
  const found = await bookingService.findBookingCancel({
    eventId: bookingId,
    approved: false, // (!) important find only unapproved requests
  } as BookingCancelFindCriteria);
  if (!found) {
    res.status(404).end("booking cancel request not found");
    return;
  }
  const reqBy:any = EventCancelBy[found.reqBy];

  if (!Object.values(EventCancelBy).includes(reqBy)) {
    res.status(400).end("invalid request by");
    return;
  }
  const ret = await bookingServiceBO.setCancelled({
    reqBy: reqBy,
    bookingId: bookingId,
    userId: idToken.uid, // (!) important
    reason: found.reason,
    free: found.free,
    by: toBy(idToken),
  } as BookingSetCancelledReq, true); // approved
  res.status(200).end();

  const event = await bookingService.bookingById(bookingId);
  if(event) {
    eventEmitter.emit('session-cancelled', {
      by: idToken.email,
      id: bookingId,
      data: event,
    });
  }

};

export default bookingApproveCancellReq;
