import { Booking } from "@backoffice-cms/models/booking";
import { PageOfData } from "@shared/services";
import { formatDate } from "date-fns";
import { Admin, AdminLastAt } from "../../models/admin";
import { Instructor } from "../../models/instructor";
import { Studio } from "../../models/studio";
import { Package } from "@backoffice-cms/models/package";
import { EventCancelBy } from "@studio-cms/models/event";
import { SessionStatus } from "@booking-api/models/booking";
import { DateTime } from "luxon";
import { PromotionWithCounts, PromoCodeWithDetails } from "../../models/promotion";

////////////
// Studio //
////////////

export function toStudioJson(studio: Studio) {
  const facebook = studio.socialMedia?.facebook;
  const instagram = studio.socialMedia?.instagram;
  const website = studio.socialMedia?.website;
  const mainUser = studio.mainUser;
  return {
    ...studio.id && { id: studio.id },
    name: studio.name,
    descr: studio.descr,
    contactNo: studio.contactNo,
    email: studio.email,
    address: studio.address,
    placeId: studio.placeId,
    geocode: studio.geocode,
    ...studio.instrLess && { instrLess: true },
    usagePolicies: studio.usagePolicies,
    pictures: studio.pictures ?? [],
    socialMedia: {
      ...facebook && { facebook: facebook },
      ...instagram && { instagram: instagram },
      ...website && { website: website },
    },
    ...!studio.active && { isDisabled: true },
    ...studio.pendingApproval && { pendingApproval: true },
    ...studio.backofficeRemarks && { backofficeRemarks: studio.backofficeRemarks },
    ...studio.starred && { starred: true },
    ...mainUser && { mainUser: {
      name: mainUser.name,
      email: mainUser.email,
      jobTitle: mainUser.jobTitle,
      contactNo: mainUser.contactNo,
    }},
  }
}

export function toPageOfStudiosJson(page: PageOfData<Studio>) {
  return {
    totalItems: page.totalItems,
    totalPages: page.totalPages,
    items: page.items.map(o => toStudioJson(o)),
  };
}

////////////////
// Instructor //
////////////////

export function toInstructorJson(instr: Instructor) {
  return {
    ...instr.id && { id: instr.id },
    name: instr.name,
    ...instr.descr && { descr: instr.descr },
    ...instr.facebook && { facebook: instr.facebook },
    ...instr.instagram && { instagram: instr.instagram },
    ...instr.certifications && { certifications: instr.certifications },
    ...instr.specialisations && { specialisations: instr.specialisations },
    ...!instr.onboarded && { isDisabled: true },
    registeredAt: formatDate(instr.registeredAt, "yyyy-MM-dd HH:mm:ss"),
    pictures: instr.pictures ?? [],
    ...instr.pendingApproval && { pendingApproval: true },
    ...instr.backofficeRemarks && { backofficeRemarks: instr.backofficeRemarks },
    ...instr.starred && { starred: true },
  }
}

export function toPageOfInstructorsJson(page: PageOfData<Instructor>) {
  return {
    totalItems: page.totalItems,
    totalPages: page.totalPages,
    items: page.items.map(o => toInstructorJson(o)),
  };
}

/////////////
// Booking //
/////////////

export function toBookingJson(booking: Booking, { timezone }: { timezone: string }) {

  let reqByName = null;
  if (booking.cancelReq) {
    switch(booking.cancelReq.reqBy) {
      case EventCancelBy[EventCancelBy.Instructor]:
        reqByName = `${booking.instructor?.name}`;
        break; 
      case EventCancelBy[EventCancelBy.Studio]:
        reqByName = `${booking.studio.name}`;
        break;
    }
  }

  return {
    id: booking.id,
    refNo: booking.refNo,
    status: booking.status,
    paymentStatus: booking.paymentStatus,
    ...booking.fees && { fees: {
      items: booking.fees.items.map(item => ({
        name: item.name,
        price: Number(item.price).toFixed(2),
        credit: item.credit ? Number(item.credit).toFixed(0) : undefined,
        currency: item.currency,
        sign: item.sign,
      })),
      total: Number(booking.fees.total).toFixed(2),
      totalCredits: booking.fees.totalCredits ? Number(booking.fees.totalCredits).toFixed(0) : undefined,
      currency: booking.fees.currency,
    }},
    startDate: booking.startDate, // Already in YYYY-MM-DD format
    startTime: booking.startTime.substring(0, 5),
    endDate: booking.endDate, // Already in YYYY-MM-DD format
    endTime: booking.endTime.substring(0, 5),

    studio: {
      id: booking.studio.id,
      name: booking.studio.name,
    },
    ...booking.instructor && {instructor: { // (!) nullable
      id: booking.instructor.id,
      name: booking.instructor.name,
    }},
    member: {
      id: booking.member.id,
      displayName: booking.member.displayName,
      fullName: booking.member.fullName,
    },
    equipment: {
      id: booking.equipment.id,
      name: booking.equipment.name,
    },

    ...booking.cancelled && {
      cancelled: true,
      cancelledAt: booking.cancelledAt ? DateTime.fromFormat(booking.cancelledAt, "yyyy-MM-dd HH:mm", { zone: 'UTC' }).setZone(timezone).toFormat("yyyy-MM-dd HH:mm") : undefined,
      cancelledBy: booking.cancelledBy,
      cancelFree: booking.cancelFree,
      cancelReason: booking.cancelReason,
      cancelRefundedAmount: booking.cancelRefundedAmount,
    },
    ...booking.cancelReq && {
      cancelReq: {
        id: booking.cancelReq.id,
        reqBy: booking.cancelReq.reqBy,
        reqByName: reqByName,
        userId: booking.cancelReq.userId,
        free: booking.cancelReq.free,
        reason: booking.cancelReq.reason,
        approved: booking.cancelReq.approved,
        approvedAt: booking.cancelReq.approvedAt ? DateTime.fromFormat(booking.cancelReq.approvedAt, "yyyy-MM-dd HH:mm", { zone: 'UTC' }).setZone(timezone).toFormat("yyyy-MM-dd HH:mm") : undefined,
        approvedBy: booking.cancelReq.approvedBy,
        at: DateTime.fromFormat(booking.cancelReq.at, "yyyy-MM-dd HH:mm", { zone: 'UTC' }).setZone(timezone).toFormat("yyyy-MM-dd HH:mm")
      }
    },
    ...booking.noShow && { noShow: true },
    ...booking.noShowAt && { noShowAt: DateTime.fromFormat(booking.noShowAt, "yyyy-MM-dd HH:mm", { zone: 'UTC' }).setZone(timezone).toFormat("yyyy-MM-dd HH:mm") },
    ...booking.noShowRemarks && { noShowRemarks: booking.noShowRemarks },

    ...booking.backofficeRemarks && { backofficeRemarks: booking.backofficeRemarks },
    ...booking.starred && { starred: true },
    ...booking.updatedAt && { updatedAt: DateTime.fromFormat(booking.updatedAt, "yyyy-MM-dd HH:mm:ss", { zone: 'UTC' }).setZone(timezone).toFormat("yyyy-MM-dd HH:mm:ss") },
  }
}

export function toPageOfBookingsJson(page: PageOfData<Booking>, { timezone }: { timezone: string }) {
  return {
    totalItems: page.totalItems,
    totalPages: page.totalPages,
    items: page.items.map(o => toBookingJson(o, { timezone })),
  };
}

///////////
// Admin //
///////////

export function toAdminJson(admin: Admin) {
  return {
    id: admin.id,
    firebaseUid: admin.firebaseUid,
    firebaseEmail: admin.firebaseEmail,
    name: admin.name,
    internalNotes: admin.internalNotes?.trim() || '',
    lastAt: toAdminLastAtJson(admin.lastAt),
    ...admin.deleted && { deleted: true },
  }
}

function toAdminLastAtJson(lastAt: AdminLastAt) {
  return {
    at: formatDate(lastAt.at, "yyyy-MM-dd HH:mm:ss"),
    by: lastAt.by,
  }
}

export function toPageOfAdminsJson(page: PageOfData<Admin>) {
  return {
    totalItems: page.totalItems,
    totalPages: page.totalPages,
    items: page.items.map(o => toAdminJson(o)),
  };
}

///////////////
// Package   //
///////////////

export function toPackageJson(pkg: Package) {
  return {
    id: pkg.id,
    name: pkg.name,
    currency: pkg.currency,
    price: pkg.price,
    credits: pkg.credits,
    bonusCredits: pkg.bonusCredits,
    status: pkg.status,
    firstTimeOnly: pkg.firstTimeOnly,
    instructorOnly: pkg.instructorOnly,
    validFrom: pkg.validFrom,
    validTo: pkg.validTo,
    purchaseLimit: pkg.purchaseLimit,
    sequence: pkg.sequence,
    createdAt: pkg.createdAt,
    updatedAt: pkg.updatedAt,
    createdBy: pkg.createdBy,
    updatedBy: pkg.updatedBy,
  }
}

export function toPageOfPackagesJson(page: PageOfData<Package>) {
  return {
    totalItems: page.totalItems,
    totalPages: page.totalPages,
    items: page.items.map(o => toPackageJson(o)),
  };
}

///////////////
// Promotion //
///////////////

export function toPromotionJson(promotion: PromotionWithCounts) {
  return {
    id: promotion.id,
    name: promotion.name,
    description: promotion.description,
    type: promotion.type,
    value: promotion.value,
    start_date: formatDate(promotion.start_date, "yyyy-MM-dd HH:mm:ss"),
    end_date: formatDate(promotion.end_date, "yyyy-MM-dd HH:mm:ss"),
    per_user_limit: promotion.per_user_limit,
    global_limit: promotion.global_limit,
    status: promotion.status,
    code_count: promotion.code_count,
    applied_count: promotion.applied_count,
    created_at: formatDate(promotion.created_at, "yyyy-MM-dd HH:mm:ss"),
    updated_at: promotion.updated_at ? formatDate(promotion.updated_at, "yyyy-MM-dd HH:mm:ss") : undefined,
  };
}

export function toPageOfPromotionsJson(page: { data: PromotionWithCounts[], total: number, page: number, pageSize: number }) {
  return {
    totalItems: page.total,
    totalPages: Math.ceil(page.total / page.pageSize),
    items: page.data.map(o => toPromotionJson(o)),
  };
}

export function toPromoCodeJson(promoCode: PromoCodeWithDetails) {
  return {
    id: promoCode.id,
    promotion_id: promoCode.promotion_id,
    code: promoCode.code,
    code_limit: promoCode.code_limit,
    assigned_user_id: promoCode.assigned_user_id,
    assigned_user_fullname: promoCode.assigned_user_fullname,
    status: promoCode.status,
    applied_count: promoCode.applied_count,
    created_at: formatDate(promoCode.created_at, "yyyy-MM-dd HH:mm:ss"),
    updated_at: promoCode.updated_at ? formatDate(promoCode.updated_at, "yyyy-MM-dd HH:mm:ss") : undefined,
  };
}

export function toPageOfPromoCodesJson(page: { data: PromoCodeWithDetails[], total: number, page: number, pageSize: number }) {
  return {
    totalItems: page.total,
    totalPages: Math.ceil(page.total / page.pageSize),
    items: page.data.map(o => toPromoCodeJson(o)),
  };
}

export function toMemberJson(member) {
  return {
    id: member.id,
    firebaseUid: member.firebaseUid,
    firebaseEmail: member.firebaseEmail,
    fullName: member.fullName,
    displayName: member.displayName,
    gender: member.gender,
    mobileNo: member.mobileNo,
    registeredAt: member.registeredAt,
    ...member.deleted && { deleted: true },
  };
}

export function toPageOfMembersJson(page) {
  return {
    totalItems: page.totalItems,
    totalPages: page.totalPages,
    items: page.items.map(o => toMemberJson(o)),
  };
}

/*
//////////
// User //
//////////

export function toUserJson(user: User) {
  const active = !!user.active;
  return {
    id: user.id,
    email: user.firebaseEmail,
    name: user.name,
    jobTitle: user.jobTitle,
    contactNo: user.contactNo,
    ...user.admin && { isAdmin: true },
    ...!active && { isDisabled: true },
    ...user.emailVerified && { isEmailVerified: true },
  }
}

export function toPageOfUsersJson(page: PageOfData<User>) {
  return {
    totalItems: page.totalItems,
    totalPages: page.totalPages,
    items: page.items.map(o => toUserJson(o)),
  };
}

///////////////
// Equipment //
///////////////

export function toEquipmentTypeJson(type: EquipmentType) {
  return {
    id: type.id,
    name: type.name,
  }
}

// (!) simple
export function toEquipmentSimpleJson(equip: EquipmentSimple) {
  return {
    id: equip.id,
    type: {
      id: equip.type.id,
      name: equip.type.name,
    },
    code: equip.code,
    shortName: equip.shortName,
    name: equip.name,
    ...equip.deleted && { deleted: true },
  }
}

export function toEquipmentJson(equip: Equipment) {
  return {
    id: equip.id,
    type: {
      id: equip.type.id,
      name: equip.type.name,
    },
    code: equip.code,
    shortName: equip.shortName,
    name: equip.name,
    ...equip.deleted && { deleted: true },
    // ...
    color: equip.color,
    descr: equip.descr,
    privacy: equip.privacy,
    startDate: !equip.startDate ? null : equip.startDate, // Already in YYYY-MM-DD format
    endDate: !equip.endDate ? null : equip.endDate, // Already in YYYY-MM-DD format
    availability: toAvailabilityJson(equip.availability),
  }
}

export function toPageOfEquipmentJson(page: PageOfData<Equipment>) {
  return {
    totalItems: page.totalItems,
    totalPages: page.totalPages,
    items: page.items.map(o => toEquipmentJson(o)),
  };
}

export function toAvailabilityJson(avail: Availability[]) {
  // @ts-ignore
  const ret = [];
  for (const o of avail) {
    const slots = o.slots;
    ret.push({
      type: o.type,
      slots: slots.map(slot => ({
        start: slot.start.substring(0, 5),
        end: slot.end.substring(0, 5),
        price: slot.price,
      })),
    });
  }
  return ret;
}

///////////
// Event //
///////////

export function toEventJson(event: Event, {
  includeInternalRemarks = false,
}) {
  const session = event.session;
  const studio = event.studio;
  return {
    id: event.id,
    studioId: event.studioId,
    ...studio && { studio: {
      name: studio.name,
      address: studio.address,
      placeId: studio.placeId,
      lat: studio.lat,
      lng: studio.lng,
    } },
    type: event.type,
    ...event.bookingRefNo && { bookingRefNo: event.bookingRefNo },
    ...event.bookingStatus && { bookingStatus: event.bookingStatus },
    ...event.name && { name: event.name },
    ...(includeInternalRemarks && event.internalRemarks) && { internalRemarks: event.internalRemarks },
    ...event.bookingFees && { bookingFeesTotal: event.bookingFees.total },
    startDate: event.startDate, // Already in YYYY-MM-DD format
    ...event.startTime && { startTime: event.startTime.substring(0, 5) },
    endDate: event.endDate, // Already in YYYY-MM-DD format
    ...event.endTime && { endTime: event.endTime.substring(0, 5) },
    ...event.isFullDay && { isFullDay: true },
    color: event.color,
    ...event.equipmentId && { equipmentId: event.equipmentId },
    ...event.equipment && { equipment: {
      shortName: event.equipment.shortName,
      name: event.equipment.name,
      privacy: event.equipment.privacy ?? '',
    } },
    ...event.instructorId && { instructorId: event.instructorId },
    ...event.instructor && { instructor: {
      fullName: event.instructor.fullName,
    } },
    ...event.memberId && { memberId: event.memberId },
    ...event.member && { member: {
      fullName: event.member.fullName,
    } },
    // cancellable: ...,
    ...event.cancelled && { cancelled: true },
    ...event.cancelledById && { cancelledById: event.cancelledById },
    ...event.cancelReason && { cancelReason: event.cancelReason },
    //
    ...event.instrNoShow && { instrNoShow: true },
    //
    ...event.session?.completed && { completed: true },
    ...event.session?.hasMemberFeedback && { hasMemberFeedback: true },
    ...event.session?.memberFeedback && { memberFeedback: event.session.memberFeedback },
    ...event.session?.memberNoFeedback && { memberNoFeedback: true },
    //
    ...session && {
      notes: {
        sessionState: session.notes.state,
        sessionTarget: session.notes.target,
      },
      ...session.record && {
        record: {
          focus: session.record.focus ?? '',
          exercises: session.record.exercises.map(o => toExerciseRecordJson(o)),
          feedback: session.record.feedback ?? '',
        },
      },
      ...session.completed && { completed: true },
    },
  }
}

export function toExerciseRecordJson(exercise: ExerciseRecord) {
  return {
    ...exercise.category && {
      category: {
        id: exercise.category.id,
        name: exercise.category.name,
      },
    },
    ...exercise.exercise && {
      exercise: {
        id: exercise.exercise.id,
        name: exercise.exercise.name,
      },
    },
    ...exercise.modification && {
      modification: {
        id: exercise.modification.id,
        name: exercise.modification.name,
      },
    },
    spring: exercise.spring ?? '',
    reps: exercise.reps ?? 0,
    duration: exercise.duration ?? '00:00',
    stabilityRating: exercise.stabilityRating ?? 0,
    formRating: exercise.formRating ?? 0,
  }
}

export function toScheduleJson(events: Event[]) {
  return {
    numBookings: events
      .filter(o => o.type === 'Booking' && !o.cancelled)
      .length,
    events: events.map(o => toEventJson(o, {
      includeInternalRemarks: true,
    })),
  };
}
*/
