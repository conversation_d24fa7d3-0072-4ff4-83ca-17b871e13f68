import { toBy } from "@shared/request-parsers";
import { auth } from "firebase-admin";
import { Admin, AdminSaveReq } from "../../models/admin";
import { Package, PackageSaveReq, PackageSetStatusReq, PackageMoveSequenceReq } from "../../models/package";
import DecodedIdToken = auth.DecodedIdToken;
import { Promotion, PromotionSaveReq, PromoCodeSaveReq, PromoCodeAssignMemberReq } from "@backoffice-cms/models/promotion";

export function toAdminSaveReq(
  body: any, idToken: DecodedIdToken
): AdminSaveReq {
  const admin = {
    id: body.id,
    name: body.name.trim(),
    internalNotes: body.internalNotes?.trim() || '',
  } as Admin;
  return {
    admin: admin,
    by: toBy(idToken),
  } as AdminSaveReq;
}

export function toPackageSaveReq(
  body: any, idToken: DecodedIdToken
): PackageSaveReq {
  const packageData = {
    id: body.id,
    name: body.name.trim(),
    currency: body.currency.trim(),
    price: +body.price,
    credits: +body.credits,
    bonusCredits: +body.bonusCredits || 0,
    status: body.status ?? true,
    firstTimeOnly: body.firstTimeOnly ?? false,
    instructorOnly: body.instructorOnly ?? false,
    validFrom: body.validFrom || null,
    validTo: body.validTo || null,
    purchaseLimit: body.purchaseLimit ? +body.purchaseLimit : null,
    sequence: body.sequence ? +body.sequence : null,
  } as Package;
  return {
    package: packageData,
    by: toBy(idToken),
  } as PackageSaveReq;
}

export function toPackageSetStatusReq(
  body: any, idToken: DecodedIdToken
): PackageSetStatusReq {
  return {
    packageId: body.packageId,
    status: body.status,
    by: toBy(idToken),
  } as PackageSetStatusReq;
}

export function toPackageMoveSequenceReq(
  body: any, idToken: DecodedIdToken
): PackageMoveSequenceReq {
  return {
    packageId: body.packageId,
    direction: body.direction,
    by: toBy(idToken),
  } as PackageMoveSequenceReq;
}

export function toPromotionSaveReq(
  body: any, idToken: DecodedIdToken
): PromotionSaveReq {
  const promotion = {
    name: body.name,
    description: body.description,
    type: body.type,
    value: body.value,
    start_date: body.start_date,
    end_date: body.end_date,
    per_user_limit: body.per_user_limit,
    global_limit: body.global_limit,
    status: body.status,
  } as Promotion;
  return {
    promotion: promotion,
    by: toBy(idToken),
  } as PromotionSaveReq;
}

export function toPromoCodeSaveReq(
  body: any, promotionId: number, idToken: DecodedIdToken
): PromoCodeSaveReq {
  return {
    promotionId,
    code: body.code,
    count: body.count,
    prefix: body.prefix,
    code_limit: body.code_limit,
    by: toBy(idToken),
  } as PromoCodeSaveReq;
}

export function toPromoCodeAssignMemberReq(body: any, idToken: any): PromoCodeAssignMemberReq {
  return {
    promoCodeId: Number(body.promoCodeId),
    memberId: body.memberId,
    by: { uid: idToken.uid, username: idToken.name || idToken.email },
  };
}

/*
export function toStudioSaveReq(
  body: any, idToken: DecodedIdToken
): StudioSaveReq {
  const studio = {
    id: '', // (!) default empty
    name: body.name,
    descr: body.descr,
    contactNo: body.contactNo,
    email: body.email,
    address: body.address,
    placeId: body.placeId,
    usagePolicies: body.usagePolicies,
    instrLess: body.instrLess ?? false,
    pictures: body.pictures ?? [],
    socialMedia: body.socialMedia ?? {},
  } as Studio;
  return {
    studio: studio,
    by: toBy(idToken),
  } as StudioSaveReq;
}

// (!) Intended for self
export function toSelfSaveReq(
  body: any, idToken: DecodedIdToken
): UserSaveReq {
  const user = {
    id: idToken.uid,          // (!) self
    firebaseUid: idToken.uid, // (!) self
    firebaseEmail: idToken.email,
    studioId: '',             // (!) default empty
    name: body.name,
    jobTitle: body.jobTitle,
    contactNo: body.contactNo,
  } as User;
  return {
    user: user,
    by: toBy(idToken),
  } as UserSaveReq;
}

// (!) Intended for manage users (by admin user)
export function toUserSaveReq(
  body: any, idToken: DecodedIdToken
): UserSaveReq {
  const user = {
    id: body.id,
    firebaseUid: '', // (!) default empty
    firebaseEmail: body.email,
    studioId: '',    // (!) default empty
    name: body.name,
    jobTitle: body.jobTitle,
    contactNo: body.contactNo,
    admin: body.isAdmin ?? false,
    active: !(body.isDisabled ?? false),
  } as User;
  return {
    user: user,
    by: toBy(idToken),
  } as UserSaveReq;
}

export function toEquipmentSaveReq(
  body: any, idToken: DecodedIdToken
): EquipmentSaveReq {
  const equipment = {
    id: body.id,
    type: {
      id: body.type.id,
      name: body.type.name,
    } as EquipmentType,
    color: body.color,
    code: body.code,
    shortName: body.shortName,
    name: body.name,
    descr: body.descr,
    privacy: body.privacy,
    startDate: body.startDate,
    endDate: body.endDate,
    availability: !body.availability ? [] : toAvailabilityJson(body.availability),
  } as Equipment;
  return {
    equipment: equipment,
    by: toBy(idToken),
  } as EquipmentSaveReq;
}

export function toAvailabilitySaveReq(
  body: any, idToken: DecodedIdToken
): AvailabilitySaveReq {
  const map: any = {};
  for (const avail of body) {
    const type = avail.type;
    const slots = avail.slots;
    for (const o of slots) {
      const slot = {
        start: o['start'],
        end: o['end'],
        price: o['price'],
      } as AvailabilitySlot;

      const val = map[type];
      if (val) {
        val.push(slot);
      } else {
        map[type] = [slot];
      }
    }
  }

  const availability: Availability[] = [];
  Object.keys(map).forEach((key: string) => {
    availability.push({
      type: key,
      slots: map[key] as AvailabilitySlot[],
    } as Availability);
  });

  return {
    availability: availability,
    by: toBy(idToken),
  } as AvailabilitySaveReq;
}

export function toEventSaveReq(
  body: any, idToken: DecodedIdToken
): EventSaveReq {
  const event = {
    id: body.id,
    type: body.type,
    name: body.name,
    internalRemarks: body.internalRemarks,
    startDate: body.startDate,
    startTime: body.startTime,
    endDate: body.endDate,
    endTime: body.endTime,
    isFullDay: body.isFullDay,
    color: body.color,
    equipmentId: body.equipmentId,
  } as Event;
  return {
    event: event,
    by: toBy(idToken),
  } as EventSaveReq;
}
*/
