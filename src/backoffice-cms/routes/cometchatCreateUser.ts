
import { createCometChatUser } from "@utils/cometchat";
import logger from "@utils/logger";
import { CometChatUser } from "@public-api/models/cometchat";
import { Response } from "express";
import { ApiRequest } from "@shared/constants";
import { requireBackofficeAccess } from "@backoffice-cms/middlewares/backoffice-access";


export default requireBackofficeAccess(async (req: ApiRequest, res: Response) => {
  const { auth: { boId } } = req;
  const result = await createCometChatUser({
    uid: boId,
    email: "<EMAIL>",
    name: "ViFit Admin",
    tags: ["backoffice"],
    userType: "backoffice",
  } as Comet<PERSON>hatUser );
  if (!result.success) {
    logger.warn(`[CometChat] Studio user creation failed for studioId=${boId}: ${result.message}`);
    res.status(500).json(result);
    return;
  }
  res.status(200).json(result);
  return;
});