import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { requireBackofficeAccess } from "../middlewares/backoffice-access";
import { PackageService } from "../services/package-service";
import { toPackageSetStatusReq } from "./shared/request-parsers";
import { getIdToken } from "../../init-openapi";

const packageService = new PackageService();

const packageSetStatus: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const reqData = toPackageSetStatusReq(req.body, idToken);
  
  try {
    const affectedRows = await packageService.setStatus(reqData);
    if (affectedRows !== 1) {
      res.status(400).json({ message: "Package not found" });
      return;
    }
    
    const statusText = reqData.status ? "activated" : "deactivated";
    res.status(200).json({ message: `Package ${statusText} successfully` });
  } catch (error: any) {
    res.status(500).json({ message: error.message || "Internal server error" });
  }
};

export default requireBackofficeAccess(packageSetStatus); 