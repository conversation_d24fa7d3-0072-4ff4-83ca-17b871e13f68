import { InstructorSetActiveReq } from "@backoffice-cms/models/instructor";
import { InstructorService } from "@backoffice-cms/services/instructor-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken, isAuthorisedForBackoffice } from "../../init-openapi";
import eventEmitter from "@utils/event-emitter";

const instructorService = new InstructorService();

const instructorSetApproved: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // (!) preconditions for backoffice
  const authorised = await isAuthorisedForBackoffice(idToken);
  if (!authorised) {
    res.status(401).end("not authorised to access backoffice");
    return;
  }

  const instructorId = req.params.instructorId + "";
  const ret = await instructorService.setApproved({
    instrId: instructorId,
    by: toBy(idToken),
  } as InstructorSetActiveReq);
  res.status(200).end();

  if(ret) {
    eventEmitter.emit('instructor-approved', {
      by: idToken.email,
      id: instructorId,
    });
  }
};

export default instructorSetApproved;
