import { Response } from "express";

import { BankService } from "@backoffice-cms/services/bank-account-service";
import { ApiRequest } from "@shared/constants";
import { requireBackofficeAccess } from "@backoffice-cms/middlewares/backoffice-access";

const bankService = new BankService();

export default requireBackofficeAccess(async (req: ApiRequest, res: Response) => {
  const result = await bankService.updateBankAccountStatus(req);
  res.status(200).json({
    success: true,
    data: result
  });
});
