import {
  Booking,
  BookingFindCriteria,
  BookingSaveRemarksReq,
  BookingSetCancelledReq,
  BookingSetStarredReq,
} from "@backoffice-cms/models/booking";
import { Page, PageOfData, Sort } from "@shared/services";
import pool from "../../init-pool";
import { BookingRepo } from "../repo/booking-repo";
import { EventRepo } from "@studio-cms/repo/event-repo";
import { CreditRepo } from "@booking-api/repo/credit-repo";
import { CreditRepo as MemCreditRepo } from "@member-api/repo/credit-repo";
import { Transaction, TransactionSaveReq, TransactionStatus, TransactionType } from "@booking-api/models/credit";
import logger from "@utils/logger";

export class BookingService {

  private bookingRepo = new BookingRepo();
  private creditRepo = new CreditRepo();
  private memberCreditRepo = new MemCreditRepo();
  private eventRepo = new EventRepo();

  async findById(id: string): Promise<Booking|undefined> {
    return this.bookingRepo.findById(pool, id);
  }

  async findAll(
    criteria: BookingFindCriteria, sort?: Sort, page?: Page
  ): Promise<PageOfData<Booking>> {
    return this.bookingRepo.findAll(pool, criteria, sort, page);
  }

  async getPendingCancelCount(): Promise<number> {
    return this.bookingRepo.getPendingCancelCount(pool);
  }

  async setCancelled(req: BookingSetCancelledReq, approval: boolean|undefined = false): Promise<number> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      const ret = await this.bookingRepo.setCancelled(conn, req);
      // check have updated row only proceed to handle credit deduction
      if(ret > 0) {
        let cancelReq: number | undefined = undefined;
        approval && (cancelReq = await this.bookingRepo.setCancelledApproved(conn, req));
        const transaction = await this.creditRepo.getTrxByEventId(conn, req.bookingId);
        if(transaction) {
          const booking = await this.bookingRepo.findById(conn, req.bookingId);
          if (booking?.fees?.totalCredits) {
            let refundCredits = +booking.fees.totalCredits;

            // Check if not free to cancel
            if(!req.free) {
              const penatlyFeePercent = await this.eventRepo.findCancellationFee(conn);
              penatlyFeePercent > 0 && (refundCredits -= (refundCredits * penatlyFeePercent/100));
            }

            if(req.refundCredits > 0) {
              refundCredits = req.refundCredits;
            }

            // Proceed if refundCredits is not empty
            if(refundCredits > 0) {
              // 1. Update cancelled_with_refund flag
              await this.eventRepo.updateCancelledWithRefund(conn, req.bookingId, true, req.by.username);

              // 2. Create Refund Transaction Record
              const transaction = {
                memberId: booking.member.id,
                type: TransactionType.Refund,
                amount: refundCredits,
                status: TransactionStatus.Success,
                eventId: req.bookingId
              } as Transaction;

              await this.creditRepo.createTrx(conn, {
                  transaction: transaction,
                  by: req.by,
              } as TransactionSaveReq);

              // 3 . Update Member Credits (Refund Back Booking's Credits)
              await this.memberCreditRepo.updateMemberCredit(conn, booking.member.id, Math.abs(refundCredits), req.by.username);
            }
          }
        }
      }

      await conn.commit();
      return ret;

    } catch (e) {
      logger.error(`setCancelled error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async saveRemarks(req: BookingSaveRemarksReq): Promise<number> {
    return this.bookingRepo.saveRemarks(pool, req);
  }

  async setStarred(req: BookingSetStarredReq): Promise<number> {
    return this.bookingRepo.setStarred(pool, req);
  }
}
