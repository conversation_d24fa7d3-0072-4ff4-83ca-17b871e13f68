import {
  Admin,
  AdminFindCriteria,
  AdminSaveRemarksReq,
  AdminSaveReq,
  AdminSetDeletedReq
} from "@backoffice-cms/models/admin";
import { Page, PageOfData, ServiceError, Sort } from "@shared/services";
import logger from "@utils/logger";
import { FirebaseAuthBackOffice } from "../../init-firebase";
import pool from "../../init-pool";
import { AdminRepo } from "../repo/admin-repo";

export class AdminService {

  private adminRepo = new AdminRepo();

  async findById(id: string): Promise<Admin|undefined> {
    return this.adminRepo.findById(pool, id);
  }

  async findByFirebaseUid(id: string): Promise<Admin|undefined> {
    return this.adminRepo.findByFirebaseUid(pool, id);
  }

  async findAll(
    criteria: AdminFindCriteria, sort?: Sort, page?: Page
  ): Promise<PageOfData<Admin>> {
    return this.adminRepo.findAll(pool, criteria, sort, page);
  }

  async create(req: AdminSaveReq): Promise<string> {
    const email = req.admin.firebaseEmail;
    const found = await this.adminRepo.findByFirebaseEmail(pool, email);
    if (found) {
      logger.info(`admin (email=${email}) already exists!`);
      throw new ServiceError("409", "admin already exists");
    }

    // (!) network i/o, do NOT do inside SQL transaction
    await this.addBackofficeClaim(email, req.by.username);

    return this.adminRepo.create(pool, req);
  }

  async update(req: AdminSaveReq): Promise<number> {
    return this.adminRepo.update(pool, req);
  }

  async setDeleted(req: AdminSetDeletedReq): Promise<number> {
    const user = await this.findById(req.adminId);
    if (!user) {
      logger.info(`admin (id=${req.adminId}) not found!`);
      throw new ServiceError("409", "Admin record not found");
    }

    // (!) network i/o, do NOT do inside SQL transaction
    if (req.deleted) {
      await this.removeBackofficeClaim(user.firebaseEmail!, req.by.username);
    } else {
      await this.addBackofficeClaim(user.firebaseEmail!, req.by.username);
    }

    return this.adminRepo.setDeleted(pool, req);
  }

  private async addBackofficeClaim(email: string, by: string): Promise<void> {
    const user = await FirebaseAuthBackOffice.getUserByEmail(email);
    const customClaims = user.customClaims ?? {};
    const hasClaim = customClaims.backoffice === true;
    if (!hasClaim) {
      customClaims.backoffice = true;
      await FirebaseAuthBackOffice.setCustomUserClaims(user.uid, customClaims);
      logger.info(`[${by}] addBackofficeClaim(${email}) customClaims=${JSON.stringify(customClaims)}`);
    } else {
      logger.info(`[${by}] addBackofficeClaim(${email}) already has backoffice custom claim`);
    }
  }

  private async removeBackofficeClaim(email: string, by: string): Promise<void> {
    const user = await FirebaseAuthBackOffice.getUserByEmail(email);
    const customClaims = user.customClaims ?? {};
    const hasClaim = customClaims.backoffice === true;
    if (hasClaim) {
      delete customClaims.backoffice;
      await FirebaseAuthBackOffice.setCustomUserClaims(user.uid, customClaims);
      logger.info(`[${by}] removeBackofficeClaim(${email}) customClaims=${JSON.stringify(customClaims)}`);
    } else {
      logger.info(`[${by}] removeBackofficeClaim(${email}) already does not have backoffice custom claim`);
    }
  }

  async saveRemarks(req: AdminSaveRemarksReq): Promise<number> {
    return this.adminRepo.saveRemarks(pool, req);
  }
}
