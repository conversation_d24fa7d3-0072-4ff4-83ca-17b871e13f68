import { BankRepo } from "@backoffice-cms/repo/bank-account-repo";
import { CountryRepo } from "@backoffice-cms/repo/country-repo";
import { Bank } from "@database/models/bank";
import { BankAccount, BankAccountEntityTypeValue, BankAccountWithBank, FormOption, ListingOption } from "@database/models/bank-account";
import { ApiRequest, STATUSES, StatusValue } from "@shared/constants";
import { ServiceError, PaginationData, getPaginationQuery } from "@shared/services";
import { withTransaction } from "@utils/db-transaction";

const { ACTIVE } = STATUSES;

export class BankService {

  private bankRepo = new BankRepo();
  private countryRepo = new CountryRepo();

  async getBankList(req: ApiRequest): Promise<PaginationData<Bank>> {
    const { params, query } = req;
    const { countryCode, inclInactive } = query;

    const { pageQuery, sortQuery } = getPaginationQuery(params);

    const status = inclInactive === 'true' ? null : ACTIVE;

    const { items, totalItems, totalPages } = await this.bankRepo.getBankList({
      status,
      countryCode: countryCode?.toString()
    }, pageQuery, sortQuery);

    return {
      items,
      meta: {
        totalItems,
        totalPages,
      }
    };
  }

  async getBankAccountList(req: ApiRequest, entityType: BankAccountEntityTypeValue, entityId: BankAccount['entityId']): Promise<PaginationData<BankAccountWithBank>> {
    const { params, query } = req;
    const { status: filterStatus, bankId, searchTerm } = query;

    const { pageQuery, sortQuery } = getPaginationQuery(params);

    const status = filterStatus ? filterStatus.toString() as StatusValue : null;

    const { items, totalItems, totalPages } = await this.bankRepo.getBankAccountList({
      status,
      entityType,
      entityId,
      bankId: bankId?.toString(),
      searchTerm: searchTerm?.toString(),
    }, pageQuery, sortQuery);

    return {
      items,
      meta: {
        totalItems,
        totalPages,
      }
    };
  }

  async createBankAccount(req: ApiRequest, entityType: BankAccountEntityTypeValue, entityId: BankAccount['entityId']): Promise<BankAccount> {
    const { idToken: { uid } } = req;
    const { body: { bankId, accountNo, accountName, primary, swiftCode } } = req;

    const duplicate = await this.bankRepo.checkDuplicateBankAccount(
      entityType,
      entityId,
      bankId,
      accountNo
    );
    if (duplicate) {
      throw new ServiceError("409", `Bank account with account number '${accountNo}' already exists`);
    }

    const data = {
      entityType,
      entityId,
      bankId,
      accountNo,
      accountName,
      primary,
      swiftCode
    };

    return await withTransaction(async (transaction) => {
      return await this.bankRepo.createBankAccount(data, {
        authId: uid,
        transaction
      });
    });
  }

  async getBankAccountById(req: ApiRequest): Promise<BankAccountWithBank | null> {
    const { params } = req;
    const { id } = params;

    const result = await this.bankRepo.getBankAccountById(id);
    if (!result) {
      throw new ServiceError("404", "Bank account not found");
    }

    return result;
  }

  async updateBankAccount(req: ApiRequest, entityType: BankAccountEntityTypeValue, entityId: BankAccount['entityId']): Promise<boolean> {
    const { body: { bankId, accountNo, accountName, primary, swiftCode }, idToken: { uid }, params } = req;
    const { id } = params;

    const duplicate = await this.bankRepo.checkDuplicateBankAccount(
      entityType,
      entityId,
      bankId,
      accountNo,
      id
    );
    if (duplicate) {
      throw new ServiceError("409", `Bank account with account number '${accountNo}' already exists`);
    }

    const existingAccount = await this.getBankAccountById(req);

    const updateData = {
      bankId,
      accountNo,
      accountName,
      primary,
      swiftCode
    };

    await withTransaction(async (transaction) => {
      return await this.bankRepo.updateBankAccountById(existingAccount, updateData, {
        authId: uid,
        transaction
      });
    });

    return true;
  }

  async updateBankAccountStatus(req: ApiRequest): Promise<boolean> {
    const { body: { status }, idToken: { uid } } = req;

    const existingAccount = await this.getBankAccountById(req);

    const updateData = {
      status
    };

    await withTransaction(async (transaction) => {
      return await this.bankRepo.updateBankAccountStatusById(existingAccount, updateData, {
        authId: uid,
        transaction
      });
    });

    return true;
  }

  async getFormOptions(req: ApiRequest): Promise<FormOption> {
    const { auth } = req;
    const bankOptions = await this.bankRepo.getBankOption(auth?.countryCode);
    const countryOptions = await this.countryRepo.getCountryOption();
    return {
      bankOptions,
      countryOptions,
    };
  }

  async getListingOptions(req: ApiRequest): Promise<ListingOption> {
    const { auth } = req;
    const bankOptions = await this.bankRepo.getBankOption(auth?.countryCode, true);
    return {
      bankOptions,
    };
  }
}
