import pool from "../../init-pool";
import { CountryRepo } from "../repo/country-repo";
import { Country, CountryOption, CurrencyOption } from "@database/models/country";

export class CountryService {
  private countryRepo = new CountryRepo();

  async getCountryOption(): Promise<CountryOption[]> {
    return await this.countryRepo.getCountryOption();
  }

  async getCurrencyOptions(): Promise<CurrencyOption[]> {
    return await this.countryRepo.getCurrencyOptions();
  }

  async getAllCountries(): Promise<Country[]> {
    return await this.countryRepo.getCountryList();
  }

}