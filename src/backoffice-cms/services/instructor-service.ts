import {
  Instructor,
  InstructorFindCriteria,
  InstructorSaveRemarksReq,
  InstructorSetActiveReq,
  InstructorSetApprovedReq,
  InstructorSetStarredReq,
} from "@backoffice-cms/models/instructor";
import { Page, PageOfData, Sort } from "@shared/services";
import logger from "@utils/logger";
import pool from "../../init-pool";
import { InstructorRepo } from "../repo/instructor-repo";

export class InstructorService {

  private instructorRepo = new InstructorRepo();

  async findById(id: string): Promise<Instructor|undefined> {
    return this.instructorRepo.findById(pool, id);
  }

  async findAll(
    criteria: InstructorFindCriteria, sort?: Sort, page?: Page
  ): Promise<PageOfData<Instructor>> {
    return this.instructorRepo.findAll(pool, criteria, sort, page);
  }

  /*
  private async setInstructorGeocode(instructor: Instructor) {
    if (!instructor.placeId.trim()) {
      return; // nothing else to do.
    }
    instructor.geocode = await geocode(instructor.placeId);
  }

  async register(userSave: UserSaveReq, instructorSave: InstructorSaveReq): Promise<void> {
    // check if email exists
    const user = await this.userRepo.findByFirebaseEmail(pool, userSave.user.firebaseEmail);
    if (user) {
      logger.info(`email ${userSave.user.firebaseEmail} already exists`);
      throw new ServiceError("409", "email already exists");
    }

    // (!) Network I/O. DO NOT do this within SQL transaction.
    await this.setInstructorGeocode(instructorSave.instructor);

    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // create instructor
      const instructorId = await this.instructorRepo.create(conn, instructorSave);
      userSave.user.instructorId = instructorId;
      userSave.user.admin = true;   // (!) main user is always admin
      userSave.user.active = false; // (!) active = false by default
      // create main user
      const userId = await this.userRepo.create(conn, userSave);

      await conn.commit();
      logger.info(`register() success, instructorId=${instructorId}, mainUserId=${userId}`);
    } catch (e) {
      logger.error(`register error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async update(req: InstructorSaveReq): Promise<number> {
    const instructorId = req.instructor.id;
    const found = await this.instructorRepo.findById(pool, instructorId);
    if (!found) {
      logger.info(`instructor (id=${instructorId}) not found!`);
      throw new ServiceError("204", "instructor not found");
    }

    if (found.placeId !== req.instructor.placeId) {
      // (!) Network I/O. DO NOT do this within SQL transaction.
      await this.setInstructorGeocode(req.instructor);
    } else {
      req.instructor.geocode = found.geocode;
    }

    return this.instructorRepo.update(pool, req);
  }

  async insertPicture(req: PictureSaveReq): Promise<string> {
    return this.instructorRepo.insertPicture(pool, req);
  }

  async findPictureById(id: string): Promise<Picture|undefined> {
    return this.instructorRepo.findPictureById(pool, id);
  }
  */

  async setActive(req: InstructorSetActiveReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      const hasPubRecord = await this.instructorRepo.hasPubRecord(conn, req.instrId);
      if (hasPubRecord) {
        await this.instructorRepo.setLiveActive(conn, req);
      }
      await this.instructorRepo.setActive(conn, req);

      await conn.commit();
      logger.info(`setActive() (instr) success`);
    } catch (e) {
      logger.error(`setActive() (instr) error! ${JSON.stringify(e)}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async setApproved(req: InstructorSetApprovedReq): Promise<boolean> {
    let conn = null;
    let approved = false;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      const hasPubRecord = await this.instructorRepo.hasPubRecord(conn, req.instrId);
      if (!hasPubRecord) {
        await this.instructorRepo.copyToLive_insert(conn, req);
      } else {
        await this.instructorRepo.copyToLive_update(conn, req);
      }
      const ret = await this.instructorRepo.setApproved(conn, req);
      approved = ret > 0;
      await conn.commit();
      logger.info(`setApproved() (instr) success`);
    } catch (e) {
      logger.error(`setApproved() (instr) error! ${JSON.stringify(e)}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
      return approved;
    }
  }

  async saveRemarks(req: InstructorSaveRemarksReq): Promise<number> {
    return this.instructorRepo.saveRemarks(pool, req);
  }

  async setStarred(req: InstructorSetStarredReq): Promise<number> {
    return this.instructorRepo.setStarred(pool, req);
  }

  /*
  async deletePicture(req: PictureDeleteReq): Promise<number> {
    return this.instructorRepo.deletePicture(pool, req);
  }
  */
}
