import {
  Studio,
  StudioFindCriteria,
  StudioSaveRemarksReq,
  StudioSetActiveReq,
  StudioSetApprovedReq,
  StudioSetStarredReq,
} from "@backoffice-cms/models/studio";
import { StudioUserSetActiveReq, StudioUsersSetActiveReq } from "@backoffice-cms/models/studio-user";
import { StudioUserRepo } from "@backoffice-cms/repo/studio-user-repo";
import { Page, PageOfData, Sort } from "@shared/services";
import logger from "@utils/logger";
import pool from "../../init-pool";
import { StudioRepo } from "../repo/studio-repo";

export class StudioService {

  private studioRepo = new StudioRepo();
  private studioUserRepo = new StudioUserRepo();

  async findById(id: string): Promise<Studio|undefined> {
    return this.studioRepo.findById(pool, id);
  }

  async findAll(
    criteria: StudioFindCriteria, sort?: Sort, page?: Page
  ): Promise<PageOfData<Studio>> {
    return this.studioRepo.findAll(pool, criteria, sort, page);
  }

  /*
  private async setStudioGeocode(studio: Studio) {
    if (!studio.placeId.trim()) {
      return; // nothing else to do.
    }
    studio.geocode = await geocode(studio.placeId);
  }

  async register(userSave: UserSaveReq, studioSave: StudioSaveReq): Promise<void> {
    // check if email exists
    const user = await this.userRepo.findByFirebaseEmail(pool, userSave.user.firebaseEmail);
    if (user) {
      logger.info(`email ${userSave.user.firebaseEmail} already exists`);
      throw new ServiceError("409", "email already exists");
    }

    // (!) Network I/O. DO NOT do this within SQL transaction.
    await this.setStudioGeocode(studioSave.studio);

    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // create studio
      const studioId = await this.studioRepo.create(conn, studioSave);
      userSave.user.studioId = studioId;
      userSave.user.admin = true;   // (!) main user is always admin
      userSave.user.active = false; // (!) active = false by default
      // create main user
      const userId = await this.userRepo.create(conn, userSave);

      await conn.commit();
      logger.info(`register() success, studioId=${studioId}, mainUserId=${userId}`);
    } catch (e) {
      logger.error(`register error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async update(req: StudioSaveReq): Promise<number> {
    const studioId = req.studio.id;
    const found = await this.studioRepo.findById(pool, studioId);
    if (!found) {
      logger.info(`studio (id=${studioId}) not found!`);
      throw new ServiceError("204", "studio not found");
    }

    if (found.placeId !== req.studio.placeId) {
      // (!) Network I/O. DO NOT do this within SQL transaction.
      await this.setStudioGeocode(req.studio);
    } else {
      req.studio.geocode = found.geocode;
    }

    return this.studioRepo.update(pool, req);
  }

  async insertPicture(req: PictureSaveReq): Promise<string> {
    return this.studioRepo.insertPicture(pool, req);
  }

  async findPictureById(id: string): Promise<Picture|undefined> {
    return this.studioRepo.findPictureById(pool, id);
  }
  */

  async setActive(req: StudioSetActiveReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // Find studio
      const studio = await this.studioRepo.findById(pool, req.studioId);
      if (!studio) {
        throw `Studio (id=${req.studioId}) not found!`;
      }

      const hasPubRecord = await this.studioRepo.hasPubRecord(conn, req.studioId);
      if (hasPubRecord) {
        await this.studioRepo.setLiveActive(conn, req);
      }

      // Studio
      await this.studioRepo.setActive(conn, req);

      // StudioUsers
      if (req.active) {
        // active = true -> main user
        const mainUser = studio.mainUser;
        if (mainUser) {
          await this.studioUserRepo.setActive(conn, {
            userId: mainUser.id,
            studioId: studio.id,
            active: req.active,
            by: req.by,
          } as StudioUserSetActiveReq);
        }
      } else {
        // active = false -> all studio users
        await this.studioUserRepo.setActiveForStudio(conn, {
          studioId: studio.id,
          active: req.active,
          by: req.by,
        } as StudioUsersSetActiveReq);
      }

      await conn.commit();
      logger.info(`setActive(req=${JSON.stringify(req)}) success`);
    } catch (e) {
      logger.error(`setActive(req=${JSON.stringify(req)}) error! ${JSON.stringify(e)}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async setApproved(req: StudioSetApprovedReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      const hasPubRecord = await this.studioRepo.hasPubRecord(conn, req.studioId);
      if (!hasPubRecord) {
        await this.studioRepo.copyToLive_insert(conn, req);
      } else {
        await this.studioRepo.copyToLive_update(conn, req);
      }
      await this.studioRepo.setApproved(conn, req);

      await conn.commit();
      logger.info(`setApproved() (studio) success`);
    } catch (e) {
      logger.error(`setApproved() (studio) error! ${JSON.stringify(e)}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async saveRemarks(req: StudioSaveRemarksReq): Promise<number> {
    return this.studioRepo.saveRemarks(pool, req);
  }

  async setStarred(req: StudioSetStarredReq): Promise<number> {
    return this.studioRepo.setStarred(pool, req);
  }

  /*
  async deletePicture(req: PictureDeleteReq): Promise<number> {
    return this.studioRepo.deletePicture(pool, req);
  }
  */
}
