import pool from "../../init-pool";
import { PackageRepo } from "../repo/package-repo";
import { Package, PackageFindCriteria, PackageSaveReq, PackageSetStatusReq, PackageMoveSequenceReq } from "../models/package";
import { Page, PageOfData, Sort } from "@shared/services";

export class PackageService {
  private packageRepo = new PackageRepo();

  async findById(id: string): Promise<Package|undefined> {
    return this.packageRepo.findById(pool, id);
  }

  async findAll(
    criteria: PackageFindCriteria,
    sort?: Sort,
    page?: Page
  ): Promise<PageOfData<Package>> {
    return this.packageRepo.findAll(pool, criteria, sort, page);
  }

  async create(req: PackageSaveReq): Promise<string> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // Auto-assign sequence if not provided
      if (!req.package.sequence) {
        const maxSequence = await this.packageRepo.getMaxSequence(conn, req.package.currency);
        req.package.sequence = maxSequence + 1;
      }

      const packageId = await this.packageRepo.create(conn, req);

      if(req.package.sequence) {
        await this.packageRepo.reorderSequences(conn, req.package.currency, req.by.username);
      }
      await conn.commit();
      return packageId;
    } catch (error) {
      conn && (await conn.rollback());
      throw error;
    } finally {
      conn && conn.release();
    }
  }

  async update(req: PackageSaveReq): Promise<number> {
      const result = await this.packageRepo.update(pool, req);
      await this.packageRepo.reorderSequences(pool, req.package.currency, req.by.username);
      return result;
  }

  async setStatus(req: PackageSetStatusReq): Promise<number> {
    return this.packageRepo.setStatus(pool, req);
  }

  async moveSequence(req: PackageMoveSequenceReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      const currentPackage = await this.packageRepo.findById(conn, req.packageId);
      if (!currentPackage) {
        throw new Error("Package not found");
      }

      if (currentPackage.sequence === null || currentPackage.sequence === undefined) {
        throw new Error("Package has no sequence assigned");
      }

      const adjacentPackage = await this.packageRepo.getAdjacentPackage(conn, req.packageId, req.direction);
      if (!adjacentPackage) {
        throw new Error(`No package available to move ${req.direction}`);
      }
      console.log(req.packageId, adjacentPackage.id);
      // Swap sequences
      await this.packageRepo.swapSequence(conn, req.packageId, adjacentPackage.id, req.by.username);

      await conn.commit();
    } catch (error) {
      conn && (await conn.rollback());
      throw error;
    } finally {
      conn && conn.release();
    }
  }

  async reorderSequences(currency: string, by: string): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      await this.packageRepo.reorderSequences(conn, currency, by);

      await conn.commit();
    } catch (error) {
      conn && (await conn.rollback());
      throw error;
    } finally {
      conn && conn.release();
    }
  }

  async getMaxSequence(currency?: string): Promise<number> {
    return this.packageRepo.getMaxSequence(pool, currency);
  }
}