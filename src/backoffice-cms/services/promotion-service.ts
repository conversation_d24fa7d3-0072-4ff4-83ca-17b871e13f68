import { PromotionRepo } from "@backoffice-cms/repo/promotion-repo";
import pool from "../../init-pool";
import { PromotionSaveReq, PromoCodeSaveReq } from "@backoffice-cms/models/promotion";
import { By } from "@shared/models";

export class PromotionService {
  private repo = new PromotionRepo();

  async createPromotion(promotionSaveReq: PromotionSaveReq): Promise<number> {
    return this.repo.insertPromotion(pool, promotionSaveReq);
  }

  async updatePromotion(promotionId: number, promotionSaveReq: PromotionSaveReq): Promise<void> {
    return this.repo.updatePromotion(pool, promotionId, promotionSaveReq);
  }

  async togglePromotionStatus(promotionId: number, status: string, by: By): Promise<void> {
    return this.repo.updatePromotionStatus(pool, promotionId, status, by);
  }

  async togglePromoCodeStatus(promoCodeId: number, status: string, by: By): Promise<void> {
    return this.repo.updatePromoCodeStatus(pool, promoCodeId, status, by);
  }

  async listPromotions(page: number, pageSize: number) {
    const promotions = await this.repo.selectPromotionsWithCounts(pool, page, pageSize);
    const total = await this.repo.countPromotions(pool);
    return {
        data: promotions,
        total,
        page,
        pageSize,
    };
  }

  async listPromoCodes(promotionId: number, page: number, pageSize: number) {
    const promoCodes = await this.repo.selectPromoCodesWithCounts(pool, promotionId, page, pageSize);
    const total = await this.repo.countPromoCodes(pool, promotionId);
    return {
        data: promoCodes,
        total,
        page,
        pageSize,
    };
  }

  async addCode(req: PromoCodeSaveReq) {
    // Optionally validate code format, etc.
    const existing = await this.repo.findExistingCodes(pool, [req.code!]);
    if (existing.length > 0) {
      throw new Error(`Code ${req.code} already exists.`);
    }
    return this.repo.insertPromoCode(pool, req);
  }

  async generateCodes(req: PromoCodeSaveReq) {
    const codes = new Set<string>();
    // Generate unique codes in memory
    while (codes.size < req.count!) {
      const code = (req.prefix || '') + Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.add(code);
    }
    // Check for existing codes in DB
    const existing = await this.repo.findExistingCodes(pool, Array.from(codes));
    for (const code of existing) {
      codes.delete(code);
      // Generate a new code for each duplicate
      let newCode;
      do {
        newCode = (req.prefix || '') + Math.random().toString(36).substring(2, 10).toUpperCase();
      } while (codes.has(newCode) || existing.includes(newCode));
      codes.add(newCode);
    }
    // Insert all unique codes
    await this.repo.insertPromoCodes(pool, req, Array.from(codes));
    return Array.from(codes);
  }

  async assignMemberToPromoCode(promoCodeId: number, memberId: string, by: By): Promise<number> {
    // Business logic: do not assign if code is used
    const isUsed = await this.repo.isPromoCodeUsed(pool, promoCodeId);
    if (isUsed) {
      throw new Error(`Cannot assign member: promo code has already been used.`);
    }
    return await this.repo.assignMemberToPromoCode(pool, promoCodeId, memberId, by);
  }
}
