import { Member, MemberFindCriteria } from "@backoffice-cms/models/member";
import { Page, PageOfData, Sort } from "@shared/services";
import pool from "../../init-pool";
import { MemberRepo } from "../repo/member-repo";

export class MemberService {
  private memberRepo = new MemberRepo();

  async findAll(
    criteria: MemberFindCriteria, sort?: Sort, page?: Page
  ): Promise<PageOfData<Member>> {
    return this.memberRepo.findAll(pool, criteria, sort, page);
  }
} 