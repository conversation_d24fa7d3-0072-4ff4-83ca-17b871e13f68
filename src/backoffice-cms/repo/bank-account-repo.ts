import { Op, WhereOptions } from 'sequelize';

import { Bank, BankFind, BankOption } from "@database/models/bank";
import { BankAccount, BankAccountCreate, BankAccountFind, BankAccountUpdate, BankAccountUpdateStatus, BankAccountWithBank } from "@database/models/bank-account";
import { Page, PageOfData, Sort } from "@shared/services";
import { BaseRepository } from '@shared/base-repo';
import { DbTransactionOptions, STATUSES } from '@shared/constants';

export class BankRepo extends BaseRepository {

  /* ---------------------------------- Bank ---------------------------------- */

  async getBankList(criteria: BankFind, page: Page, sort?: Sort): Promise<PageOfData<Bank>> {
    const where: WhereOptions = {};
    if (criteria.status) {
      where.status = criteria.status;
    }
    if (criteria.countryCode) {
      where.countryCode = criteria.countryCode;
    }

    const options = {
      include: [
        {
          association: 'country',
          attributes: ['code', 'name', 'currency', 'symbol']
        }
      ]
    };

    return await this.findAndCountAll<Bank>('Bank', page, where, sort, options);
  }

  async getBankOption(countryCode: Bank['countryCode'] | null, appendSelectAllOption: boolean = false): Promise<BankOption[]> {
    const where: WhereOptions = {
      status: STATUSES.ACTIVE
    };

    if (countryCode) {
      where.countryCode = countryCode;
    }

    const result = await this.findAll<BankOption>('Bank', {
      attributes: [
        ['id', 'value'],
        ['name', 'label'],
        ['country_code', 'group']
      ],
      where
    });

    if (appendSelectAllOption) {
      result.unshift({
        value: '',
        label: 'All Banks'
      });
    }

    return result;
  }

  /* ------------------------------ Bank Account ------------------------------ */

  async getBankAccountList(criteria: BankAccountFind, page: Page, sort?: Sort): Promise<PageOfData<BankAccountWithBank>> {
    const { searchTerm, bankId, entityType, entityId, status } = criteria;

    const where: WhereOptions<any> = {};

    if (bankId) {
      where.bankId = bankId;
    }
    if (entityType) {
      where.entityType = entityType;
    }
    if (entityId) {
      where.entityId = entityId;
    }
    if (status) {
      where.status = status;
    }
    if (searchTerm) {
      Object.assign(where, {
        [Op.or]: [
          { accountNo: { [Op.like]: `%${searchTerm}%` } },
          { accountName: { [Op.like]: `%${searchTerm}%` } }
        ]
      });
    }

    const options: any = {
      include: [
        {
          association: 'bank',
          attributes: ['id', 'name', 'code', 'countryCode']
        }
      ]
    };

    return await this.findAndCountAll<BankAccountWithBank>('BankAccount', page, where, sort, options);
  }

  async createBankAccount(data: BankAccountCreate, options: DbTransactionOptions = {}): Promise<BankAccount> {
    return await this.create<BankAccount>('BankAccount', data, options);
  }

  async getBankAccountById(id: string): Promise<BankAccountWithBank | null> {
    const options = {
      include: [
        {
          association: 'bank',
          attributes: ['id', 'name', 'code', 'countryCode']
        }
      ]
    };

    return await this.findById<BankAccountWithBank>('BankAccount', id, options);
  }

  async updateBankAccountById(bankAccount: BankAccount, data: BankAccountUpdate, options: DbTransactionOptions = {}): Promise<BankAccount> {
    return await this.update(bankAccount, data, options);
  }

  async updateBankAccountStatusById(bankAccount: BankAccount, data: BankAccountUpdateStatus, options: DbTransactionOptions = {}): Promise<BankAccount> {
    return await this.update(bankAccount, data, options);
  }

  async checkDuplicateBankAccount(
    entityType: BankAccount['entityType'],
    entityId: BankAccount['entityId'],
    bankId: BankAccount['bankId'],
    accountNo: BankAccount['accountNo'],
    id?: BankAccount['id']
  ): Promise<BankAccount | null> {
    const where: WhereOptions = {
      entityType,
      entityId,
      bankId,
      accountNo
    };

    if (id) {
      where.id = { [Op.ne]: id };
    }

    return await this.findOne<BankAccount>('BankAccount', where);
  }
}
