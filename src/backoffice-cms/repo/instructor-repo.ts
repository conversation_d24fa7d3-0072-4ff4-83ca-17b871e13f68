import { Page, PageOfData, Sort } from "@shared/services";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import {
  Instructor,
  InstructorCopyToLiveReq,
  InstructorFindCriteria,
  InstructorSaveRemarksReq,
  InstructorSetActiveReq,
  InstructorSetApprovedReq,
  InstructorSetStarredReq,
} from "../models/instructor";

export class InstructorRepo {

  async findById(conn: Connection, id: string): Promise<Instructor|undefined> {
    const sql = "select * from instr01instructors where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toInstructor(results[0]);
  }

  // Check if pub02 record exists
  async hasPubRecord(conn: Connection, id: string): Promise<boolean> {
    const sql = "select count(id) as count from pub02instructors where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results[0]['count'] !== 0;
  }

  async findAll(
    conn: Connection,
    criteria: InstructorFindCriteria,
    sort?: Sort,
    page?: Page,
  ): Promise<PageOfData<Instructor>> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    if (criteria.instrId) {
      clauses.push("(id = ?)");
      params.push(criteria.instrId);
    }
    if (!criteria.inclDisabled) {
      clauses.push("(onboarded is true)");
    }
    { // deleted
      clauses.push("(deleted is false)");
    }

    // select items
    let selectSql = "select * from instr01instructors";
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }

    const orderBys: string[] = [];
    const starred = await this.findStarredInstrIds(conn, criteria.adminId!);
    if (starred.length !== 0) {
      orderBys.push(`FIELD(id, ${starred.map(o => `"${o}"`).join(",")}) = 0`);
    }
    if (sort) {
      const sortDir = sort.sortAsc ? 'asc' : 'desc';
      const lookup = new Map<string, string>();
      lookup.set("name"     , `name ${sortDir}`);
      lookup.set("facebook",  `facebook ${sortDir}`);
      lookup.set("instagram", `instagram ${sortDir}`);
      lookup.set("onboarded", `onboarded ${sortDir}`);
      lookup.set("pendingApproval", `pend_approval ${sortDir}`);
      if (lookup.has(sort.sort)) {
        orderBys.push(lookup.get(sort.sort)!);
      }
    }
    if (orderBys.length !== 0) {
      selectSql += ` order by ${orderBys.join(",")}`;
    }
    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    // logger.debug(selectSql);
    const [results1] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = results1.map(result => {
      const ret = this.toInstructor(result);
      if (starred.includes(ret.id)) {
        ret.starred = true;
      }
      return ret;
    });

    // select total
    let totalSql = "select count(*) as `count` from instr01instructors";
    if (clauses.length !== 0) {
      totalSql += ` where ${clauses.join(" and ")}`
    }
    const [results2] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = results2[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;

    // return page of data
    return {
      totalItems: totalItems,
      totalPages: totalPages,
      items: items,
    } as PageOfData<Instructor>;
  }

  private async findStarredInstrIds(conn: Connection, adminId: string): Promise<string[]> {
    const sql = "select instr_id from adm03instr_stars where admin_id = ?";
    const params = [adminId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(row => row['instr_id']!);
  }

  private toInstructor(row: any): Instructor {
    return {
      id: row['id'],
      firebaseUid: row['fbase_uid'],
      name: row['name'],
      descr: row['descr'],
      facebook: row['facebook'],
      instagram: row['instagram'],
      certifications: row['certs'],
      specialisations: row['specs'],
      pictures: JSON.parse(row['pictures'] ?? '[]'),
      registeredAt: row['reg_at'],
      onboarded: row['onboarded'] === 1,
      pendingApproval: row['pend_approval'] == 1,
      backofficeRemarks: row['bo_remarks'],
      starred: row['starred'] == 1,
      deleted: row['deleted'] === 1,
    }
  }

  /*
  async create(conn: Connection, req: InstructorSaveReq): Promise<string> {
    const sql = `insert into stu01instructors (
        id, name, descr, contact_no, email, address, place_id,
        geocode, lat, lng, latlng,
        facebook, instagram, \`usage\`,
        instrless,
        pictures, videos,
        created_by
      ) values (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?, ?, ?, ?
      )`;
    const by = req.by.username;
    const instructor = req.instructor;
    const lat = instructor.geocode?.geometry.location.lat ?? null;
    const lng = instructor.geocode?.geometry.location.lng ?? null;
    const latLng = (lat && lng) ? new LatLng(lat, lng) : null;
    const uuid = crypto.randomUUID();
    logger.debug(`[${by}] create() instructor=${JSON.stringify(instructor)}`)
    const params = [
      uuid,
      instructor.name,
      instructor.descr ?? '',
      instructor.contactNo,
      instructor.email,
      instructor.address,
      instructor.placeId,
      JSON.stringify(instructor.geocode) ?? null,
      lat,
      lng,
      latLng?.toCacheString() ?? null,
      instructor.socialMedia?.facebook ?? '',
      instructor.socialMedia?.instagram ?? '',
      instructor.usagePolicies ?? '',
      instructor.instrLess ?? false,
      instructor.pictures ?? '[]',
      instructor.videos ?? '[]',
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created instructor, id=${uuid}, numRows=${results.affectedRows}`);
    return uuid;
  }

  async update(conn: Connection, req: InstructorSaveReq): Promise<number> {
    const sql = `update stu01instructors set
      name = ?, descr = ?, contact_no = ?, email = ?, address = ?, place_id = ?,
      geocode = ?, lat = ?, lng = ?, latlng = ?,
      facebook = ?, instagram = ?, \`usage\` = ?,
      instrless = ?,
      pictures = ?, videos = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const instructor = req.instructor;
    const lat = instructor.geocode?.geometry.location.lat ?? null;
    const lng = instructor.geocode?.geometry.location.lng ?? null;
    const latLng = (lat && lng) ? new LatLng(lat, lng) : null;
    logger.debug(`[${by}] update() instructor=${JSON.stringify(instructor)}`)
    const params = [
      instructor.name,
      instructor.descr ?? '',
      instructor.contactNo,
      instructor.email,
      instructor.address,
      instructor.placeId,
      JSON.stringify(instructor.geocode) ?? null,
      lat,
      lng,
      latLng?.toCacheString() ?? null,
      instructor.socialMedia?.facebook ?? '',
      instructor.socialMedia?.instagram ?? '',
      instructor.usagePolicies ?? '',
      instructor.instrLess ?? false,
      instructor.pictures ?? '[]',
      instructor.videos ?? '[]',
      by,
      instructor.id,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated instructor, id=${instructor.id}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async insertPicture(conn: Connection, req: PictureSaveReq): Promise<string> {
    const sql = `insert into stu06pictures (
        id, user_id, path, created_by
      ) values (
        ?, ?, ?, ?
      )`;
    const by = req.by.username;
    const uuid = crypto.randomUUID();
    logger.debug(`[${by}] insertPicture() req=${JSON.stringify(req)}`)
    const params = [
      uuid,
      req.userId,
      req.path,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] inserted picture, id=${uuid}, numRows=${results.affectedRows}`);
    return uuid;
  }

  async findPictureById(conn: Connection, id: string): Promise<Picture|undefined> {
    const sql = "select *  from stu06pictures where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toPicture(results[0]);
  }

  private toPicture(row: any): Picture {
    return {
      id: row['id'],
      userId: row['userId'],
      path: row['path'],
    } as Picture;
  }
  */

  async setActive(
    conn: Connection,
    req: InstructorSetActiveReq,
  ): Promise<number> {
    const sql = `update instr01instructors set
      onboarded = ?, updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const params = [
      req.active,
      by,
      req.instrId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] set instructor active=${req.active}, id=${req.instrId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setLiveActive(conn: Connection, req: InstructorSetActiveReq): Promise<number> {
    const sql = `update pub02instructors set
      onboarded = ?, updated_by = ?
      where id = ?`;
      const by = req.by.username;
    const params = [
      req.active,
      by,
      req.instrId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows !== 1) {
      throw `setLiveActive() (instr) expected exactly 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] setLiveActive(), active=${req.active}, instrId=${req.instrId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async copyToLive_insert(conn: Connection, req: InstructorCopyToLiveReq): Promise<number> {
    const sql = `insert into pub02instructors (
        id, fbase_uid, name, descr, facebook, instagram, certs, specs, pictures, reg_at, onboarded,
        -- pend_approval,
        deleted, deleted_at, deleted_by, created_at, created_by, updated_at, updated_by
      ) select
        id, fbase_uid, name, descr, facebook, instagram, certs, specs, pictures, reg_at, onboarded,
        -- pend_approval,
        deleted, deleted_at, deleted_by, created_at, created_by, updated_at, updated_by
      from instr01instructors where id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] copyToLive_insert() req=${JSON.stringify(req)}`)
    const params = [req.instrId!];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] copyToLive_insert(), instrId=${req.instrId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async copyToLive_update(conn: Connection, req: InstructorCopyToLiveReq): Promise<number> {
    const sql = `update
      pub02instructors tgt inner join instr01instructors src on (tgt.id = src.id)
      set
      tgt.name       = src.name,
      tgt.descr      = src.descr,
      tgt.facebook   = src.facebook,
      tgt.instagram  = src.instagram,
      tgt.certs      = src.certs,
      tgt.specs      = src.specs,
      tgt.pictures   = src.pictures,
      tgt.onboarded  = src.onboarded,
      tgt.deleted    = src.deleted,
      tgt.deleted_at = src.deleted_at,
      tgt.deleted_by = src.deleted_by,
      tgt.updated_at = src.updated_at,
      tgt.updated_by = src.updated_by
      where src.id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] copyToLive_update() req=${JSON.stringify(req)}`)
    const params = [req.instrId!];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows !== 1) {
      throw `copyToLive_update() (instr) expected exactly 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] copyToLive_update(), instrId=${req.instrId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setApproved(conn: Connection, req: InstructorSetApprovedReq): Promise<number> {
    const sql = `update instr01instructors set
      pend_approval = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] setApproved() req=${JSON.stringify(req)}`)
    const params = [
      false, // (!) set pend_approval to false
      by,
      req.instrId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows !== 1) {
      throw `setApproved() (instr) expected exactly 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] setApproved(), instrId=${req.instrId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async saveRemarks(conn: Connection, req: InstructorSaveRemarksReq): Promise<number> {
    const sql = `update instr01instructors set
      bo_remarks = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] saveRemarks() req=${JSON.stringify(req)}`)
    const params = [
      req.value,
      by,
      req.instrId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows !== 1) {
      throw `saveRemarks() (instr) expected exactly 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] saveRemarks(), instrId=${req.instrId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setStarred(conn: Connection, req: InstructorSetStarredReq): Promise<number> {
    return req.starred
      ? await this.unstar(conn, req)
      : await this.star(conn, req);
  }

  private async star(conn: Connection, req: InstructorSetStarredReq): Promise<number> {
    const sql = `insert ignore into adm03instr_stars(
        admin_id, instr_id, created_by
      ) values (
        ?, ?, ?
      )`;
    const by = req.by.username;
    logger.debug(`[${by}] star() req=${JSON.stringify(req)}`)
    const params = [
      req.by.uid!, // admin_id
      req.instrId!,
      by,          // created_by
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] star(), instrId=${req.instrId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  private async unstar(conn: Connection, req: InstructorSetStarredReq): Promise<number> {
    const sql = `delete from adm03instr_stars where admin_id = ? and instr_id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] unstar() req=${JSON.stringify(req)}`)
    const params = [
      req.by.uid!, // admin_id
      req.instrId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows > 1) {
      throw `unstar() (instr) expected max 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] unstar(), instrId=${req.instrId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  /*
  async deletePicture(conn: Connection, req: PictureDeleteReq): Promise<number> {
    const sql = `delete from stu06pictures where id = ? and user_id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] deletePicture() req=${JSON.stringify(req)}`)
    const params = [
      req.pictureId,
      req.userId,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] deleted picture, id=${req.pictureId}, numRows=${affectedRows}`);
    return affectedRows;
  }
  */
}
