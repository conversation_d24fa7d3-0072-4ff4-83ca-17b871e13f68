import { Page, PageOfData, Sort } from "@shared/services";
import { Connection, RowDataPacket } from "mysql2/promise";
import { Member, MemberFindCriteria } from "@backoffice-cms/models/member";

export class MemberRepo {
  async findAll(
    conn: Connection,
    criteria: MemberFindCriteria,
    sort?: Sort,
    page?: Page,
  ): Promise<PageOfData<Member>> {
    const clauses = [];
    const params = [];
    if (criteria.searchTerm) {
      clauses.push("(full_name LIKE ? OR fbase_email LIKE ? OR mobile_no LIKE ?)");
      const like = `%${criteria.searchTerm}%`;
      params.push(like, like, like);
    }
    if (!criteria.inclDeleted) {
      clauses.push("(deleted is false OR deleted is null)");
    }
    let selectSql = "select * from usr01members";
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`;
    }
    const orderBys: string[] = [];
    if (sort) {
      const sortDir = sort.sortAsc ? 'asc' : 'desc';
      const lookup = new Map<string, string>();
      lookup.set("fullName", `full_name ${sortDir}`);
      lookup.set("firebaseEmail", `fbase_email ${sortDir}`);
      if (lookup.has(sort.sort)) {
        orderBys.push(lookup.get(sort.sort)!);
      }
    }
    if (orderBys.length !== 0) {
      selectSql += ` order by ${orderBys.join(",")}`;
    }
    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    const [results1] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = results1.map(row => this.toMember(row));
    let totalSql = "select count(*) as `count` from usr01members";
    if (clauses.length !== 0) {
      totalSql += ` where ${clauses.join(" and ")}`;
    }
    const [results2] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = results2[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;
    return {
      totalItems,
      totalPages,
      items,
    };
  }

  private toMember(row: any): Member {
    return {
      id: row['id'],
      firebaseUid: row['fbase_uid'],
      firebaseEmail: row['fbase_email'],
      fullName: row['full_name'],
      displayName: row['display_name'],
      gender: row['gender'],
      mobileNo: row['mobile_no'],
      registeredAt: row['reg_at'],
      deleted: row['deleted'] === 1,
    };
  }
} 