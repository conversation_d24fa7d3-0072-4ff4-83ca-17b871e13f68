import { Page, PageOfData, Sort } from "@shared/services";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import {
  Admin,
  AdminFindCriteria,
  AdminLastAt,
  AdminSaveRemarksReq,
  AdminSaveReq,
  AdminSetDeletedReq
} from "../models/admin";

export class AdminRepo {

  async findById(conn: Connection, id: string): Promise<Admin|undefined> {
    const sql = "select * from adm01admins where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toAdmin(results[0]);
  }

  async findByFirebaseUid(conn: Connection, uid: string): Promise<Admin|undefined> {
    const sql = "select * from adm01admins where fbase_uid = ?";
    const params = [uid];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toAdmin(results[0]);
  }

  async findByFirebaseEmail(conn: Connection, email: string): Promise<Admin|undefined> {
    const sql = "select * from adm01admins where fbase_email = ?";
    const params = [email];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toAdmin(results[0]);
  }

  async findAll(
    conn: Connection,
    criteria: AdminFindCriteria,
    sort?: Sort,
    page?: Page,
  ): Promise<PageOfData<Admin>> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    if (criteria.adminId) {
      clauses.push("(id = ?)");
      params.push(criteria.adminId);
    }
    if (!criteria.inclDeleted) {
      clauses.push("(deleted is false)");
    }

    // select items
    let selectSql = "select * from adm01admins";
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }
    const orderBys: string[] = [];
    if (sort) {
      const sortDir = sort.sortAsc ? 'asc' : 'desc';
      const lookup = new Map<string, string>();
      lookup.set("name",          `name ${sortDir}`);
      lookup.set("firebaseEmail", `fbase_email ${sortDir}`);
      lookup.set("deleted",       `deleted ${sortDir}`);
      if (lookup.has(sort.sort)) {
        orderBys.push(lookup.get(sort.sort)!);
      }
    }
    if (orderBys.length !== 0) {
      selectSql += ` order by ${orderBys.join(",")}`;
    }
    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    // logger.debug(selectSql);
    const [results1] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = results1.map(result => this.toAdmin(result))

    // select total
    let totalSql = "select count(*) as `count` from adm01admins";
    if (clauses.length !== 0) {
      totalSql += ` where ${clauses.join(" and ")}`
    }
    const [results2] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = results2[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;

    // return page of data
    return {
      totalItems: totalItems,
      totalPages: totalPages,
      items: items,
    } as PageOfData<Admin>;
  }

  private toAdmin(row: any): Admin {
    return {
      id: row['id'],
      firebaseUid: row['fbase_uid'],
      firebaseEmail: row['fbase_email'],
      name: row['name'],
      internalNotes: row['notes_int'],
      lastAt: {
        at: row['last_at'],
        byUid: row['last_by_uid'],
        by: row['last_by'],
      } as AdminLastAt,
      deleted: row['deleted'] === 1,
    } as Admin
  }

  async create(conn: Connection, req: AdminSaveReq): Promise<string> {
    const sql = `insert into adm01admins (
        id, fbase_uid, fbase_email, name, notes_int,
        last_at, last_by_uid, last_by,
        created_by
      ) values (
        ?, ?, ?, ?, ?, now(), ?, ?, ?
      )`;
    const by = req.by.username;
    const admin = req.admin;
    logger.debug(`[${by}] create() admin=${JSON.stringify(admin)}`)
    const params = [
      admin.firebaseUid, // id, same as firebaseUid
      admin.firebaseUid,
      admin.firebaseEmail,
      admin.name,
      admin.internalNotes,
      req.by.uid,                     // last_by_uid
      req.by.username,                // last_by
      by,                             // created_by
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created admin, firebaseUid=${admin.firebaseUid}, numRows=${results.affectedRows}`);
    return admin.id;
  }

  async update(conn: Connection, req: AdminSaveReq): Promise<number> {
    const sql = `update adm01admins set
      name = ?, notes_int = ?,
      last_at = now(), last_by_uid = ?, last_by = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const admin = req.admin;
    logger.debug(`[${by}] update() admin=${JSON.stringify(admin)}`)
    const params = [
      admin.name,
      admin.internalNotes,
      req.by.uid,                     // last_by_uid
      req.by.username,                // last_by
      by,                             // updated_by
      admin.id,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated admin, id=${admin.id}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setDeleted(
    conn: Connection,
    req: AdminSetDeletedReq,
  ): Promise<number> {
    const sql = `update adm01admins set
      deleted = ?, deleted_at = CASE WHEN ? = 1 THEN now() ELSE deleted_at END, deleted_by = ?,
      last_at = now(), last_by_uid = ?, last_by = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const params = [
      req.deleted,
      req.deleted ? 1 : 0,
      req.deleted ? req.by : null,    // deleted_by
      req.by.uid,                     // last_by_uid
      req.by.username,                // last_by
      req.by,                         // updated_by
      req.adminId,                    // id
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] set admin deleted=${req.deleted}, id=${req.adminId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async saveRemarks(conn: Connection, req: AdminSaveRemarksReq): Promise<number> {
    const sql = `update adm01admins set
      notes_int = ?,
      last_at = now(), last_by_uid = ?, last_by = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] saveRemarks() req=${JSON.stringify(req)}`)
    const params = [
      req.value,                      // notes_int
      req.by.uid,                     // last_by_uid
      req.by.username,                // last_by
      by,                             // updated_by
      req.adminId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows !== 1) {
      throw `saveRemarks() (admin) expected exactly 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] saveRemarks(), adminId=${req.adminId}, numRows=${affectedRows}`);
    return affectedRows;
  }
}
