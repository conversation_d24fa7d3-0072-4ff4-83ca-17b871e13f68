import { WhereOptions, Model } from 'sequelize';

import { BaseRepository } from '@shared/base-repo';
import { Option } from '@shared/constants';
import { Country, CountryOption, CurrencyOption } from '@database/models/country';

export class CountryRepo extends BaseRepository {
  async getCountryOption(appendSelectAllOption: boolean = false): Promise<Option[]> {
    const results = await this.findAll<CountryOption>('Country', {
      attributes: [
        ['code', 'value'],
        ['name', 'label']
      ],
    });

    if (appendSelectAllOption) {
      results.unshift({
        value: '',
        label: 'All Countries'
      });
    }

    return results.map(item => this.toCountryOption(item));
  }

  async getCurrencyOptions(appendSelectAllOption: boolean = false): Promise<Option[]> {
    const results = await this.findAll<CurrencyOption>('Country', {
      attributes: [
        ['currency', 'value'],
        ['currency_name', 'label']
      ],
    });

    if (appendSelectAllOption) {
      results.unshift({
        value: '',
        label: 'All Currencies'
      });
    }

    return results.map(item => this.toCurrencyOption(item));
  }

  async getCountryList(): Promise<Country[]> {
    const results = await this.findAll<Model>('Country', {
      order: [['name', 'ASC']]
    });

    // Convert Sequelize model instances to plain objects
    return results.map(item => this.toCountry(item));
  }

  private toCountry(row: any): Country {
    return {
      code: row['code'],
      name: row['name'],
      timezone: row['timezone'],
      currency: row['currency'],
      currency_name: row['currency_name'],
      symbol: row['symbol'],
    } as Country;
  }

  private toCurrencyOption(row: any): CurrencyOption {
    const data = row.dataValues || row.toJSON ? row.toJSON() : row;
    return {
      value: data.value,
      label: data.label,
    } as CurrencyOption;
  }

  private toCountryOption(row: any): CountryOption {
    const data = row.dataValues || row.toJSON ? row.toJSON() : row;
    return {
      value: data.value,
      label: data.label,
    } as CountryOption;
  }
}