import { Page, PageOfData, Sort } from "@shared/services";
import { EventCancelBy } from "@studio-cms/models/event";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import {
  Booking,
  BookingEquipment,
  BookingFindCriteria,
  BookingInstructor,
  BookingMember,
  BookingSaveRemarksReq,
  BookingSetCancelledReq,
  BookingSetStarredReq,
  BookingStudio,
  BookingCancelReq,
} from "../models/booking";

export class BookingRepo {

  async findById(conn: Connection, id: string): Promise<Booking|undefined> {
    const sql = `
      SELECT b.*
      FROM evt01booking_details b
      WHERE b.id = ?`;
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }

    // Get cancellation request if exists
    const cancelReqSql = `
      SELECT event_id, id, req_by, user_id, free, reason, approved, approved_at, approved_by, at
      FROM evt03cancel_req
      WHERE event_id = ? AND approved = 0`;
    const [cancelReqs] = await conn.query<RowDataPacket[]>(cancelReqSql, [id]);

    const booking = this.toBooking(results[0]);
    if (cancelReqs.length > 0) {
      booking.cancelReq = this.toCancelBookingReq(cancelReqs[0]);
    }
    return booking;
  }

  async findAll(
    conn: Connection,
    criteria: BookingFindCriteria,
    sort?: Sort,
    page?: Page,
  ): Promise<PageOfData<Booking>> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    if (criteria.bookingId) {
      clauses.push("(b.id = ?)");
      params.push(criteria.bookingId);
    }
    if (!criteria.inclCancelled) {
      clauses.push("(b.cancelled is false)");
    }
    { // deleted
      clauses.push("(b.deleted is false)");
    }

    // Add cancellation request filter using EXISTS
    if (criteria.pendingCancelReq) {
      clauses.push(`(EXISTS (
        SELECT 1 FROM evt03cancel_req cr
        WHERE cr.event_id = b.id
        AND cr.approved = 0
        AND cr.req_by IN (?, ?)
      ))`);
      params.push(EventCancelBy[EventCancelBy.Studio], EventCancelBy[EventCancelBy.Instructor]);
    }

    // Build the base query
    let selectSql = `
      SELECT b.*
      FROM evt01booking_details b`;

    if (clauses.length !== 0) {
      selectSql += ` WHERE ${clauses.join(" AND ")}`
    }

    const orderBys: string[] = [];
    const starred = await this.findStarredBookingIds(conn, criteria.adminId!);
    if (starred.length !== 0) {
      orderBys.push(`FIELD(b.id, ${starred.map(o => `"${o}"`).join(",")}) = 0`);
    }
    orderBys.push(`coalesce(b.updated_at, b.created_at) desc`);
    if (sort) {
      const sortDir = sort.sortAsc ? 'asc' : 'desc';
      const lookup = new Map<string, string>();
      lookup.set("booking_status"   , `b.booking_status ${sortDir}`);
      lookup.set("cancelled", `b.cancelled ${sortDir}`);
      if (lookup.has(sort.sort)) {
        orderBys.push(lookup.get(sort.sort)!);
      }
    }
    if (orderBys.length !== 0) {
      selectSql += ` ORDER BY ${orderBys.join(",")}`;
    }
    if (page) {
      selectSql += " LIMIT " + page.toZeroBasedOffset() + "," + page.size;
    }

    const [results1] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = results1.map(result => {
      const ret = this.toBooking(result);
      if (starred.includes(ret.id)) {
        ret.starred = true;
      }
      return ret;
    });

    // select total with EXISTS
    let totalSql = `
      SELECT COUNT(*) as count
      FROM evt01booking_details b`;

    if (clauses.length !== 0) {
      totalSql += ` WHERE ${clauses.join(" AND ")}`
    }

    const [results2] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = results2[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;

    // return page of data
    return {
      totalItems: totalItems,
      totalPages: totalPages,
      items: items,
    } as PageOfData<Booking>;
  }

  private async findStarredBookingIds(conn: Connection, adminId: string): Promise<string[]> {
    const sql = "select booking_id from adm04booking_stars where admin_id = ?";
    const params = [adminId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(row => row['booking_id']!);
  }

  private toBooking(row: any): Booking {
    const fees = row['booking_fees'];
    const instrId = row['instr_id'];
    return {
      id: row['id'],
      refNo: row['booking_ref'],
      status: row['status'],
      paymentStatus: row['booking_status'],
      fees: fees ? JSON.parse(fees) : undefined,
      startDate: row['start_date'],
      startTime: row['start_time'],
      endDate: row['end_date'],
      endTime: row['end_time'],
      studio: {
        id: row['studio_id'],
        name: row['studio_name'],
      } as BookingStudio,
      instructor: instrId ? {
        id: row['instr_id'],
        name: row['instr_name'],
      } as BookingInstructor : undefined,
      member: {
        id: row['member_id'],
        displayName: row['member_display_name'],
        fullName: row['member_full_name'],
      } as BookingMember,
      equipment: {
        id: row['equip_id'],
        name: row['equip_name'],
        code: row['equip_code'],
      } as BookingEquipment,
      cancelled: row['cancelled'] == 1,
      cancelledAt: row['cancelled_at'],
      cancelledBy: row['cancelled_by'],
      cancelFree: row['cancel_free'] == 1,
      cancelReason: row['cancel_reason'],
      cancelRefundedAmount: row['cancel_refunded_amount'] ? +row['cancel_refunded_amount'] : undefined,
      ...row['cancel_req_id'] && {
        cancelReq: this.toCancelBookingReq({
          id: row['cancel_req_id'],
          event_id: row['id'],
          req_by: row['cancel_req_by'],
          user_id: row['cancel_req_user_id'],
          free: row['cancel_req_free'],
          reason: row['cancel_req_reason'],
          approved: row['cancel_req_approved'],
          approved_at: row['cancel_req_approved_at'],
          approved_by: row['cancel_req_approved_by'],
          at: row['cancel_req_at'],
        } as RowDataPacket),
      },
      noShow: row['i_noshow'] == 1,
      noShowAt: row['i_noshow_at'],
      noShowRemarks: row['i_noshow_remarks'],
      backofficeRemarks: row['bo_remarks'],
      starred: row['starred'] == 1,
      deleted: row['deleted'] === 1,
      updatedAt: row['updated_at'],
    } as Booking;
  }

  private toCancelBookingReq(row: RowDataPacket): BookingCancelReq {
    return {
      id: row.id,
      eventId: row.event_id,
      reqBy: row.req_by,
      userId: row.user_id,
      free: row.free === 1,
      reason: row.reason,
      approved: row.approved === 1,
      approvedAt: row.approved_at,
      approvedBy: row.approved_by,
      at: row.at
    };
  }

  async setCancelled(conn: Connection, req: BookingSetCancelledReq): Promise<number> {
    const sql = `update evt01events set
      cancelled = ?, cancelled_at = now(),
      cancelled_by = ?, cancelled_by_id = ?,
      cancel_free = ?, cancel_reason = ?,
      updated_by = ?
      where id = ? and cancelled != 1`;
    const by = req.by.username;
    logger.debug(`[${by}] setCancelled() req=${JSON.stringify(req)}`)
    const params = [
      true, // cancelled
      EventCancelBy[req.reqBy],
      req.userId, // cancelled_by_id
      req.free,    // cancel_free
      req.reason, // cancelled_reason
      by,         // updated_by
      req.bookingId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows > 1) {
      throw `setCancelled() (booking) expected max 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] setCancelled(), bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setCancelledApproved(conn: Connection, req: BookingSetCancelledReq): Promise<number> {
    const sql = `update evt03cancel_req set
      approved = 1, approved_at = now(),
      approved_by = ?, updated_by = ?
      where event_id = ? and req_by = ? and approved = 0`;
    const by = req.by.username;
    logger.debug(`[${by}] setCancelledApproved() req=${JSON.stringify(req)}`)
    const params = [
      by, // approved_by
      by, // updated_by
      req.bookingId!, // event_id
      EventCancelBy[req.reqBy], // req_by
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows > 1) {
      throw `setCancelledApproved() (booking) expected max 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] setCancelledApproved(), bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async saveRemarks(conn: Connection, req: BookingSaveRemarksReq): Promise<number> {
    const sql = `update evt01events set
      bo_remarks = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] saveRemarks() req=${JSON.stringify(req)}`)
    const params = [
      req.value,
      by,
      req.bookingId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows !== 1) {
      throw `saveRemarks() (booking) expected exactly 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] saveRemarks(), bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setStarred(conn: Connection, req: BookingSetStarredReq): Promise<number> {
    return req.starred
      ? await this.unstar(conn, req)
      : await this.star(conn, req);
  }

  private async star(conn: Connection, req: BookingSetStarredReq): Promise<number> {
    const sql = `insert ignore into adm04booking_stars(
        admin_id, booking_id, created_by
      ) values (
        ?, ?, ?
      )`;
    const by = req.by.username;
    logger.debug(`[${by}] star() req=${JSON.stringify(req)}`)
    const params = [
      req.by.uid!, // admin_id
      req.bookingId!,
      by,          // created_by
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] star(), bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  private async unstar(conn: Connection, req: BookingSetStarredReq): Promise<number> {
    const sql = `delete from adm04booking_stars where admin_id = ? and booking_id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] unstar() req=${JSON.stringify(req)}`)
    const params = [
      req.by.uid!, // admin_id
      req.bookingId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows > 1) {
      throw `unstar() (booking) expected max 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] unstar(), bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async getPendingCancelCount(conn: Connection): Promise<number> {
    // Count bookings with a pending cancel request (approved = 0, req_by Studio or Instructor)
    const sql = `
      SELECT COUNT(DISTINCT cr.event_id) as count
      FROM evt03cancel_req cr
      WHERE cr.approved = 0
        AND cr.req_by IN (?, ?)
    `;
    const params = [EventCancelBy[EventCancelBy.Studio], EventCancelBy[EventCancelBy.Instructor]];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results[0]?.count || 0;
  }
}
