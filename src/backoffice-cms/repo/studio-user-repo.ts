import logger from "@utils/logger";
import { Connection, ResultSetHeader } from "mysql2/promise";
import { StudioUserSetActiveReq, StudioUsersSetActiveReq } from "../models/studio-user";

export class StudioUserRepo {

  /*
  async findById(conn: Connection, id: string): Promise<StudioUser|undefined> {
    const sql = "select * from stu02users where id = ?";
    const params = [id];
    const [results, fields] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toStudioUser(results[0]);
  }

  async findByFirebaseEmail(conn: Connection, email: string): Promise<StudioUser|undefined> {
    const sql = "select * from stu02users where fbase_email = ?";
    const params = [email];
    const [results, fields] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toStudioUser(results[0]);
  }

  async findAll(
    conn: Connection,
    criteria: UserFindCriteria,
    sort?: Sort,
    page?: Page,
  ): Promise<PageOfData<User>> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    if (criteria.studioId) {
      clauses.push("(studio_id = ?)");
      params.push(criteria.studioId);
    }
    if (!criteria.inclDisabled) {
      clauses.push("(active is true)");
    }

    // select items
    let selectSql = "select * from stu02users";
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }
    if (sort) {
      const sortDir = sort.sortAsc ? 'asc' : 'desc';
      const lookup = new Map<String, String>();
      lookup.set("name"     , ` order by name ${sortDir}`);
      lookup.set("email"    , ` order by fbase_email ${sortDir}`);
      lookup.set("jobTitle" , ` order by job_title ${sortDir}`);
      lookup.set("contactNo", ` order by contact_no ${sortDir}`);
      lookup.set("active"   , ` order by active ${sortDir}`);
      lookup.set("admin"    , ` order by admin ${sortDir}`);
      if (lookup.has(sort.sort)) {
        selectSql += lookup.get(sort.sort);
      }
    }
    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    // logger.debug(selectSql);
    const [results1] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = results1.map(result => this.toUser(result))

    // select total
    let totalSql = "select count(*) as `count` from stu02users";
    if (clauses.length !== 0) {
      totalSql += ` where ${clauses.join(" and ")}`
    }
    const [results2] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = results2[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;

    // return page of data
    return {
      totalItems: totalItems,
      totalPages: totalPages,
      items: items,
    } as PageOfData<User>;
  }

  private toStudioUser(row: any): StudioUser {
    return {
      id: row['id'],
      firebaseUid: row['fbase_uid'],
      firebaseEmail: row['fbase_email'],
      studioId: row['studio_id'],
      name: row['name'],
      jobTitle: row['job_title'],
      contactNo: row['contact_no'],
      admin: row['admin'] === 1,
      active: row['active'] === 1,
    } as StudioUser;
  }

  async setFirebaseUid(
    conn: Connection,
    req: FirebaseUidSetReq,
  ): Promise<number> {
    const sql = `update stu02users
      set fbase_uid = ?, updated_by = ?
      where id = ? and fbase_email = ? and fbase_uid like '@%'`;
    const by = req.by.username;
    const params = [
      req.firebaseUid,
      by, // updated_by
      req.userId,
      req.firebaseEmail,
    ];
    const [results, fields] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows > 1) {
      throw `setFirebaseUid() expecting max 1 row updated, actual ${affectedRows}`;
    }
    logger.info(`[${by}] setFirebaseUid() req=${JSON.stringify(req)}, numRows=${results.affectedRows}`);
    return affectedRows;
  }

  async create(conn: Connection, req: UserSaveReq): Promise<string> {
    const sql = `insert into stu02users (
        id, fbase_uid, fbase_email, studio_id,
        name, job_title, contact_no,
        admin, active,
        created_by
      ) values (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
      )`;
    const uuid = crypto.randomUUID();
    const by = req.by.username;
    const user = req.user;
    const firebaseUid = user.firebaseUid;
    logger.debug(`[${by}] create() user=${JSON.stringify(user)}`)
    const params = [
      uuid,        // id
      firebaseUid, // fbase_uid
      user.firebaseEmail,
      user.studioId,
      user.name,
      user.jobTitle,
      user.contactNo,
      user.admin ?? false,
      user.active ?? false,
      by,
    ];
    const [results, fields] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created user, id=${uuid}, numRows=${results.affectedRows}`);
    return firebaseUid;
  }

  // (!) Intended for manage users (by admin user)
  async update(conn: Connection, req: UserSaveReq): Promise<number> {
    const sql = `update stu02users set
      name = ?, job_title = ?, contact_no = ?,
      admin = ?, active = ?,
      updated_by = ?
      where id = ? and studio_id = ?`;
    const by = req.by.username;
    const user = req.user;
    logger.debug(`[${by}] update() user=${JSON.stringify(user)}`)
    const params = [
      user.name,
      user.jobTitle,
      user.contactNo,
      user.admin ?? false,
      user.active ?? false,
      by,
      user.id,
      user.studioId, // (!) important
    ];
    const [results, fields] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated user, id=${user.id}, numRows=${affectedRows}`);
    return affectedRows;
  }

  // (!) Intended for self
  async updateSelf(conn: Connection, req: UserSaveReq): Promise<number> {
    const sql = `update stu02users set
      name = ?, job_title = ?, contact_no = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const user = req.user;
    logger.debug(`[${by}] updateSelf() user=${JSON.stringify(user)}`)
    const params = [
      user.name,
      user.jobTitle,
      user.contactNo,
      by,
      user.id,
    ];
    const [results, fields] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated self, id=${user.id}, numRows=${affectedRows}`);
    return affectedRows;
  }
  */

  // (!) this is for 1 studio user
  async setActive(conn: Connection, req: StudioUserSetActiveReq): Promise<number> {
    const sql = `update stu02users set
      active = ?, updated_by = ?
      where id = ? and studio_id = ?`;
    const by = req.by.username;
    const params = [
      req.active,
      by,
      req.userId,
      req.studioId, // (!) important
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] set studio user active=${req.active}, id=${req.userId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  // (!) this is for all studio users
  async setActiveForStudio(conn: Connection, req: StudioUsersSetActiveReq): Promise<number> {
    const sql = `update stu02users set
      active = ?, updated_by = ?
      where studio_id = ?`;
    const by = req.by.username;
    const params = [
      req.active,
      by,
      req.studioId!, // (!) important
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] set studio users active=${req.active}, studioId=${req.studioId}, numRows=${affectedRows}`);
    return affectedRows;
  }
}
