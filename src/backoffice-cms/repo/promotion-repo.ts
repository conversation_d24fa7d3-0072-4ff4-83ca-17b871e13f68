import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import {
  PromotionSaveReq,
  PromotionWithCounts,
  PromoCodeWithDetails,
  PromoCodeSaveReq,
} from "@backoffice-cms/models/promotion";
import logger from "@utils/logger";
import { By } from "@shared/models";

export class PromotionRepo {

    async insertPromotion(conn: Connection, promotionSaveReq: PromotionSaveReq): Promise<number> {
      const [result] = await conn.query<ResultSetHeader>(
        `INSERT INTO promo01promotion (name, description, type, value, start_date, end_date, per_user_limit, global_limit, status, created_by)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          promotionSaveReq.promotion.name,
          promotionSaveReq.promotion.description,
          promotionSaveReq.promotion.type,
          promotionSaveReq.promotion.value,
          promotionSaveReq.promotion.start_date,
          promotionSaveReq.promotion.end_date,
          promotionSaveReq.promotion.per_user_limit,
          promotionSaveReq.promotion.global_limit,
          promotionSaveReq.promotion.status,
          promotionSaveReq.by.uid,
        ]
      );
      logger.info(`[${promotionSaveReq.by.username}] created promotion, id=${result.insertId}`);
      return result.insertId;
    }

    async updatePromotion(conn: Connection, promotionId: number, promotionSaveReq: PromotionSaveReq): Promise<void> {
      await conn.query(
        `UPDATE promo01promotion
         SET name = ?, description = ?, type = ?, value = ?, start_date = ?, end_date = ?,
             per_user_limit = ?, global_limit = ?, status = ?
         WHERE id = ?`,
        [
          promotionSaveReq.promotion.name,
          promotionSaveReq.promotion.description,
          promotionSaveReq.promotion.type,
          promotionSaveReq.promotion.value,
          promotionSaveReq.promotion.start_date,
          promotionSaveReq.promotion.end_date,
          promotionSaveReq.promotion.per_user_limit,
          promotionSaveReq.promotion.global_limit,
          promotionSaveReq.promotion.status,
          promotionId
        ]
      );
      logger.info(`[${promotionSaveReq.by.username}] updated promotion, id=${promotionId}`);
    }

    async updatePromotionStatus(conn: Connection, promotionId: number, status: string, by: By): Promise<void> {
      await conn.query(
        `UPDATE promo01promotion SET status = ? WHERE id = ?`,
        [status, promotionId]
      );
      logger.info(`[${by.username}] updated promotion status, id=${promotionId}, status=${status}`);
    }

    async updatePromoCodeStatus(conn: Connection, promoCodeId: number, status: string, by: By): Promise<void> {
      await conn.query(
        `UPDATE promo02promo_code SET status = ? WHERE id = ?`,
        [status, promoCodeId]
      );
      logger.info(`[${by.username}] updated promo_code status, id=${promoCodeId}, status=${status}`);
    }

    async insertPromoCode(conn: Connection, req: PromoCodeSaveReq) {
      await conn.query(
        `INSERT INTO promo02promo_code (promotion_id, code, code_limit, created_by, created_at)
         VALUES (?, ?, ?, ?, NOW())`,
        [req.promotionId, req.code, req.code_limit, req.by.uid]
      );
    }

    async insertPromoCodes(conn: Connection, req: PromoCodeSaveReq, codes: string[]) {
      if (!codes.length) return;
      const values = codes.map(code => [req.promotionId, code, req.code_limit, req.by.uid]);
      await conn.query(
        `INSERT INTO promo02promo_code (promotion_id, code, code_limit, created_by)
         VALUES ?`,
        [values]
      );
    }

    async findExistingCodes(conn, codes) {
      if (!codes.length) return [];
      const [rows] = await conn.query(
        `SELECT code FROM promo02promo_code WHERE code IN (?)`,
        [codes]
      );
      return rows.map(row => row.code);
    }

    private toPromotionWithCounts(row: any): PromotionWithCounts {
      return {
        id: row['id'],
        name: row['name'],
        description: row['description'],
        type: row['type'],
        value: row['value'],
        start_date: row['start_date'],
        end_date: row['end_date'],
        per_user_limit: row['per_user_limit'],
        global_limit: row['global_limit'],
        status: row['status'],
        created_by: row['created_by'],
        created_at: row['created_at'],
        updated_by: row['updated_by'],
        updated_at: row['updated_at'],
        deleted_by: row['deleted_by'],
        deleted_at: row['deleted_at'],
        code_count: Number(row['code_count']),
        applied_count: Number(row['applied_count']),
      } as PromotionWithCounts;
    }

    private toPromoCodeWithDetails(row: any): PromoCodeWithDetails {
      return {
        id: row['id'],
        promotion_id: row['promotion_id'],
        code: row['code'],
        code_limit: row['code_limit'],
        assigned_user_id: row['assigned_user_id'],
        status: row['status'],
        created_by: row['created_by'],
        created_at: row['created_at'],
        updated_by: row['updated_by'],
        updated_at: row['updated_at'],
        deleted_by: row['deleted_by'],
        deleted_at: row['deleted_at'],
        applied_count: Number(row['applied_count']),
        assigned_user_fullname: row['assigned_user_fullname'],
      } as PromoCodeWithDetails;
    }

    async selectPromotionsWithCounts(conn: Connection, page: number, pageSize: number): Promise<PromotionWithCounts[]> {
      const offset = (page - 1) * pageSize;
      const [rows] = await conn.query<RowDataPacket[]>(
        `
          SELECT p.*,
                 COUNT(DISTINCT pc.id)  AS code_count,
                 COUNT(DISTINCT pcu.id) AS applied_count
          FROM promo01promotion p
                 LEFT JOIN promo02promo_code pc ON p.id = pc.promotion_id
                 LEFT JOIN promo03promo_code_usage pcu ON pc.id = pcu.promo_code_id
          GROUP BY p.id
          ORDER BY p.created_at DESC
          LIMIT ? OFFSET ?
        `,
        [pageSize, offset]
      );
      return rows.map(row => this.toPromotionWithCounts(row));
    }

    async countPromotions(conn: Connection): Promise<number> {
      const [rows] = await conn.query<RowDataPacket[]>(`SELECT COUNT(*) AS total FROM promo01promotion`);
      return rows[0].total;
    }

    async selectPromoCodesWithCounts(conn: Connection, promotionId: number, page: number, pageSize: number): Promise<PromoCodeWithDetails[]> {
      const offset = (page - 1) * pageSize;
      const [rows] = await conn.query<RowDataPacket[]>(
        `
          SELECT pc.*,
                 COUNT(pcu.id) AS applied_count,
                 u.full_name AS assigned_user_fullname
          FROM promo02promo_code pc
                 LEFT JOIN promo03promo_code_usage pcu ON pc.id = pcu.promo_code_id
                 LEFT JOIN usr01members u ON pc.assigned_user_id = u.fbase_uid
          WHERE pc.promotion_id = ?
          GROUP BY pc.id
          ORDER BY pc.created_at DESC
          LIMIT ? OFFSET ?
        `,
        [promotionId, pageSize, offset]
      );
      return rows.map(row => this.toPromoCodeWithDetails(row));
    }

    async countPromoCodes(conn: Connection, promotionId: number): Promise<number> {
      const [rows] = await conn.query<RowDataPacket[]>(`SELECT COUNT(*) AS total FROM promo02promo_code WHERE promotion_id = ?`, [promotionId]);
      return rows[0].total;
    }

    async isPromoCodeUsed(conn: Connection, promoCodeId: number): Promise<boolean> {
      const [rows] = await conn.query<RowDataPacket[]>(
        `SELECT COUNT(*) as cnt FROM promo03promo_code_usage WHERE promo_code_id = ?`,
        [promoCodeId]
      );
      return rows[0].cnt > 0;
    }

    async assignMemberToPromoCode(conn: Connection, promoCodeId: number, memberId: string, by: By): Promise<number> {
      const sql = `update promo02promo_code set
      assigned_user_id = ?, updated_by = ?, updated_at = now()
      where id = ?`;
      const params = [memberId, by.uid, promoCodeId];
      const [results] = await conn.execute<ResultSetHeader>(sql, params);
      const affectedRows = results.affectedRows;
      if (affectedRows !== 1) {
        throw `promo_code not found, id=${promoCodeId}`;
      }
      logger.info(`[${by.username}] assigned member ${memberId} to promo_code id=${promoCodeId}`);
      return affectedRows;
    }
}