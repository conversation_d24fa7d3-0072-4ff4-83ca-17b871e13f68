import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import logger from "@utils/logger";
import { Package, PackageFindCriteria, PackageSaveReq, PackageSetStatusReq, PackageMoveSequenceReq } from "../models/package";
import { Page, PageOfData, Sort } from "@shared/services";

export class PackageRepo {

  async findById(conn: Connection, id: string): Promise<Package|undefined> {
    const sql = "select * from credit01package where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toPackage(results[0]);
  }

  async findAll(
    conn: Connection,
    criteria: PackageFindCriteria,
    sort?: Sort,
    page?: Page,
  ): Promise<PageOfData<Package>> {
    // prepare clauses and params
    const clauses = [];
    const params = [];

    if (criteria.packageId) {
      clauses.push("(id = ?)");
      params.push(criteria.packageId);
    }

    if (criteria.currency) {
      clauses.push("(currency = ?)");
      params.push(criteria.currency);
    }

    if (criteria.status !== undefined) {
      clauses.push("(status = ?)");
      params.push(criteria.status ? 1 : 0);
    }
    // Note: If status is undefined, we show all packages (both active and inactive)
    // The inclInactive parameter is redundant and can be removed

    // select items
    let selectSql = "select * from credit01package";
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }

    const orderBys: string[] = [];
    if (sort) {
      const sortDir = sort.sortAsc ? 'asc' : 'desc';
      const lookup = new Map<string, string>();
      lookup.set("name", `name ${sortDir}`);
      lookup.set("currency", `currency ${sortDir}`);
      lookup.set("price", `price ${sortDir}`);
      lookup.set("credits", `credits ${sortDir}`);
      lookup.set("status", `status ${sortDir}`);
      lookup.set("sequence", `sequence ${sortDir}`);
      lookup.set("createdAt", `created_at ${sortDir}`);
      if (lookup.has(sort.sort)) {
        orderBys.push(lookup.get(sort.sort)!);
      }
    }

    // Default ordering by sequence, then by name
    if (orderBys.length === 0) {
      orderBys.push("sequence asc", "name asc");
    }

    selectSql += ` order by ${orderBys.join(",")}`;

    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }

    const [results1] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = results1.map(result => this.toPackage(result));

    // select total
    let totalSql = "select count(*) as `count` from credit01package";
    if (clauses.length !== 0) {
      totalSql += ` where ${clauses.join(" and ")}`
    }
    const [results2] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = results2[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;

    // return page of data
    return {
      totalItems: totalItems,
      totalPages: totalPages,
      items: items,
    } as PageOfData<Package>;
  }

  async create(conn: Connection, req: PackageSaveReq): Promise<string> {
    const sql = `insert into credit01package (
        id, name, currency, price, credits, bonus_credits, status,
        first_time_only, instructor_only, valid_from, valid_to, purchase_limit, sequence,
        created_by, updated_by, updated_at
      ) values (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, now()
      )`;
    const by = req.by.username;
    const packageData = req.package;
    const uuid = crypto.randomUUID();
    logger.debug(`[${by}] create() package=${JSON.stringify(packageData)}`)
    const params = [
      uuid,
      packageData.name,
      packageData.currency,
      packageData.price,
      packageData.credits,
      packageData.bonusCredits,
      packageData.status ? 1 : 0,
      packageData.firstTimeOnly ? 1 : 0,
      packageData.instructorOnly ? 1 : 0,
      packageData.validFrom || null,
      packageData.validTo || null,
      packageData.purchaseLimit || null,
      packageData.sequence || null,
      by,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created package, id=${uuid}, numRows=${results.affectedRows}`);
    return uuid;
  }

  async update(conn: Connection, req: PackageSaveReq): Promise<number> {
    const sql = `update credit01package set
      name = ?, currency = ?, price = ?, credits = ?, bonus_credits = ?, status = ?,
      first_time_only = ?, instructor_only = ?, valid_from = ?, valid_to = ?, purchase_limit = ?, sequence = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const packageData = req.package;
    logger.debug(`[${by}] update() package=${JSON.stringify(packageData)}`)
    const params = [
      packageData.name,
      packageData.currency,
      packageData.price,
      packageData.credits,
      packageData.bonusCredits,
      packageData.status ? 1 : 0,
      packageData.firstTimeOnly ? 1 : 0,
      packageData.instructorOnly ? 1 : 0,
      packageData.validFrom || null,
      packageData.validTo || null,
      packageData.purchaseLimit || null,
      packageData.sequence || null,
      by,
      packageData.id,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated package, id=${packageData.id}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setStatus(conn: Connection, req: PackageSetStatusReq): Promise<number> {
    const sql = `update credit01package set
      status = ?, updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const params = [
      req.status ? 1 : 0,
      by,
      req.packageId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] set package status=${req.status}, id=${req.packageId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async getMaxSequence(conn: Connection, currency?: string): Promise<number> {
    let sql = "select max(sequence) as maxSeq from credit01package";
    const params = [];

    if (currency) {
      sql += " where currency = ?";
      params.push(currency);
    }

    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results[0].maxSeq || 0;
  }

  async getAdjacentPackage(conn: Connection, packageId: string, direction: 'up' | 'down'): Promise<Package|undefined> {
    const currentPackage = await this.findById(conn, packageId);
    if (!currentPackage || currentPackage.sequence === null || currentPackage.sequence === undefined) {
      return undefined;
    }

    const operator = direction === 'up' ? '<' : '>';
    const orderDir = direction === 'up' ? 'desc' : 'asc';

    const sql = `select * from credit01package
                 where currency = ? and sequence ${operator} ?
                 order by sequence ${orderDir}
                 limit 1`;
    const params = [currentPackage.currency, currentPackage.sequence];

    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length === 0) {
      return undefined;
    }

    return this.toPackage(results[0]);
  }

  async swapSequence(conn: Connection, packageId1: string, packageId2: string, by: string): Promise<void> {
    // Get current sequences
    const pkg1 = await this.findById(conn, packageId1);
    const pkg2 = await this.findById(conn, packageId2);

    if (!pkg1 || !pkg2) {
      throw new Error("One or both packages not found");
    }

    // Swap sequences
    const sql = `update credit01package set sequence = CASE
                 WHEN id = ? THEN ?
                 WHEN id = ? THEN ?
                 END, updated_by = ?
                 where id in (?, ?)`;
    const params = [
      packageId1, pkg2.sequence,
      packageId2, pkg1.sequence,
      by,
      packageId1, packageId2
    ];

    await conn.execute(sql, params);
    logger.info(`[${by}] swapped sequences between packages ${packageId1} and ${packageId2}`);
  }

  async reorderSequences(conn: Connection, currency: string, by: string): Promise<void> {
    // Use a single UPDATE query with CASE statement to reorder sequences
    const sql = `
      UPDATE credit01package 
      SET sequence = (
        SELECT new_seq 
        FROM (
          SELECT id, ROW_NUMBER() OVER (ORDER BY sequence ASC, updated_at DESC) as new_seq
          FROM credit01package 
          WHERE currency = ? AND sequence IS NOT NULL
        ) AS ranked
        WHERE ranked.id = credit01package.id
      ),
      updated_by = ?
      WHERE currency = ? AND sequence IS NOT NULL
    `;
    
    const [results] = await conn.execute<ResultSetHeader>(sql, [currency, by, currency]);
    
    logger.info(`[${by}] reordered sequences for currency ${currency}, updated ${results.affectedRows} packages`);
  }

  private toPackage(row: any): Package {
    return {
      id: row['id'],
      name: row['name'],
      currency: row['currency'],
      price: +row['price'],
      credits: +row['credits'],
      bonusCredits: +row['bonus_credits'],
      status: row['status'] === 1,
      firstTimeOnly: row['first_time_only'] === 1,
      instructorOnly: row['instructor_only'] === 1,
      validFrom: row['valid_from'],
      validTo: row['valid_to'],
      purchaseLimit: row['purchase_limit'],
      sequence: row['sequence'],
      createdAt: row['created_at'],
      updatedAt: row['updated_at'],
      createdBy: row['created_by'],
      updatedBy: row['updated_by'],
    } as Package;
  }
}