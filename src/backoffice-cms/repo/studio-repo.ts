import { GeocodeResult } from "@googlemaps/google-maps-services-js";
import { LatLng } from "@booking-api/models/search";
import { Page, PageOfData, Sort } from "@shared/services";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import { SocialMedia } from "../models/shared";
import {
  Studio,
  StudioCopyToLiveReq,
  StudioFindCriteria,
  StudioMainUser,
  StudioSaveRemarksReq,
  StudioSetActiveReq,
  StudioSetApprovedReq,
  StudioSetStarredReq,
} from "../models/studio";

export class StudioRepo {

  async findById(conn: Connection, id: string): Promise<Studio|undefined> {
    const sql = "select * from stu01studio_main_user where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toStudio(results[0]);
  }

  // Check if pub01 record exists
  async hasPubRecord(conn: Connection, id: string): Promise<boolean> {
    const sql = "select count(id) as count from pub01studios where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results[0]['count'] !== 0;
  }

  async findAll(
    conn: Connection,
    criteria: StudioFindCriteria,
    sort?: Sort,
    page?: Page,
  ): Promise<PageOfData<Studio>> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    if (criteria.studioId) {
      clauses.push("(id = ?)");
      params.push(criteria.studioId);
    }
    if (!criteria.inclDisabled) {
      clauses.push("(active is true)");
    }
    { // deleted
      clauses.push("(deleted is false)");
    }

    // select items
    let selectSql = "select * from stu01studio_main_user";
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }

    const orderBys: string[] = [];
    const starred = await this.findStarredStudioIds(conn, criteria.adminId!);
    if (starred.length !== 0) {
      orderBys.push(`FIELD(id, ${starred.map(o => `"${o}"`).join(",")}) = 0`);
    }
    if (sort) {
      const sortDir = sort.sortAsc ? 'asc' : 'desc';
      const lookup = new Map<string, string>();
      lookup.set("name"     , `name ${sortDir}`);
      lookup.set("contactNo", `contact_no ${sortDir}`);
      lookup.set("email"    , `email ${sortDir}`);
      lookup.set("mainUser" , `main_user ${sortDir}`);
      lookup.set("instrless", `instrless ${sortDir}`);
      lookup.set("active",    `active ${sortDir}`);
      lookup.set("pendingApproval", `pend_approval ${sortDir}`);
      if (lookup.has(sort.sort)) {
        orderBys.push(lookup.get(sort.sort)!);
      }
    }
    if (orderBys.length !== 0) {
      selectSql += ` order by ${orderBys.join(",")}`;
    }
    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    // logger.debug(selectSql);
    const [results1] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = results1.map(result => {
      const ret = this.toStudio(result);
      if (starred.includes(ret.id)) {
        ret.starred = true;
      }
      return ret;
    });

    // select total
    let totalSql = "select count(*) as `count` from stu01studio_main_user";
    if (clauses.length !== 0) {
      totalSql += ` where ${clauses.join(" and ")}`
    }
    const [results2] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = results2[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;

    // return page of data
    return {
      totalItems: totalItems,
      totalPages: totalPages,
      items: items,
    } as PageOfData<Studio>;
  }

  private async findStarredStudioIds(conn: Connection, adminId: string): Promise<string[]> {
    const sql = "select studio_id from adm02studio_stars where admin_id = ?";
    const params = [adminId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(row => row['studio_id']!);
  }

  private toStudio(row: any): Studio {
    const mainUser = row["main_user"];
    const mainUserEmail = row["main_user_email"];
    const mainUserJobTitle = row["main_user_job_title"];
    const mainUserContactNo = row["main_user_contact_no"];
    const geocode = row['geocode'];
    return {
      id: row['id'],
      name: row['name'],
      descr: row['descr'],
      contactNo: row['contact_no'],
      email: row['email'],
      address: row['address'],
      placeId: row['place_id'],
      geocode: geocode == null ? null : JSON.parse(geocode) as GeocodeResult,
      latLng: row['lat'] && row['lng']? new LatLng(
        +row['lat'],
        +row['lng'],
      ) : null,
      socialMedia: {
        facebook: row['facebook'],
        instagram: row['instagram'],
        website: row['website'],
      } as SocialMedia,
      usagePolicies: row['usage'],
      instrLess: row['instrless'] === 1,
      pictures: JSON.parse(row['pictures'] ?? '[]'),
      active: row['active'] === 1,
      pendingApproval: row['pend_approval'] == 1,
      hasDoorPin: row['has_door_pin'] == 1,
      backofficeRemarks: row['bo_remarks'],
      starred: row['starred'] == 1,
      deleted: row['deleted'] === 1,
      ...mainUser && { mainUser: {
        id: row['main_user_id'],
        name: mainUser,
        email: mainUserEmail,
        jobTitle: mainUserJobTitle,
        contactNo: mainUserContactNo,
      } as StudioMainUser},
    } as Studio;
  }

  /*
  async create(conn: Connection, req: StudioSaveReq): Promise<string> {
    const sql = `insert into stu01studios (
        id, name, descr, contact_no, email, address, place_id,
        geocode, lat, lng, latlng,
        facebook, instagram, \`usage\`,
        instrless,
        pictures, videos,
        created_by
      ) values (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?, ?, ?, ?
      )`;
    const by = req.by.username;
    const studio = req.studio;
    const lat = studio.geocode?.geometry.location.lat ?? null;
    const lng = studio.geocode?.geometry.location.lng ?? null;
    const latLng = (lat && lng) ? new LatLng(lat, lng) : null;
    const uuid = crypto.randomUUID();
    logger.debug(`[${by}] create() studio=${JSON.stringify(studio)}`)
    const params = [
      uuid,
      studio.name,
      studio.descr ?? '',
      studio.contactNo,
      studio.email,
      studio.address,
      studio.placeId,
      JSON.stringify(studio.geocode) ?? null,
      lat,
      lng,
      latLng?.toCacheString() ?? null,
      studio.socialMedia?.facebook ?? '',
      studio.socialMedia?.instagram ?? '',
      studio.usagePolicies ?? '',
      studio.instrLess ?? false,
      studio.pictures ?? '[]',
      studio.videos ?? '[]',
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created studio, id=${uuid}, numRows=${results.affectedRows}`);
    return uuid;
  }

  async update(conn: Connection, req: StudioSaveReq): Promise<number> {
    const sql = `update stu01studios set
      name = ?, descr = ?, contact_no = ?, email = ?, address = ?, place_id = ?,
      geocode = ?, lat = ?, lng = ?, latlng = ?,
      facebook = ?, instagram = ?, \`usage\` = ?,
      instrless = ?,
      pictures = ?, videos = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const studio = req.studio;
    const lat = studio.geocode?.geometry.location.lat ?? null;
    const lng = studio.geocode?.geometry.location.lng ?? null;
    const latLng = (lat && lng) ? new LatLng(lat, lng) : null;
    logger.debug(`[${by}] update() studio=${JSON.stringify(studio)}`)
    const params = [
      studio.name,
      studio.descr ?? '',
      studio.contactNo,
      studio.email,
      studio.address,
      studio.placeId,
      JSON.stringify(studio.geocode) ?? null,
      lat,
      lng,
      latLng?.toCacheString() ?? null,
      studio.socialMedia?.facebook ?? '',
      studio.socialMedia?.instagram ?? '',
      studio.usagePolicies ?? '',
      studio.instrLess ?? false,
      studio.pictures ?? '[]',
      studio.videos ?? '[]',
      by,
      studio.id,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated studio, id=${studio.id}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async insertPicture(conn: Connection, req: PictureSaveReq): Promise<string> {
    const sql = `insert into stu06pictures (
        id, user_id, path, created_by
      ) values (
        ?, ?, ?, ?
      )`;
    const by = req.by.username;
    const uuid = crypto.randomUUID();
    logger.debug(`[${by}] insertPicture() req=${JSON.stringify(req)}`)
    const params = [
      uuid,
      req.userId,
      req.path,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] inserted picture, id=${uuid}, numRows=${results.affectedRows}`);
    return uuid;
  }

  async findPictureById(conn: Connection, id: string): Promise<Picture|undefined> {
    const sql = "select *  from stu06pictures where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toPicture(results[0]);
  }

  private toPicture(row: any): Picture {
    return {
      id: row['id'],
      userId: row['userId'],
      path: row['path'],
    } as Picture;
  }
  */

  async setActive(
    conn: Connection,
    req: StudioSetActiveReq,
  ): Promise<number> {
    const sql = `update stu01studios set
      active = ?, updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const params = [
      req.active,
      by,
      req.studioId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] set studio active=${req.active}, id=${req.studioId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setLiveActive(conn: Connection, req: StudioSetActiveReq): Promise<number> {
    const sql = `update pub01studios set
      active = ?, updated_by = ?
      where id = ?`;
      const by = req.by.username;
    const params = [
      req.active,
      by,
      req.studioId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows !== 1) {
      throw `setLiveActive() (studio) expected exactly 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] setLiveActive(), active=${req.active}, studioId=${req.studioId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async copyToLive_insert(conn: Connection, req: StudioCopyToLiveReq): Promise<number> {
    const sql = `insert into pub01studios (
        id, main_user_id, name, descr, contact_no, email,
        address, place_id, geocode, lat, lng, latlng,
        instrless, facebook, instagram, website, \`usage\`, pictures, videos, active,
        -- pend_approval,
        deleted, deleted_at, deleted_by, created_at, created_by, updated_at, updated_by
      ) select
        id, main_user_id, name, descr, contact_no, email,
        address, place_id, geocode, lat, lng, latlng,
        instrless, facebook, instagram, website, \`usage\`, pictures, videos, active,
        -- pend_approval,
        deleted, deleted_at, deleted_by, created_at, created_by, updated_at, updated_by
      from stu01studios where id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] copyToLive_insert() req=${JSON.stringify(req)}`)
    const params = [req.studioId!];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] copyToLive_insert(), studioId=${req.studioId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async copyToLive_update(conn: Connection, req: StudioCopyToLiveReq): Promise<number> {
    const sql = `update
      pub01studios tgt inner join stu01studios src on (tgt.id = src.id)
      set
      tgt.name       = src.name,
      tgt.descr      = src.descr,
      tgt.contact_no = src.contact_no,
      tgt.email      = src.email,
      tgt.address    = src.address,
      tgt.place_id   = src.place_id,
      tgt.geocode    = src.geocode,
      tgt.lat        = src.lat,
      tgt.lng        = src.lng,
      tgt.latlng     = src.latlng,
      tgt.instrless  = src.instrless,
      tgt.facebook   = src.facebook,
      tgt.instagram  = src.instagram,
      tgt.website    = src.website,
      tgt.\`usage\`  = src.\`usage\`,
      tgt.pictures   = src.pictures,
      tgt.videos     = src.videos,
      tgt.active     = src.active,
      tgt.deleted    = src.deleted,
      tgt.deleted_at = src.deleted_at,
      tgt.deleted_by = src.deleted_by,
      tgt.updated_at = src.updated_at,
      tgt.updated_by = src.updated_by
      where src.id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] copyToLive_update() req=${JSON.stringify(req)}`)
    const params = [req.studioId!];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows !== 1) {
      throw `copyToLive_update() (studio) expected exactly 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] copyToLive_update(), studioId=${req.studioId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setApproved(conn: Connection, req: StudioSetApprovedReq): Promise<number> {
    const sql = `update stu01studios set
      pend_approval = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] setApproved() req=${JSON.stringify(req)}`)
    const params = [
      false, // (!) set pend_approval to false
      by,
      req.studioId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows !== 1) {
      throw `setApproved() (studio) expected exactly 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] setApproved(), studioId=${req.studioId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async saveRemarks(conn: Connection, req: StudioSaveRemarksReq): Promise<number> {
    const sql = `update stu01studios set
      bo_remarks = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] saveRemarks() req=${JSON.stringify(req)}`)
    const params = [
      req.value,
      by,
      req.studioId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows !== 1) {
      throw `saveRemarks() (studio) expected exactly 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] saveRemarks(), studioId=${req.studioId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setStarred(conn: Connection, req: StudioSetStarredReq): Promise<number> {
    return req.starred
      ? await this.unstar(conn, req)
      : await this.star(conn, req);
  }

  private async star(conn: Connection, req: StudioSetStarredReq): Promise<number> {
    const sql = `insert ignore into adm02studio_stars(
        admin_id, studio_id, created_by
      ) values (
        ?, ?, ?
      )`;
    const by = req.by.username;
    logger.debug(`[${by}] star() req=${JSON.stringify(req)}`)
    const params = [
      req.by.uid!, // admin_id
      req.studioId!,
      by,          // created_by
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] star(), studioId=${req.studioId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  private async unstar(conn: Connection, req: StudioSetStarredReq): Promise<number> {
    const sql = `delete from adm02studio_stars where admin_id = ? and studio_id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] unstar() req=${JSON.stringify(req)}`)
    const params = [
      req.by.uid!, // admin_id
      req.studioId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows > 1) {
      throw `unstar() (studio) expected max 1 row affected, actual=${affectedRows}`;
    }
    logger.info(`[${by}] unstar(), studioId=${req.studioId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  /*
  async deletePicture(conn: Connection, req: PictureDeleteReq): Promise<number> {
    const sql = `delete from stu06pictures where id = ? and user_id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] deletePicture() req=${JSON.stringify(req)}`)
    const params = [
      req.pictureId,
      req.userId,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] deleted picture, id=${req.pictureId}, numRows=${affectedRows}`);
    return affectedRows;
  }
  */
}
