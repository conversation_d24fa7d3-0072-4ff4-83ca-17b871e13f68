import dotenv from 'dotenv';

dotenv.config();

export interface EnvironmentConfig {
  // Node Environment
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;

  // Database Configuration
  DATABASE_HOST: string;
  DATABASE_NAME: string;
  DATABASE_PASSWORD: string;
  DATABASE_POOL_CONNECTION_LIMIT: number;
  DATABASE_PORT: number;
  DATABASE_TIMEZONE: string;
  DATABASE_USERNAME: string;

  // Backoffice Configuration
  BO_ID: string;
}

class EnvironmentManager {
  private config: EnvironmentConfig;

  constructor() {
    this.config = this.loadConfig();
  }

  private loadConfig(): EnvironmentConfig {
    return {
      // Node Environment
      NODE_ENV: (process.env.NODE_ENV as EnvironmentConfig['NODE_ENV']) || 'development',
      PORT: parseInt(process.env.PORT || '3000'),

      // Database Configuration
      DATABASE_HOST: process.env.DATABASE_HOST || 'localhost',
      DATABASE_PORT: parseInt(process.env.DATABASE_PORT || '3306'),
      DATABASE_USERNAME: process.env.DATABASE_USERNAME || 'root',
      DATABASE_PASSWORD: process.env.DATABASE_PASSWORD || '',
      DATABASE_NAME: process.env.DATABASE_NAME || 'vifit',
      DATABASE_TIMEZONE: process.env.DATABASE_TIMEZONE || '+08:00',
      DATABASE_POOL_CONNECTION_LIMIT: parseInt(process.env.DATABASE_POOL_CONNECTION_LIMIT || '10'),

      // Backoffice Configuration
      BO_ID: process.env.BO_ID,
    };
  }

  get<K extends keyof EnvironmentConfig>(key: K): EnvironmentConfig[K] {
    return this.config[key];
  }

  isDevelopment(): boolean {
    return this.config.NODE_ENV === 'development';
  }

  isProduction(): boolean {
    return this.config.NODE_ENV === 'production';
  }

  isTest(): boolean {
    return this.config.NODE_ENV === 'test';
  }
}

const env = new EnvironmentManager();

export default env;
