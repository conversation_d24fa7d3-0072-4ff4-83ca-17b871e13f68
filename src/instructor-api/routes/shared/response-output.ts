import { Blackout, Instructor, Mobility, Rates, WorkHours } from "@instructor-api/models/instructor";
import logger from "@utils/logger";
import { format } from "date-fns";

export function toInstructorJson(instr: Instructor) {
  return {
    id: instr.id ?? '',
    name: instr.name,
    descr: instr.descr ?? '',
    facebook: instr.facebook ?? '',
    instagram: instr.instagram ?? '',
    certifications: instr.certifications ?? '',
    specialisations: instr.specialisations ?? '',
    pictures: instr.pictures ?? [],
    onboarded: !!instr.onboarded,
    isCompleted: !!instr.isCompleted,
    fav: instr.fav ?? false,
    prefStudios: (instr.prefStudios ?? []).map(o => ({
      id: o.id,
      name: o.name,
      address: o.address,
      pictures: o.pictures ?? [],
    })),
    avgRating: Number(instr.avgRating ?? 0).toFixed(1),
    totalRatingCount: instr.totalRatingCount ?? 0,
    equipments: instr.equipments ?? [],
  }
}

export function toRatesJson(rates: Rates[]) {
  // @ts-ignore
  const ret = [];
  for (const o of rates) {
    const rates = o.rates;
    ret.push({
      type: o.type,
      rates: rates.map(rate => ({
        start: rate.start.substring(0, 5),
        end: rate.end.substring(0, 5),
        price: rate.price,
      })),
    });
  }
  return ret;
}

export function toWorkHoursJson(hours: WorkHours[]) {
  // @ts-ignore
  const ret = [];
  for (const o of hours) {
    const slots = o.slots;
    ret.push({
      type: o.type,
      slots: slots.map(rate => ({
        start: rate.start.substring(0, 5),
        end: rate.end.substring(0, 5),
      })),
    });
  }
  return ret;
}

export function toMobilityJson(mobility: Mobility) {
  return {
    dow: mobility.dow,
    ...mobility.locationId && { locationId: mobility.locationId },
    ...mobility.locationName && { locationName: mobility.locationName },
    distance: mobility.distance,
  } as Mobility;
}

export function toBlackoutJson(blackout: Blackout) {
  // Always return startDate, endDate, startTime, endTime, and fullDay
  const isFullDay = !!blackout.fullDay;
  return {
    id: blackout.id,
    name: blackout.name,
    startDate: blackout.startDate, // Already in YYYY-MM-DD format
    startTime: isFullDay ? '00:00' : blackout.startTime?.substring(0, 5),
    endDate: blackout.endDate,     // Already in YYYY-MM-DD format
    endTime: isFullDay ? '23:59' : blackout.endTime?.substring(0, 5),
    fullDay: isFullDay,
  } as Blackout;
}
