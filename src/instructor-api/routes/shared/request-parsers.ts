import { toBy } from "@shared/request-parsers";
import { auth } from "firebase-admin";
import { ContactNo} from "@member-api/models/member";

import {
  Blackout,
  BlackoutSaveReq,
  Instructor,
  InstructorSaveReq,
  InstructorSignUpReq,
  Mobility,
  MobilitySaveReq,
  Rate,
  Rates,
  RatesSaveReq,
  WorkHours,
  WorkHoursSaveReq,
  EquipmentTypesSaveReq
} from "../../models/instructor";
import DecodedIdToken = auth.DecodedIdToken;

export function toInstructorSaveReq(
  body: any, idToken: DecodedIdToken
): InstructorSaveReq {
  const instr = {
    id: '', // (!) default empty
    firebaseUid: idToken.uid,
    name: body.name,
    descr: body.descr,
    facebook: body.facebook,
    instagram: body.instagram,
    certifications: body.certifications,
    specialisations: body.specialisations,
    pictures: body.pictures ?? [],
    equipmentTypes: body.equipmentTypes ?? [],
    isCompleted: body.isCompleted ?? true
  } as Instructor;
  return {
    instructor: instr,
    by: toBy(idToken),
  } as InstructorSaveReq;
}

export function toInstructorSignUpReq(
  body: any, idToken: DecodedIdToken
): InstructorSignUpReq {
  return {
    id: idToken.uid,
    firebaseUid: idToken.uid,
    firebaseEmail: idToken.email,
    fullName: body.fullName,
    gender: body.gender,
    mobileNo: {
      countryCode: body.mobileNo?.countryCode,
      number: body.mobileNo?.number,
    } as ContactNo,
    by: toBy(idToken),
  } as InstructorSignUpReq;
}

export function toRatesSaveReq(
  body: any, idToken: DecodedIdToken
): RatesSaveReq {
  const map: any = {};
  for (const item of body) {
    const type = item.type;
    const rates = item.rates;
    for (const o of rates) {
      const rate = {
        start: o['start'],
        end: o['end'],
        price: o['price'],
      } as Rate;

      const val = map[type];
      if (val) {
        val.push(rate);
      } else {
        map[type] = [rate];
      }
    }
  }

  const rates: Rates[] = [];
  Object.keys(map).forEach((key: string) => {
    rates.push({
      type: key,
      rates: map[key] as Rate[],
    } as Rates);
  });

  return {
    rates: rates,
    by: toBy(idToken),
  } as RatesSaveReq;
}

export function toWorkHoursSaveReq(
  body: any, idToken: DecodedIdToken
): WorkHoursSaveReq {
  const map: any = {};
  for (const item of body) {
    const type = item.type;
    const slots = item.slots;
    for (const o of slots) {
      const slot = {
        start: o['start'],
        end: o['end'],
        price: o['price'],
      } as Rate;

      const val = map[type];
      if (val) {
        val.push(slot);
      } else {
        map[type] = [slot];
      }
    }
  }

  const hours: WorkHours[] = [];
  Object.keys(map).forEach((key: string) => {
    hours.push({
      type: key,
      slots: map[key] as Rate[],
    } as WorkHours);
  });

  return {
    slots: hours,
    by: toBy(idToken),
  } as WorkHoursSaveReq;
}

export function toMobilitySaveReq(
  body: any, idToken: DecodedIdToken
): MobilitySaveReq {
  const mobility = [];
  for (const o of body) {
    mobility.push({
      dow: o.dow,
      locationId: o.locationId,
      distance: o.distance,
    } as Mobility);
  }
  return {
    mobility: mobility,
    by: toBy(idToken),
  } as MobilitySaveReq;
}

export function toBlackoutSaveReq(
  body: any, idToken: DecodedIdToken
): BlackoutSaveReq {
  const blackout = {
    id: body.id ?? '',
    name: body.name,
    startDate: body.startDate,
    startTime: body.startTime,
    endDate: body.endDate,
    endTime: body.endTime,
    fullDay: body.fullDay,
  } as Blackout;
  return {
    blackout: blackout,
    by: toBy(idToken),
  } as BlackoutSaveReq;
}

export function toEquipmentTypesSaveReq(
  body: any, idToken: DecodedIdToken
): EquipmentTypesSaveReq {
  return {
    instructorId: body.instructorId,
    equipmentTypes: body.equipmentTypes ?? [],
    by: toBy(idToken),
  } as EquipmentTypesSaveReq;
}
