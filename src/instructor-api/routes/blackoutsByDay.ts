import { BlackoutFindCriteria } from "@instructor-api/models/instructor";
import { RequestHand<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toBlackoutJson } from "./shared/response-output";

const instrService = new InstructorService();

const blackoutsByDay: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const day = req.query.day!.toString();
  const blackouts = await instrService.findBlackouts({
    day: day,
    instructorId: uid,
  } as BlackoutFindCriteria);
  res.status(200).json(
    blackouts.map(o => toBlackoutJson(o))
  );
};

export default blackoutsByDay;
