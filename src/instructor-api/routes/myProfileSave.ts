import logger from "@utils/logger";
import { RequestHand<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toInstructorSaveReq } from "./shared/request-parsers";

const instrService = new InstructorService();

const myProfileSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;
  const ret = await instrService.findByFirebaseUid(uid);
  if (!ret || ret.deleted) {
    logger.info(`uid=${uid}, email=${idToken.email} does not exist!`);
    res.status(204).end(); // no content
    return;
  }

  // accept only request else set existing value
  if(body.isCompleted === undefined) {
    body.isCompleted = ret.isCompleted;
  }

  const toSave = toInstructorSaveReq(body, idToken);
  toSave.instructor.id = idToken.uid; // (!) important
  await instrService.update(toSave)
  res.status(200).end()
};

export default myProfileSave;
