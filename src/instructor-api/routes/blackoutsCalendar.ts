import { Blackout, BlackoutFindCriteria } from "@instructor-api/models/instructor";
import { DateTime } from "luxon";
import { Request<PERSON>and<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toBlackoutJson } from "./shared/response-output";

const instrService = new InstructorService();

const blackoutsCalendar: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const year = req.query.year!.toString();
  const month = req.query.month!.toString();

  const blackouts = await instrService.findBlackouts({
    month: `${year}-${month.padStart(2, "0")}`,
    instructorId: uid,
  } as BlackoutFindCriteria);
  const map: Map<String, Blackout[]> = groupByDateRange(blackouts, req.userTimezone);
  res.status(200).json(Object.fromEntries(map));
};

function groupByDateRange(blackouts: Blackout[], userTimezone: string) {
  const ret = new Map<String, Blackout[]>();
  for (const blackout of blackouts) {
    // Use Luxon for date range
    const start = DateTime.fromISO(blackout.startDate, { zone: userTimezone });
    const end = DateTime.fromISO(blackout.endDate, { zone: userTimezone });
    let cursor = start;
    while (cursor <= end) {
      const key = cursor.toFormat('yyyy-MM-dd');
      const blackoutJson = toBlackoutJson(blackout);
      if (ret.has(key)) {
        ret.get(key)!.push(blackoutJson);
      } else {
        ret.set(key, [blackoutJson]);
      }
      cursor = cursor.plus({ days: 1 });
    }
  }
  return ret;
}

export default blackoutsCalendar;
