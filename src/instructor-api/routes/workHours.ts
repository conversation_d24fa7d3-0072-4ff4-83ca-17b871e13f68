import { Request<PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toWorkHoursJson } from "./shared/response-output";

const instrService = new InstructorService();

const workHours: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const ret = await instrService.getWorkHours(uid);
  res.status(200).json(toWorkHoursJson(ret));
};

export default workHours;
