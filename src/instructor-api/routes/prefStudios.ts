import { PrefService } from "@instructor-api/services/pref-service";
import { toStudioJson } from "@studio-cms/routes/shared/response-output";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const prefService = new PrefService();

const prefStudios: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const ret = await prefService.getPrefStudios(uid);
  res.status(200).json(
    ret.map(o => toStudioJson(o))
  );
};

export default prefStudios;
