import { PrefStudioSaveReq } from "@instructor-api/models/pref";
import { PrefService } from "@instructor-api/services/pref-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const prefService = new PrefService();

const prefStudiosAdd: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  await prefService.addPrefStudio({
    userId: uid,
    studioId: req.params['studioId']!,
    by: toBy(idToken),
} as PrefStudioSaveReq)
  res.status(200).end()
};

export default prefStudiosAdd;
