import { ServiceError } from "@shared/services";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toInstructorSaveReq } from "./shared/request-parsers";

const instrService = new InstructorService();

const register: RequestHandler = async (req, res) => {
  try {
    const idToken = getIdToken(req);
    const body = req.body;

    const toSave = toInstructorSaveReq(body, idToken);
    toSave.instructor.id = idToken.uid; // (!) important
    toSave.instructor.isCompleted = false; // (!) important (v2 sign-up flow)

    // Await the service call to ensure proper handling
    await instrService.create(toSave);
    res.status(200).end(); // Send success response
  } catch (error) {
    handleError(error, res); // Pass response to handleError
  }
};

// Error handling function
const handleError = (error: any, res: any) => {
  if (error instanceof ServiceError) {
    if (error.code === "409") {
      res.status(409).end();
    } else {
      res.status(500).end();
    }
  } else {
    res.status(500).end();
  }
};

export default register;
