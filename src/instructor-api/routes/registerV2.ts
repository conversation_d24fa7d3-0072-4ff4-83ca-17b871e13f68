import { ServiceError } from "@shared/services";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toInstructorSignUpReq } from "./shared/request-parsers";
import { createCometChatUser } from "@utils/cometchat";
import logger from "@utils/logger";
import { CometChatUser } from "@public-api/models/cometchat";


const instrService = new InstructorService();

const registerV2: RequestHandler = async (req, res) => {
  try {
    const idToken = getIdToken(req);
    const body = req.body;
    body.name = body.fullName;
    const toSaveInstructor = toInstructorSignUpReq(body, idToken);

    await instrService.createV2(toSaveInstructor);

    // CometChat integration (non-blocking)
    try {
      const result = await createCometChatUser({
        uid: toSaveInstructor.firebaseUid,
        email: toSaveInstructor.firebaseEmail,
        name: toSaveInstructor.fullName,
        phone: toSaveInstructor.mobileNo?.countryCode + toSaveInstructor.mobileNo?.number,
        tags: ["member", "instructor"],
        userType: "instructor",
      } as CometChatUser);

      if (!result.success) {
        logger.warn(`[CometChat] Instructor user creation failed for userId=${toSaveInstructor.firebaseUid}: ${result.message}`);
      }
    } catch (e) {
      logger.warn(`[CometChat] Instructor user creation failed for userId=${toSaveInstructor.firebaseUid}: ${e}`);
    }

    res.status(200).end();
  } catch (error) {
    handleError(error, res);
  }
};

// Error handling function
const handleError = (error: any, res: any) => {
  if (error instanceof ServiceError) {
    if (error.code === "409") {
      res.status(409).end();
    } else {
      res.status(500).end();
    }
  } else {
    res.status(500).end();
  }
};

export default registerV2;
