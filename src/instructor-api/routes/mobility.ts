import { FaveService } from "@member-api/services/fave-service";
import { StudioService } from "@studio-cms/services/studio-service";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toMobilityJson } from "./shared/response-output";

const instrService = new InstructorService();
const studioService = new StudioService();
const faveService = new FaveService();

interface Fave {
  id: string,
  name: string,
}

const mobility: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const mobilityList = await instrService.getMobility(uid);
  const faves: Fave[] = (await faveService.getFaveStudios(uid)).map(f => ({
    id: f.id,
    name: f.name,
  }));

  for (const m of mobilityList) {
    const studio = await studioService.findById(m.locationId);
    m.locationName = studio?.name;

    if (!m.locationId || !m.locationName) {
      continue;
    }

    const found = faves.find(f => f.id === m.locationId);
    if (!found) {
      faves.push({
        id: m.locationId,
        name: m.locationName,
      } as Fave)
    }
  }
  faves.sort((o1, o2) => o1.name.localeCompare(o2.name));

  res.status(200).json({
    mobility: mobilityList.map(o => toMobilityJson(o)),
    options: faves.map(o => ({
      id: o.id,
      name: o.name,
    })),
  });
};

export default mobility;
