import { Request<PERSON>and<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toWorkHoursSaveReq } from "./shared/request-parsers";

const instrService = new InstructorService();

const workHoursSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;

  const toSave = toWorkHoursSaveReq(body, idToken);
  toSave.instructorId = uid; // (!) important
  await instrService.saveWorkHours(toSave);
  res.status(200).end();
};

export default workHoursSave;
