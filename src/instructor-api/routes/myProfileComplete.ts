import logger from "@utils/logger";
import { RequestHand<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { CompleteSaveReq } from '@instructor-api/models/instructor';
import { toBy } from "@shared/request-parsers";

const instrService = new InstructorService();

const myProfileComplete: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;
  const ret = await instrService.findByFirebaseUid(uid);
  if (!ret || ret.deleted) {
    logger.info(`uid=${uid}, email=${idToken.email} does not exist!`);
    res.status(204).end(); // no content
    return;
  }
  await instrService.complete({
    instructorId: ret.id,
    pictures: body.pictures ?? [],
    by: toBy(idToken)
  } as CompleteSaveReq);
  res.status(200).end();
};

export default myProfileComplete;
