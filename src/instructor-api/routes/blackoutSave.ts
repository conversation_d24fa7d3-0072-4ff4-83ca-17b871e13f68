import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toBlackoutSaveReq } from "./shared/request-parsers";

const instrService = new InstructorService();

const blackoutSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;

  const toSave = toBlackoutSaveReq(body, idToken);
  toSave.instructorId = uid; // (!) important
  if (toSave.blackout.id) {
    res.status(422).end(); // unprocessable content
  } else {
    await instrService.insertBlackout(toSave);
    res.status(200).end();
  }
};

export default blackoutSave;
