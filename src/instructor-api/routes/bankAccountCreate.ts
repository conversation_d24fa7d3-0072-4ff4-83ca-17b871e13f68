import { Response } from "express";

import { BankService } from "@backoffice-cms/services/bank-account-service";
import { ApiRequest, ENTITY_TYPES } from "@shared/constants";
import { toBankAccountCreateJson } from "@database/models/bank-account";
import { requireInstructorAccess } from "@instructor-api/middlewares/instructor-access";

const bankService = new BankService();

export default requireInstructorAccess(async (req: ApiRequest, res: Response) => {
  const { idToken: { uid } } = req;
  const result = await bankService.createBankAccount(req, ENTITY_TYPES.INSTRUCTOR, uid);
  res.status(200).json({
    success: true,
    data: toBankAccountCreateJson(result)
  });
});
