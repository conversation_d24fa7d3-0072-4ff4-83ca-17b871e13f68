import s3 from "@utils/aws-s3";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { InstructorService } from "@instructor-api/services/instructor-service";
import { toBy } from "@shared/request-parsers";
import logger from "@utils/logger";
import { format } from "date-fns";
import { RequestHandler } from "express";
import path from "path";
import crypto from "crypto";
import { getIdToken } from "../../init-openapi";
import fs from "fs/promises";
import { DateTime } from "luxon";
import { getCurrentTimeInUserTimezone } from "@shared/middleware/timezone";

const instrService = new InstructorService();

const myProfilePictureUpload: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  // Get uploaded file
  const files = req.files as Express.Multer.File[];
  const file = files?.find(o => o.fieldname === "file");
  if (!file) {
    res.status(400).end("file is required");
    return;
  }

  const uploadedFile = file.path;
  const uploadedFileExt = path.extname((file.originalname ?? '').trim());
  const now = getCurrentTimeInUserTimezone(req);
  const s3Key = `${now.toFormat('yyyy/MM-dd')}/${crypto.randomUUID()}${uploadedFileExt}`;

  // Read file and upload to S3
  const fileBuffer = await fs.readFile(uploadedFile);
  await s3.send(new PutObjectCommand({
    Bucket: process.env.AWS_S3_BUCKET!,
    Key: `instr-pics/${s3Key}`,
    Body: fileBuffer,
    ContentType: file.mimetype,
  }));

  // Optionally, delete the local file after upload
  await fs.unlink(uploadedFile);

  // Store S3 key (or full URL) in DB
  const ret = await instrService.insertPicture({
    userId: uid,
    path: s3Key, // or use the full S3 URL if you prefer
    by: toBy(idToken),
  });
  res.status(200).end(ret);
};

export default myProfilePictureUpload;
