import { Request<PERSON>and<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toMobilitySaveReq } from "./shared/request-parsers";

const instrService = new InstructorService();

const mobilitySave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;

  const toSave = toMobilitySaveReq(body, idToken);
  toSave.instructorId = uid; // (!) important
  await instrService.saveMobility(toSave);
  res.status(200).end();
};

export default mobilitySave;
