import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toInstructorJson } from "./shared/response-output";

const instrService = new InstructorService();

const myAccount: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const ret = await instrService.findByFirebaseUid(uid);
  if (!ret || ret.deleted) {
    res.status(204).end(); // no content
  } else {
    ret.equipments = await instrService.getEquipmentTypesWithName(uid);
    const json = toInstructorJson(ret);
    res.status(200).json(json);
  }
};

export default myAccount;
