import { BlackoutDeleteReq } from "@instructor-api/models/instructor";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";

const instrService = new InstructorService();

const blackoutsByDay: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const id = req.params.id!.toString().trim();
  const ret = await instrService.deleteBlackout({
    blackoutId: id,
    instructorId: uid, // (!) important
    by: toBy(idToken),
  } as BlackoutDeleteReq);
  res.status(200).end();
};

export default blackoutsByDay;
