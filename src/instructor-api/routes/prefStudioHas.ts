import { PrefService } from "@instructor-api/services/pref-service";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const prefService = new PrefService();

const prefStudioHas: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const studioId = req.params.studioId!;
  const ret = await prefService.hasPrefStudio(uid, studioId);
  res.status(200).json(ret);
};

export default prefStudioHas;
