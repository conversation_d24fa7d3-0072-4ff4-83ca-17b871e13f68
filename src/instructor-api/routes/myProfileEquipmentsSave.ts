import logger from "@utils/logger";
import { RequestHand<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toEquipmentTypesSaveReq } from "./shared/request-parsers";

const instrService = new InstructorService();

const myProfileEquipmentsSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;
  const ret = await instrService.findByFirebaseUid(uid);
  if (!ret || ret.deleted) {
    logger.info(`uid=${uid}, email=${idToken.email} does not exist!`);
    res.status(204).end(); // no content
    return;
  }
  body.instructorId = uid;
  const toSave = toEquipmentTypesSaveReq(body, idToken);
  await instrService.saveEquipmentTypes(toSave)
  res.status(200).end()
};

export default myProfileEquipmentsSave;
