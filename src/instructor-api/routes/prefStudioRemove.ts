import { PrefService } from "@instructor-api/services/pref-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const prefService = new PrefService();

const prefStudioRemove: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const studioId = req.params.studioId!;
  const ret = await prefService.removePrefStudio({
    userId: uid,
    studioId: studioId,
    by: toBy(idToken),
  });
  res.status(200).json();
};

export default prefStudioRemove;
