import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toRatesSaveReq } from "./shared/request-parsers";

const instrService = new InstructorService();

const ratesSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;

  const toSave = toRatesSaveReq(body, idToken);
  toSave.instructorId = uid; // (!) important
  await instrService.saveRates(toSave);
  res.status(200).end();
};

export default ratesSave;
