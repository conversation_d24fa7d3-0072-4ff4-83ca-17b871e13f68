import { Response } from "express";

import { BankService } from "@backoffice-cms/services/bank-account-service";
import { ApiRequest } from "@shared/constants";
import { requireInstructorAccess } from "@instructor-api/middlewares/instructor-access";

const bankService = new BankService();

export default requireInstructorAccess(async (req: ApiRequest, res: Response) => {
  const result = await bankService.updateBankAccountStatus(req);
  res.status(200).json({
    success: true,
    data: result
  });
});
