import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";
import { toRatesJson } from "./shared/response-output";

const instrService = new InstructorService();

const rates: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const ret = await instrService.getRates(uid);
  res.status(200).json(toRatesJson(ret));
};

export default rates;
