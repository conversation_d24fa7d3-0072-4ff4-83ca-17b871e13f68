import { Request<PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { InstructorService } from "../services/instructor-service";

const instrService = new InstructorService();

const exists: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const ret = await instrService.findByFirebaseUid(uid);
  if (!ret || ret.deleted) {
    res.status(204).end(); // no content
  } else {
    res.status(200).end();
  }
};

export default exists;
