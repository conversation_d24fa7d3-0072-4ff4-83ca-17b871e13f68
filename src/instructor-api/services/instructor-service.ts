import { Picture, PictureSaveReq } from "@shared/models";
import { ServiceError } from "@shared/services";
import { MemberRepo } from "@member-api/repo/member-repo";
import { PublicRepo } from "@public-api/repo/public-repo";
import { CreditRepo } from "@member-api/repo/credit-repo";
import logger from "@utils/logger";
import pool from "../../init-pool";
import {
  Blackout,
  BlackoutDeleteReq,
  BlackoutFindCriteria,
  BlackoutSaveReq,
  CompleteSaveReq,
  EquipmentTypesSaveReq,
  Instructor,
  InstructorEquipment,
  InstructorSaveReq,
  InstructorSignUpReq,
  Mobility,
  MobilitySaveReq,
  Rates,
  RatesSaveReq,
  WorkHours,
  WorkHoursSaveReq
} from "../models/instructor";
import { InstructorRepo } from "../repo/instructor-repo";
import { ContactNo, Member } from "@member-api/models/member";
import { Transaction, TransactionType, TransactionStatus, TransactionSaveReq } from "@booking-api/models/credit";
import { CreditRepo as BookingCreditRepo } from "@booking-api/repo/credit-repo";

export class InstructorService {

  private instrRepo = new InstructorRepo();
  private memberRepo = new MemberRepo();
  private publicRepo = new PublicRepo();
  private creditRepo = new CreditRepo();
  private bookingCreditRepo = new BookingCreditRepo();

  // Equivalent to findByFirebaseUid()
  async findById(id: string): Promise<Instructor|undefined> {
    return this.findByFirebaseUid(id);
  }

  // Equivalent to findById()
  async findByFirebaseUid(uid: string): Promise<Instructor|undefined> {
    return this.instrRepo.findByFirebaseUid(pool, uid);
  }

  async create(req: InstructorSaveReq): Promise<string> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // check if uid exists
      const user = await this.instrRepo.findByFirebaseUid(conn, req.instructor.firebaseUid);
      if (user) {
        logger.info(`uid=${req.instructor.firebaseUid} already exists`);
        throw new ServiceError("409", "uid already exists");
      }
      // create user
      const ret = await this.instrRepo.create(conn, req);
      // save equipment types if provided
      if (req.instructor.equipmentTypes?.length) {
        const equipReq: EquipmentTypesSaveReq = {
          instructorId: ret,
          equipmentTypes: req.instructor.equipmentTypes,
          by: req.by,
        };
        await this.instrRepo.insertEquipmentTypes(conn, equipReq);
      }

      await conn.commit();
      logger.info(`create instr success, userId=${ret}`);
      return ret;
    } catch (e) {
      logger.error(`create instr error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async createV2(req: InstructorSignUpReq): Promise<string> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // check if uid exists
      const instr = await this.instrRepo.findByFirebaseUid(conn, req.firebaseUid);
      if (instr) {
        logger.info(`uid=${req.firebaseUid} already exists`);
        throw new ServiceError("409", "account already exists");
      }

      const member = await this.memberRepo.findByFirebaseUid(conn, req.firebaseUid);

      // Member not exists (New user for ViFit)
      if(!member) {
        // create member account
        const memberId = await this.memberRepo.create(conn, {
          member: {
            fullName: req.fullName,
            firebaseUid: req.firebaseUid,
            firebaseEmail: req.firebaseEmail,
            displayName: req.fullName,
            gender: req.gender,
            mobileNo: {
              countryCode: req.mobileNo?.countryCode,
              number: req.mobileNo?.number,
            } as ContactNo,
          } as Member,
          by: req.by
        });

        // create credit record for new member
        await this.creditRepo.create(conn, memberId, req.by.username);

        if(+process.env.WELCOME_FREE_CREDIT > 0) {
          const transaction = {
            memberId: memberId,
            type: TransactionType.Promotion,
            amount: +process.env.WELCOME_FREE_CREDIT,
            status: TransactionStatus.Success,
            remark: 'Welcome Rewards',
          } as Transaction;

          await this.bookingCreditRepo.createTrx(conn, {
              transaction: transaction,
              by: req.by,
          } as TransactionSaveReq);

          await this.creditRepo.updateMemberCredit(conn, memberId, Math.abs(+process.env.WELCOME_FREE_CREDIT), 'RegistrationTrigger');
        }
      }

      // create instructor account
      const ret = await this.instrRepo.create(conn, {
        instructor: {
          id: req.id,
          firebaseUid: req.firebaseUid,
          name: req.fullName,
          isCompleted: false,
        } as Instructor,
        by: req.by
      } as InstructorSaveReq);

      await conn.commit();
      logger.info(`create instr success, userId=${ret}`);
      return ret;
    } catch (e) {
      logger.error(`create instr error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async update(req: InstructorSaveReq): Promise<number> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // update instructor
      const affectedRows = await this.instrRepo.update(conn, req);

      // update equipment types if provided
      if (req.instructor.equipmentTypes) {
        const equipReq: EquipmentTypesSaveReq = {
          instructorId: req.instructor.id,
          equipmentTypes: req.instructor.equipmentTypes,
          by: req.by,
        };
        await this.instrRepo.deleteEquipmentTypes(conn, equipReq);
        if (equipReq.equipmentTypes.length) {
          await this.instrRepo.insertEquipmentTypes(conn, equipReq);
        }
      }

      await conn.commit();
      return affectedRows;
    } catch (e) {
      logger.error(`update instr error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  //////////////////////
  // Complete Profile //
  //////////////////////
  async complete(req: CompleteSaveReq): Promise<number> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // update instructor
      const affectedRows = await this.instrRepo.complete(conn, req);
      await conn.commit();
      return affectedRows;
    } catch (e) {
      logger.error(`update instr error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  //////////////
  // Pictures //
  //////////////

  async insertPicture(req: PictureSaveReq): Promise<string> {
    return this.instrRepo.insertPicture(pool, req);
  }

  async findPictureById(id: string): Promise<Picture|undefined> {
    return this.instrRepo.findPictureById(pool, id);
  }

  ///////////
  // Rates //
  ///////////

  async getRates(instructorId: string): Promise<Rates[]> {
    return this.instrRepo.findRates(pool, instructorId);
  }

  async saveRates(req: RatesSaveReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();
      await this.instrRepo.deleteRates(conn, req);
      await this.instrRepo.insertRates(conn, req);
      await conn.commit();
      logger.info(`saveRates() success, instrId=${req.instructorId}`);
    } catch (e) {
      logger.error(`saveRates error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  ////////////////
  // Work Hours //
  ////////////////

  async getWorkHours(instructorId: string): Promise<WorkHours[]> {
    return this.instrRepo.findWorkHours(pool, instructorId);
  }

  async saveWorkHours(req: WorkHoursSaveReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();
      await this.instrRepo.deleteWorkHours(conn, req);
      await this.instrRepo.insertWorkHours(conn, req);
      await conn.commit();
      logger.info(`saveWorkHours() success, instrId=${req.instructorId}`);
    } catch (e) {
      logger.error(`saveWorkHours error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  //////////////
  // Mobility //
  //////////////

  async getMobility(instructorId: string): Promise<Mobility[]> {
    return this.instrRepo.findMobility(pool, instructorId);
  }

  async saveMobility(req: MobilitySaveReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();
      await this.instrRepo.deleteMobility(conn, req);
      await this.instrRepo.insertMobility(conn, req);
      await conn.commit();
      logger.info(`saveMobility() success, instrId=${req.instructorId}`);
    } catch (e) {
      logger.error(`saveMobility error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  //////////////
  // Blackout //
  //////////////

  async findBlackouts(criteria: BlackoutFindCriteria): Promise<Blackout[]> {
    return this.instrRepo.findBlackouts(pool, criteria);
  }

  async deleteBlackout(req: BlackoutDeleteReq): Promise<number> {
    return this.instrRepo.deleteBlackout(pool, req);
  }

  async insertBlackout(req: BlackoutSaveReq): Promise<string> {
    return this.instrRepo.insertBlackout(pool, req);
  }

  /////////////////
  // Equipment //
  /////////////////

  async getEquipmentTypes(instructorId: string): Promise<string[]> {
    return this.instrRepo.findEquipmentTypes(pool, instructorId);
  }

  async getEquipmentTypesWithName(instructorId: string): Promise<InstructorEquipment[]> {
    return this.publicRepo.getInstructorEquipmentTypeByIds(pool, [instructorId]);
  }

  async saveEquipmentTypes(req: EquipmentTypesSaveReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();
      await this.instrRepo.deleteEquipmentTypes(conn, req);
      if (req.equipmentTypes.length) {
        await this.instrRepo.insertEquipmentTypes(conn, req);
      }
      await conn.commit();
      logger.info(`saveEquipmentTypes() success, instrId=${req.instructorId}`);
    } catch (e) {
      logger.error(`saveEquipmentTypes error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }
}
