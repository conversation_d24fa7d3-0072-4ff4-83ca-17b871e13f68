import { PrefStudioSaveReq } from "@instructor-api/models/pref";
import { PrefRepo } from "@instructor-api/repo/pref-repo";
import { Studio } from "@studio-cms/models/studio";
import { PublicService } from "@public-api/services/public-service";
import pool from "../../init-pool";
import { StudioRepo } from "@studio-cms/repo/studio-repo";

export class PrefService {

  private prefRepo = new PrefRepo();
  private studioRepo = new StudioRepo();

  ///////////////////////
  // Preferred Studios //
  ///////////////////////

  async addPrefStudio(req: PrefStudioSaveReq): Promise<number> {
    return this.prefRepo.addPrefStudio(pool, req);
  }

  async removePrefStudio(req: PrefStudioSaveReq): Promise<number> {
    return this.prefRepo.removePrefStudio(pool, req);
  }

  async getPrefStudios(userId: string): Promise<Studio[]> {
    const ret = await this.prefRepo.getPrefStudios(pool, userId);
    for (const o of ret) {
      o.pictures = await this.studioRepo.prefixStudioPictures(pool, o.pictures);
    }
    return ret;
  }

  async hasPrefStudio(userId: string, studioId: string): Promise<boolean> {
    return this.prefRepo.hasPrefStudio(pool, userId, studioId);
  }
}
