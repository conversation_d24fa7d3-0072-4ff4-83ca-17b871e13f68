import { Response, NextFunction, RequestHandler } from "express";

import { getIdToken } from "../../init-openapi";
import { ApiRequest } from "@shared/constants";
import { ServiceError } from "@shared/services";
import { InstructorService } from "../services/instructor-service";

const instrService = new InstructorService();

export const requireInstructorAccess = (handler: RequestHandler): RequestHandler => {
  return async (req: ApiRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const idToken = getIdToken(req);
      const uid = idToken.uid;

      const instructor = await instrService.findByFirebaseUid(uid);
      if (!instructor || instructor.deleted) {
        throw new ServiceError("204", `not authorised`);
      }

      req.idToken = idToken;
      req.auth = { countryCode: 'SG' }; // TODO: get country code from instr01instructors table

      await handler(req, res, next);
    } catch (err) {
      res.status(Number(err.code) || 500).json({ success: false, message: err.message });
    }
  };
}
