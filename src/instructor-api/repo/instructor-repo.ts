import { Picture, PictureSaveReq } from "@shared/models";
import { convertMySQLDateToTimezoneString } from "@shared/utils/timezone-formatter";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import {
  Blackout,
  BlackoutDeleteReq,
  BlackoutFindCriteria,
  BlackoutSaveReq,
  CompleteSaveReq,
  EquipmentTypesSaveReq,
  Instructor,
  InstructorEquipment,
  InstructorSaveReq,
  Mobility,
  MobilitySaveReq,
  Rate,
  Rates,
  RatesSaveReq,
  WorkHours,
  WorkHoursSaveReq,
  WorkHoursSlot
} from "../models/instructor";
import { SessionFeedback } from "@public-api/models/public";
import { By } from "@shared/models";

export class InstructorRepo {

  async findByFirebaseUid(conn: Connection, uid: string): Promise<Instructor|undefined> {
    const sql = "select * from instr01instructors where fbase_uid = ?";
    const params = [uid];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return InstructorRepo.toInstructor(results[0]);
  }

  static toInstructor(row: any): Instructor {
    return {
      id: row['id'],
      firebaseUid: row['fbase_uid'],
      name: row['name'],
      descr: row['descr'],
      facebook: row['facebook'],
      instagram: row['instagram'],
      certifications: row['certs'],
      specialisations: row['specs'],
      pictures: JSON.parse(row['pictures'] ?? '[]'),
      registeredAt: row['reg_at'],
      onboarded: row['onboarded'] === 1,
      pendingApproval: row['pend_approval'] == 1,
      isCompleted: row['is_complete'] == 1,
      createdBy: row['created_by'],
      deleted: row['deleted'] === 1,
    }
  }

  static toEquipment(row: any): InstructorEquipment {
    return {
      instructorId: row['instr_id'],
      typeId: row['type_id'],
      typeName: row['type_name'],
    }
  }

  static toSessionFeedback(row: any): SessionFeedback {
    return {
      rating: row['m_instructor_rating'],
      feedback: row['m_instructor_feedback'],
      feedbackAt: row['m_feedback_at'],
    }
  }

  async create(conn: Connection, req: InstructorSaveReq): Promise<string> {
    const sql = `insert into instr01instructors (
        id, fbase_uid, name, descr,
        facebook, instagram, certs, specs,
        pictures,
        reg_at,
        pend_approval,
        is_complete,
        created_by
      ) values (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, now(), ?,
        ?, ?
      )`;
    const by = req.by.username;
    const instr = req.instructor;
    logger.debug(`[${by}] create() instr=${JSON.stringify(instr)}`)
    const params = [
      instr.firebaseUid, // (!) id = firebaseUid
      instr.firebaseUid,
      instr.name,
      instr.descr ?? '',
      instr.facebook ?? '',
      instr.instagram ?? '',
      instr.certifications ?? '',
      instr.specialisations ?? '',
      instr.pictures ?? '[]',
      true, // (!) pending approval
      instr.isCompleted ?? true, // (!) default v1 set true
      by,   // created_by
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created instr, id=${instr.firebaseUid}, numRows=${results.affectedRows}`);
    return instr.firebaseUid;
  }

  async update(conn: Connection, req: InstructorSaveReq): Promise<number> {
    const sql = `update instr01instructors set
      name = ?, descr = ?,
      facebook = ?, instagram = ?, certs = ?, specs = ?,
      pictures = ?,
      pend_approval = ?,
      is_complete = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const instr = req.instructor;
    logger.debug(`[${by}] update() instr=${JSON.stringify(instr)}`)
    const params = [
      instr.name,
      instr.descr ?? '',
      instr.facebook ?? '',
      instr.instagram ?? '',
      instr.certifications ?? '',
      instr.specialisations ?? '',
      instr.pictures ?? '[]',
      true, // (!) pending approval
      instr.isCompleted ?? true, // (!) default v1 set true
      by,   // updated_by
      instr.id,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated instr, id=${instr.id}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async complete(conn: Connection, req: CompleteSaveReq): Promise<number> {
    const sql = `update instr01instructors set
      pictures = ?,
      is_complete = true
      where id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] update() instr=${JSON.stringify(req)}`)
    const params = [
      req.pictures ?? [],
      req.instructorId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] completed instr profile, id=${req.instructorId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  //////////////
  // Pictures //
  //////////////

  async insertPicture(conn: Connection, req: PictureSaveReq): Promise<string> {
    const sql = `insert into instr06pictures (
        id, user_id, path, created_by
      ) values (
        ?, ?, ?, ?
      )`;
    const by = req.by.username;
    const uuid = crypto.randomUUID();
    logger.debug(`[${by}] insertPicture() req=${JSON.stringify(req)}`)
    const params = [
      uuid,
      req.userId,
      req.path,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] inserted picture, id=${uuid}, numRows=${results.affectedRows}`);
    return uuid;
  }

  async findPictureById(conn: Connection, id: string): Promise<Picture|undefined> {
    const sql = "select *  from instr06pictures where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toPicture(results[0]);
  }

  async findPictureByIds(conn: Connection, ids: string[]): Promise<Picture[]> {
    const placeholders = ids.map(() => '?').join(',');
    const sql = `select *  from instr06pictures where id in (${placeholders})`;
    const params = ids;
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(this.toPicture);
  }

  private toPicture(row: any): Picture {
    return {
      id: row['id'],
      userId: row['userId'],
      path: row['path'],
    } as Picture;
  }

  async prefixInstructorPictures(conn: Connection, pictureIds: string[]): Promise<string[]> {
    if (!pictureIds || pictureIds.length === 0) {
      return [];
    }
    const pictures = await this.findPictureByIds(conn, pictureIds);
    return pictures.map(picture =>
      `${process.env.PIC_URL_PREFIX!}/instr-pics/${picture.path}`
    );
  }

  ///////////
  // Rates //
  ///////////

  async findRates(
    conn: Connection, instrId: string,
  ): Promise<Rates[]> {
    const sql = `select * from instr02rates
      where instr_id = ?
      order by type, start`;
    const params = [instrId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return this.toRates(results);
  }

  private toRates(rows: any[]): Rates[] {
    const map: any = {};
    for (const row of rows) {
      const type: string = row['type'];
      const rate = {
        start: row['start'],
        end: row['end'],
        price: row['price'],
      } as Rate;

      const val = map[type];
      if (val) {
        val.push(rate);
      } else {
        map[type] = [rate];
      }
    }

    const ret: Rates[] = [];
    Object.keys(map).forEach((key: string) => {
      ret.push({
        type: key,
        rates: map[key] as Rate[],
      } as Rates);
    });
    return ret;
  }

  async deleteRates(
    conn: Connection, req: RatesSaveReq
  ): Promise<number> {
    const sql = "delete from instr02rates where instr_id = ?";
    const by = req.by.username;
    const params = [req.instructorId];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] deleted rates, instrId=${req.instructorId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async insertRates(
    conn: Connection, req: RatesSaveReq
  ): Promise<void> {
    const sql = `insert into instr02rates (
        id, instr_id, type, start, end, price, created_by
      ) values (
        ?, ?, ?, ?, ?, ?, ?
      )`;
    const by = req.by.username;
    const rates = req.rates;
    for (const rate of rates) {
      const type = rate.type;
      for (const o of rate.rates) {
        const params = [
          crypto.randomUUID(),
          req.instructorId,
          type,
          o.start,
          o.end,
          o.price,
          by,
        ];
        await conn.execute<ResultSetHeader>(sql, params);
      }
      logger.info(`[${by}] inserted rates, `
        + `type=${type}, instrId=${req.instructorId}, numRates=${rate.rates.length}`);
    }
  }

  ////////////////
  // Work Hours //
  ////////////////

  async findWorkHours(
    conn: Connection, instrId: string,
  ): Promise<WorkHours[]> {
    const sql = `select * from instr03work_hours
      where instr_id = ?
      order by type, start`;
    const params = [instrId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return this.toWorkHours(results);
  }

  private toWorkHours(rows: any[]): WorkHours[] {
    const map: any = {};
    for (const row of rows) {
      const type: string = row['type'];
      const slot = {
        start: row['start'],
        end: row['end'],
      } as WorkHoursSlot;

      const val = map[type];
      if (val) {
        val.push(slot);
      } else {
        map[type] = [slot];
      }
    }

    const ret: WorkHours[] = [];
    Object.keys(map).forEach((key: string) => {
      ret.push({
        type: key,
        slots: map[key] as WorkHoursSlot[],
      } as WorkHours);
    });
    return ret;
  }

  async deleteWorkHours(
    conn: Connection, req: WorkHoursSaveReq
  ): Promise<number> {
    const sql = "delete from instr03work_hours where instr_id = ?";
    const by = req.by.username;
    const params = [req.instructorId];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] deleted work hours, instrId=${req.instructorId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async insertWorkHours(
    conn: Connection, req: WorkHoursSaveReq
  ): Promise<void> {
    const sql = `insert into instr03work_hours (
        id, instr_id, type, start, end, created_by
      ) values (
        ?, ?, ?, ?, ?, ?
      )`;
    const by = req.by.username;
    const slots = req.slots;
    for (const slot of slots) {
      const type = slot.type;
      for (const o of slot.slots) {
        const params = [
          crypto.randomUUID(),
          req.instructorId,
          type,
          o.start,
          o.end,
          by,
        ];
        await conn.execute<ResultSetHeader>(sql, params);
      }
      logger.info(`[${by}] inserted work hours, `
        + `type=${type}, instrId=${req.instructorId}, numRates=${slot.slots.length}`);
    }
  }

  //////////////
  // Mobility //
  //////////////

  async findMobility(
    conn: Connection, instrId: string,
  ): Promise<Mobility[]> {
    const sql = `select * from instr04mobility
      where instr_id = ?
      order by dow`;
    const params = [instrId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => this.toMobility(o));
  }

  private toMobility(row: any): Mobility {
    return {
      dow: row['dow'],
      locationId: row['location_id'],
      distance: row['distance'],
    }
  }

  async deleteMobility(
    conn: Connection, req: MobilitySaveReq
  ): Promise<number> {
    const sql = "delete from instr04mobility where instr_id = ?";
    const by = req.by.username;
    const params = [req.instructorId];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] deleted mobility, instrId=${req.instructorId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async insertMobility(
    conn: Connection, req: MobilitySaveReq
  ): Promise<void> {
    const sql = `insert into instr04mobility (
        id, instr_id, dow,
        location_id, lat, lng, latlng,
        distance, created_by
      ) select
        ?, ?, ?,
        id, lat, lng, latlng,
        ?, ?
        from stu01studios where id = ?`;
    const by = req.by.username;
    const mobility = req.mobility;
    for (const o of mobility) {
      const params = [
        crypto.randomUUID(),
        req.instructorId,
        o.dow,
        o.distance,
        by,
        o.locationId,
      ];
      const [results] = await conn.execute<ResultSetHeader>(sql, params);
      const affectedRows = results.affectedRows;
      if (affectedRows != 1) throw `Expecting exactly 1 row inserted, actual: ${affectedRows}`;
    }
    logger.info(`[${by}] inserted mobility, `
      + `instrId=${req.instructorId}, numRates=${req.mobility.length}`);
  }

  //////////////
  // Blackout //
  //////////////

  async findBlackouts(
    conn: Connection, criteria: BlackoutFindCriteria,
  ): Promise<Blackout[]> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    {
      clauses.push("(instr_id = ?)");
      params.push(criteria.instructorId);
    }
    if (criteria.month) {
      clauses.push(`(
          date_format(start_date, '%Y-%m') = ?
          or
          date_format(end_date, '%Y-%m') = ?
        )`);
      params.push(criteria.month);
      params.push(criteria.month);
    }
    if (criteria.day) {
      clauses.push(`(start_date <= ? AND end_date >= ?)`);
      params.push(criteria.day);
      params.push(criteria.day);
    }
    clauses.push("(deleted is false)");

    // select items
    let selectSql = "select * from instr07blackouts";
    selectSql += ` where ${clauses.join(" and ")}`
    selectSql += ` order by start_date, start_time, end_date, end_time`;
    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    return results.map(result => this.toBlackout(result))
  }

  private toBlackout(row: any): Blackout {
    return {
      id: row['id'],
      name: row['name'],
      startDate: convertMySQLDateToTimezoneString(row['start_date']),
      startTime: row['start_time'],
      endDate: convertMySQLDateToTimezoneString(row['end_date']),
      endTime: row['end_time'],
      fullDay: row['full_day'] === 1,
    }
  }

  async deleteBlackout(
    conn: Connection, req: BlackoutDeleteReq
  ): Promise<number> {
    const sql = `update instr07blackouts
      set deleted = true, deleted_at = now(), deleted_by = ?,
      updated_by = ?
      where id = ? and instr_id = ? and deleted is false`;
    const by = req.by.username;
    const params = [
      by,  // deleted_by
      by,  // updated_by
      req.blackoutId,
      req.instructorId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] deleted blackout, id=${req.blackoutId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async insertBlackout(
    conn: Connection, req: BlackoutSaveReq
  ): Promise<string> {
    const sql = `insert into instr07blackouts (
        id, instr_id, name, start_date, start_time, end_date, end_time, full_day,
        created_by
      ) values (
        ?, ?, ?, ?, ?, ?, ?, ?, ?
      )`;
    const uuid = crypto.randomUUID();
    const by = req.by.username;
    const blackout = req.blackout;
    const params = [
      uuid,
      req.instructorId,
      blackout.name,
      blackout.startDate,
      blackout.startTime ?? null,
      blackout.endDate,
      blackout.endTime ?? null,
      blackout.fullDay ? 1 : 0,
      by
    ];
    const [result] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] inserted blackout, id=${uuid}, instrId=${req.instructorId}`);
    return uuid;
  }

  /////////////////
  // Equipment //
  /////////////////

  async findEquipmentTypes(
    conn: Connection, instrId: string,
  ): Promise<string[]> {
    const sql = `select type_id from instr08equipment
      where instr_id = ?`;
    const params = [instrId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(row => row['type_id']);
  }

  async deleteEquipmentTypes(
    conn: Connection, req: EquipmentTypesSaveReq
  ): Promise<number> {
    const sql = "delete from instr08equipment where instr_id = ?";
    const by = req.by.username;
    const params = [req.instructorId];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] deleted equipment types, instrId=${req.instructorId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async insertEquipmentTypes(
    conn: Connection, req: EquipmentTypesSaveReq
  ): Promise<void> {
    const sql = `insert into instr08equipment (
        instr_id, type_id, created_by
      ) values (
        ?, ?, ?
      )
      ON DUPLICATE KEY UPDATE updated_by = ?, updated_at = now();
    `;
    const by = req.by.username;
    const equipmentTypes = req.equipmentTypes;
    for (const typeId of equipmentTypes) {
      const params = [
        req.instructorId,
        typeId,
        by,
        by,
      ];
      await conn.execute<ResultSetHeader>(sql, params);
    }
    logger.info(`[${by}] inserted equipment types, `
      + `instrId=${req.instructorId}, numTypes=${equipmentTypes.length}`);
  }

  /////////////////
  // Test Script //
  /////////////////
  async getRandomInstructorByCreator(conn: Connection, creatorPattern: string): Promise<Instructor | undefined> {
      const sql = `SELECT * FROM instr01instructors WHERE created_by LIKE ? ORDER BY RAND() LIMIT 1`;
      const params = [creatorPattern];
      const [results] = await conn.query<RowDataPacket[]>(sql, params);

      if (results.length === 0) {
          return undefined; // No matching instructor found
      }
      return InstructorRepo.toInstructor(results[0]);
  }

  ////////////////////
  // soft delete //
  ////////////////////
  async softDelete(conn: Connection, instructorId: string, by: By, timestamp: string): Promise<number> {
    const sql = `update instr01instructors set deleted = true, deleted_at = now(), deleted_by = ?, updated_by = ?, id = CONCAT(id, ?), fbase_uid = CONCAT(fbase_uid, ?) where id = ?`;
    const byWho = by.username;
    logger.debug(`[${byWho}] softDelete() instructor by=${JSON.stringify(instructorId)}`)
    const params = [byWho, byWho, timestamp, timestamp, instructorId];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${byWho}] soft deleted instructor, id=${instructorId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async softDeleteRelatedRecords(conn: Connection, instructorId: string, by: By, timestamp: string): Promise<void> {
    const byWho = by.username;
    logger.debug(`[${byWho}] softDeleteRelatedRecords() instructor by=${JSON.stringify(instructorId)}`)

    // instructor profile
    const [results] = await conn.execute<ResultSetHeader>(`delete from pub02instructors where id = ?`, [instructorId]);
    logger.info(`[${byWho}] hard deleted instructor pub02instructors, id=${instructorId}, numRows=${results.affectedRows}`);
    const [results2] = await conn.execute<ResultSetHeader>(`update instr02rates set instr_id = CONCAT(instr_id, ?) where instr_id = ?`, [timestamp, instructorId]);
    logger.info(`[${byWho}] soft deleted instructor instr02rates, id=${instructorId}, numRows=${results2.affectedRows}`);
    const [results3] = await conn.execute<ResultSetHeader>(`update instr03work_hours set instr_id = CONCAT(instr_id, ?) where instr_id = ?`, [timestamp, instructorId]);
    logger.info(`[${byWho}] soft deleted instructor instr03work_hours, id=${instructorId}, numRows=${results3.affectedRows}`);
    const [results4] = await conn.execute<ResultSetHeader>(`update instr04mobility set instr_id = CONCAT(instr_id, ?) where instr_id = ?`, [timestamp, instructorId]);
    logger.info(`[${byWho}] soft deleted instructor instr04mobility, id=${instructorId}, numRows=${results4.affectedRows}`);
    const [results5] = await conn.execute<ResultSetHeader>(`update instr05pref_studios set user_id = CONCAT(user_id, ?) where user_id = ?`, [timestamp, instructorId]);
    logger.info(`[${byWho}] soft deleted instructor instr05pref_studios, id=${instructorId}, numRows=${results5.affectedRows}`);
    const [results6] = await conn.execute<ResultSetHeader>(`update instr06pictures set user_id = CONCAT(user_id, ?) where user_id = ?`, [timestamp, instructorId]);
    logger.info(`[${byWho}] soft deleted instructor instr06pictures, id=${instructorId}, numRows=${results6.affectedRows}`);
    const [results7] = await conn.execute<ResultSetHeader>(`update instr07blackouts set instr_id = CONCAT(instr_id, ?) where instr_id = ?`, [timestamp, instructorId]);
    logger.info(`[${byWho}] soft deleted instructor instr07blackouts, id=${instructorId}, numRows=${results7.affectedRows}`);
    const [results8] = await conn.execute<ResultSetHeader>(`update instr08equipment set instr_id = CONCAT(instr_id, ?) where instr_id = ?`, [timestamp, instructorId]);
    logger.info(`[${byWho}] soft deleted instructor instr08equipment, id=${instructorId}, numRows=${results8.affectedRows}`);

    // other tables
    const [results9] = await conn.execute<ResultSetHeader>(`update usr03fave_instrs set instr_id = CONCAT(instr_id, ?) where instr_id = ?`, [timestamp, instructorId]);
    logger.info(`[${byWho}] soft deleted instructor usr03fave_instrs, id=${instructorId}, numRows=${results9.affectedRows}`);

    // bookings
    const [results10] = await conn.execute<ResultSetHeader>(`update evt01events set deleted = true, deleted_at = NOW(), deleted_by = ?, updated_by = ?, instr_id = CONCAT(instr_id, ?) where instr_id = ? AND start_at >= CONVERT_TZ(NOW(), @@session.time_zone, timezone)`, [byWho, byWho, timestamp, instructorId]);
    const [results11] = await conn.execute<ResultSetHeader>(`update evt01events set updated_by = ?, instr_id = CONCAT(instr_id, ?) where instr_id = ? AND start_at < CONVERT_TZ(NOW(), @@session.time_zone, timezone)`, [byWho, timestamp, instructorId]);

    logger.info(`[${byWho}] soft deleted instructor evt01events, id=${instructorId}, numRows=${results10.affectedRows + results11.affectedRows}`);

    const [results12] = await conn.execute<ResultSetHeader>(`update evt02sessions set instr_id = CONCAT(instr_id, ?) where instr_id = ?`, [timestamp, instructorId]);
    logger.info(`[${byWho}] soft deleted instructor evt02sessions, id=${instructorId}, numRows=${results12.affectedRows}`);

    return;
  }

}
