import { PrefStudioSaveReq } from "@instructor-api/models/pref";
import { Studio } from "@studio-cms/models/studio";
import { StudioRepo } from "@studio-cms/repo/studio-repo";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";

export class PrefRepo {

  ///////////////////////
  // Preferred Studios //
  ///////////////////////

  async addPrefStudio(
    conn: Connection, req: PrefStudioSaveReq,
  ): Promise<number> {
    const sql =
`insert ignore into instr05pref_studios (
  user_id, studio_id, created_by
) value (?, ?, ?)`;
    const by = req.by.username;
    logger.debug(`[${by}] addPrefStudio() req=${JSON.stringify(req)}`)
    const params = [
      req.userId!,
      req.studioId!,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] addPrefStudio()`
      + ` userId=${req.userId}, studioId=${req.studioId} numRows=${affectedRows}`);
    return affectedRows;
  }

  async removePrefStudio(
    conn: Connection, req: PrefStudioSaveReq,
  ): Promise<number> {
    const sql = "delete from instr05pref_studios where user_id = ? and studio_id = ?";
    const by = req.by.username;
    logger.debug(`[${by}] removePrefStudio() req=${JSON.stringify(req)}`)
    const params = [
      req.userId!,
      req.studioId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] removePrefStudio()`
      + ` userId=${req.userId}, studioId=${req.studioId} numRows=${affectedRows}`);
    return affectedRows;
  }

  async getPrefStudios(
    conn: Connection, userId: string,
  ): Promise<Studio[]> {
    const sql =
`select * from vw_instr05pref_studios
  where user_id = ? and deleted is false`;
    const params = [userId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => StudioRepo.toStudio(o));
  }

  async hasPrefStudio(
    conn: Connection, userId: string, studioId: string,
  ): Promise<boolean> {
    const sql =
`select count(studio_id) as \`count\` from instr05pref_studios
  where user_id = ? and studio_id = ?`;
    const params = [userId, studioId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results[0]['count'] !== 0;
  }
}
