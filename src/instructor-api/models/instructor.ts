import { By } from "@shared/models";
import { Studio } from "@studio-cms/models/studio";
import { ContactNo} from "@member-api/models/member";

export interface Instructor {
  id: string;
  firebaseUid: string;
  name: string;
  descr?: string;
  facebook?: string;
  instagram?: string;
  certifications?: string;
  specialisations?: string;
  pictures: string[];
  registeredAt: string;
  onboarded?: boolean;
  pendingApproval?: boolean,
  isCompleted?: boolean,
  createdBy?: string,
  deleted?: boolean;
  //
  prefStudios?: Studio[],
  equipmentTypes?: string[],
  equipments?: InstructorEquipment[],
  avgRating?: number,
  totalRatingCount?: number;
  fav?: boolean,
}

export interface InstructorSaveReq {
  instructor: Instructor,
  by: By,
}

export interface InstructorSignUpReq {
  id: string;
  firebaseUid: string;
  firebaseEmail: string;
  fullName: string;
  gender: string;
  mobileNo?: ContactNo;
  by: By,
}

export interface CompleteSaveReq {
  instructorId: string,
  pictures: string[],
  by: By,
}


///////////
// Rates //
///////////

export interface Rates {
  type: string,
  rates: Rate[],
}

export interface Rate {
  start: string,
  end: string,
  price: string,
}

export interface RatesSaveReq {
  instructorId: string,
  rates: Rates[],
  by: By,
}

////////////////
// Work Hours //
////////////////

export interface WorkHours {
  type: string,
  slots: WorkHoursSlot[],
}

export interface WorkHoursSlot {
  start: string,
  end: string,
}

export interface WorkHoursSaveReq {
  instructorId: string,
  slots: WorkHours[],
  by: By,
}

//////////////
// Mobility //
//////////////

export interface Mobility {
  dow: number,
  locationId: string,
  locationName?: string,
  distance: string,
}

export interface MobilitySaveReq {
  instructorId: string,
  mobility: Mobility[],
  by: By,
}

//////////////
// Blackout //
//////////////

export interface BlackoutFindCriteria {
  instructorId: string,
  month: string,
  day: string,
}

export interface Blackout {
  id?: string,
  name: string,
  startDate: string,
  startTime: string,
  endDate: string,
  endTime: string,
  fullDay?: boolean,
}

export interface BlackoutSaveReq {
  instructorId: string,
  blackout: Blackout,
  by: By,
}

export interface BlackoutDeleteReq {
  instructorId: string,
  blackoutId: string,
  by: By,
}

///////////////
// Equipment //
///////////////

export interface EquipmentTypesSaveReq {
  instructorId: string,
  equipmentTypes: string[],
  by: By,
}

export interface InstructorEquipment {
  instructorId: string,
  typeId: string,
  typeName: string,
}
