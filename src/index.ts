import 'dotenv/config';
import express, { Express } from "express";
import { initCORS } from "./init-cors";
import { initOpenAPI } from "./init-openapi";
import { initSequelize, closeSequelize } from "./init-sequelize";
import logger from "@utils/logger";
import path from 'path';
import './utils/event-listeners';
global.appRoot = path.resolve(__dirname, '..');

// Error handlers
process.on('unhandledRejection', (reason: any, promise) => {
  logger.error('=== Unhandled Rejection ===');
  logger.error('Promise:', promise);
  logger.error('Reason:', reason);
});

process.on('uncaughtException', (error: Error) => {
  logger.error('=== Uncaught Exception ===');
  logger.error(JSON.stringify(error));
});

const app: Express = express();
app.disable('x-powered-by');
const port = process.env.PORT || 3000;

const startServer = async () => {
  try {
    initCORS(app);
    initOpenAPI(app);

    await initSequelize();

    app.listen(port, () => {
      logger.info(`Server started on port=${port}`);
    });

    process.on('SIGTERM', async () => {
      logger.info('SIGTERM received, shutting down gracefully');
      await closeSequelize();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.info('SIGINT received, shutting down gracefully');
      await closeSequelize();
      process.exit(0);
    });

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
