import { Sequelize } from 'sequelize';

import env from '@config/env.config';
import defineAssociations from '@database/models/_associations';
import { loadModels } from '@database/models';
import logger from '@utils/logger';

const getDatabaseConfig = () => {
  return {
    dialect: 'mysql' as const,
    host: env.get('DATABASE_HOST'),
    port: env.get('DATABASE_PORT'),
    username: env.get('DATABASE_USERNAME'),
    password: env.get('DATABASE_PASSWORD'),
    database: env.get('DATABASE_NAME'),
    timezone: env.get('DATABASE_TIMEZONE'),
    dialectOptions: {
      timezone: env.get('DATABASE_TIMEZONE'),
      charset: 'utf8mb4',
      ...(env.isProduction() && {
        ssl: {
          require: true,
          rejectUnauthorized: false
        }
      })
    },
    logging: (sql: string) => {
      if (env.isDevelopment()) {
        logger.debug(`SQL: ${sql}`);
      }
    },
    pool: {
      max: env.get('DATABASE_POOL_CONNECTION_LIMIT'),
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    define: {
      underscored: true,
      freezeTableName: true
    }
  };
};

const sequelize = new Sequelize(getDatabaseConfig());

export const initSequelize = async (): Promise<void> => {
  try {
    logger.info('Initializing Sequelize...');

    await sequelize.authenticate();
    logger.info('Database connection established successfully');
    try {
      loadModels(sequelize);
      logger.info('Models loaded successfully');

      defineAssociations(sequelize);
      logger.info('Associations defined successfully');

    } catch (error) {
      logger.warn('Failed to load models:', error);
    }

    if (process.env.NODE_ENV === 'development' && process.env.DATABASE_SYNC === 'true') {
      logger.warn('Syncing database schema (development mode)');
      await sequelize.sync({ alter: true });
      logger.info('Database schema synced');
    }

    logger.info('Sequelize initialization completed successfully');
  } catch (error) {
    logger.error('Failed to initialize Sequelize:', error);
    throw error;
  }
};

// Graceful shutdown
export const closeSequelize = async (): Promise<void> => {
  try {
    await sequelize.close();
    logger.info('Database connection closed successfully');
  } catch (error) {
    logger.error('Error closing database connection:', error);
  }
};

export default sequelize;
