import { Exercise, ExerciseCategory, ExerciseModification } from "@booking-api/models/exercise";
import { ExerciseRepo } from "@booking-api/repo/exercise-repo";
import pool from "../../init-pool";

export class ExerciseService {

  private exerciseRepo = new ExerciseRepo();

  async findCategories(): Promise<ExerciseCategory[]> {
    return this.exerciseRepo.findCategories(pool);
  }

  async findExercisesByCategoryId(
    categoryId: string,
  ): Promise<Exercise[]> {
    return this.exerciseRepo.findExercisesByCategoryId(pool, categoryId);
  }

  async findModificationsByExerciseId(
    exerciseId: string,
  ): Promise<ExerciseModification[]> {
    return this.exerciseRepo.findModificationsByExerciseId(pool, exerciseId);
  }
}
