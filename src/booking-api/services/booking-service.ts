import {
  BookingCancel,
  BookingCancelFindCriteria,
  BookingCancelReq,
  BookingCancelResult,
  BookingDoorPinSaveReq,
  BookingFeedbackSaveReq,
  BookingFindCriteria,
  BookingNoFeedbackSaveReq,
  ScheduleFindCriteria,
  SessionFindCriteria
} from "@booking-api/models/booking";
import { InitSessionReq } from "../models/session";
import { Event, EventCancelReq, EventSaveReq } from "@studio-cms/models/event";
import { Transaction, TransactionSaveReq, TransactionStatus, TransactionType } from "@booking-api/models/credit";
import { BookingFees } from "@booking-api/models/search";
import { Page } from "@shared/services";
import { EventRepo } from "@studio-cms/repo/event-repo";
import { CreditRepo } from "../repo/credit-repo";
import { BookingRepo } from "../repo/booking-repo";
import { SessionRepo } from "../repo/session-repo";
import { CreditRepo as MemCreditRepo } from "@member-api/repo/credit-repo";
import logger from "@utils/logger";
import pool from "../../init-pool";
import { PinLogRepo } from '../repo/pin-log-repo';
import { PinIgloohomeLog } from '../models/pin-log';
import { DateTime } from 'luxon';
import crypto from "crypto";
import { PromotionService } from "./promotion-service";
import { PromotionType } from "@backoffice-cms/models/promotion";

export class BookingService {

  private eventRepo = new EventRepo();
  private bookingRepo = new BookingRepo();
  private sessionRepo = new SessionRepo();
  private creditRepo = new CreditRepo();
  private memberCreditRepo = new MemCreditRepo();
  private pinLogRepo = new PinLogRepo();
  private promotionService = new PromotionService();

  async makeBooking(
    req: EventSaveReq,
    fees: BookingFees,
  ): Promise<string> {
    return this.eventRepo.create(pool, req, fees);
  }

  async makeBookingByCredit(
    req: EventSaveReq,
    fees: BookingFees,
  ): Promise<string> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // 0. Re-validate promo code, if any
      const validatedPromo = (req.event.promoCode)
        ? await this.promotionService.findAndValidatePromoCode(req.event.promoCode, req.event.memberId, PromotionType.BOOKING)
        : undefined;

      // 1. Create Booking Event first
      const bookingId = await this.eventRepo.create(conn, req, fees);

      // 2. Create Transaction Record Type Booking with Event ID
      const bookingFee = +fees.totalCredits;
      const transaction = {
        memberId: req.event.memberId,
        type: TransactionType.Booking,
        amount: bookingFee,
        status: TransactionStatus.Success,
        remark: validatedPromo ? `Promo Code: ${req.event.promoCode} (${validatedPromo.promotion.name})` : null,
        eventId: bookingId
      } as Transaction;

      const trxId = await this.creditRepo.createTrx(conn, {
          transaction: transaction,
          by: req.by,
      } as TransactionSaveReq);

      // 3. Update Member Credits (Deduct Booking's Credits)
      await this.memberCreditRepo.updateMemberCredit(conn, req.event.memberId, -Math.abs(bookingFee), req.by.username);

      // 4. Record promo code usage
      if (validatedPromo) {
        await this.promotionService.recordUsage(conn, validatedPromo.promoCode.id, req.event.memberId, trxId, bookingId);
      }

      // 5. Initialize Session
      await this.sessionRepo.initSessionByBookingId(conn, {
        bookingId: bookingId,
        by: req.by.username,
      } as InitSessionReq);

      await conn.commit();
      return bookingId;

    } catch (e) {
      logger.error(`makeBookingByCredit error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async bookings(
    criteria: BookingFindCriteria,
    page?: Page,
  ): Promise<Event[]> {
    return this.bookingRepo.findBookings(pool, criteria, page);
  }

  async bookingCounts(
    criteria: BookingFindCriteria,
  ): Promise<{ bookings: number; sessions: number }> {
    return this.bookingRepo.countBookingsAndSessions(pool, criteria);
  }

  async bookingById(id: string): Promise<Event|undefined> {
    return this.bookingRepo.findBookingById(pool, id);
  }

  async findByPaymentReqId(paymentReqId: string): Promise<Event|undefined> {
    return this.bookingRepo.findByPaymentReqId(pool, paymentReqId);
  }

  async sessions(
    criteria: SessionFindCriteria,
    page?: Page,
  ): Promise<Event[]> {
    return this.bookingRepo.findSessions(pool, criteria, page);
  }

  /* unused
  async saveBookingState(req: BookingNotesSaveReq): Promise<number> {
    return this.bookingRepo.saveBookingState(pool, req);
  }

  async saveBookingTarget(req: BookingNotesSaveReq): Promise<number> {
    return this.bookingRepo.saveBookingTarget(pool, req);
  }
  */

  async saveFeedback(req: BookingFeedbackSaveReq): Promise<number> {
    return this.bookingRepo.saveFeedback(pool, req);
  }

  async noFeedback(req: BookingNoFeedbackSaveReq): Promise<number> {
    return this.bookingRepo.noFeedback(pool, req);
  }

  //////////////
  // Schedule //
  //////////////

  async schedule(criteria: ScheduleFindCriteria): Promise<Event[]> {
    return this.bookingRepo.schedule(pool, criteria);
  }

  ////////////
  // Cancel //
  ////////////

  async findBookingCancel(
    criteria: BookingCancelFindCriteria,
  ): Promise<BookingCancel|undefined> {
    return this.bookingRepo.findCancelReq(pool, criteria);
  }

  async findBookingCancelReqsByBookingIds(bookingIds: string[], approved: boolean|undefined): Promise<BookingCancel[]> {
    return this.bookingRepo.findCancelReqsByBookingIds(pool, bookingIds, approved);
  }

  async cancelReq(req: BookingCancelReq): Promise<BookingCancelResult> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // check if email exists
      const booking = await this.bookingRepo.findBookingById(conn, req.eventId!);
      if (!booking) {
        throw "Booking not found!"; // fail fast
      }
      if (booking.memberId !== req.userId!) {
        throw "Booking does not belong to user!"; // fail fast
      }
      if (booking.bookedByAdmin) {
        throw "Booking is booked by admin!"; // fail fast
      }

      // New check: Ensure cancellation is only allowed before the booking starts
      const timezone = booking.timezone || 'Asia/Singapore';
      const now = DateTime.now().setZone(timezone);

      const startAt = DateTime.fromFormat(`${booking.startDate} ${booking.startTime}`, 'yyyy-MM-dd HH:mm:ss', { zone: timezone });
      logger.debug(`eventId=${booking.id}, userId=${booking.memberId}, startAtStr=${startAt}`);
      if (now > startAt) {
        throw "Cannot cancel booking after it has started!";
      }

      const freeNumHours = +process.env.BOOKING_FREE_HOURS!;
      const freeNumSecs = freeNumHours * 60 * 60;
      logger.debug(`freeNumHours=${freeNumHours}, freeNumSecs=${freeNumSecs}`);

      const numSecs = Math.floor(startAt.diff(now, 'seconds').seconds);
      logger.debug(`now=${now}, numSecs=${numSecs}`);

      const freeCancel = numSecs >= freeNumSecs;
      logger.debug(`freeCancel=${freeCancel}`);
      req.free = freeCancel;
      req.approved = true; // (!) important: member request no need backoffice approval

      const token = await this.bookingRepo.createCancelReq(conn, req);
      await conn.commit();
      return {
        token: token,
        free: freeCancel,
      } as BookingCancelResult;
    } catch (e) {
      logger.error(`cancelReq error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async cancel(req: EventCancelReq): Promise<number> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      const ret = await this.eventRepo.cancel(conn, req);

      // check have updated row only proceed to handle credit deduction
      if(ret > 0) {
        const transaction = await this.creditRepo.getTrxByEventId(conn, req.eventId);
        if(transaction) {
          const event = await this.eventRepo.findById(conn, req.eventId);
          let refundCredits = +event.bookingFees.totalCredits;

          // Check if not free to cancel
          if(!req.cancelFree) {
            const penatlyFeePercent = await this.eventRepo.findCancellationFee(conn);
            penatlyFeePercent > 0 && (refundCredits -= (refundCredits * penatlyFeePercent/100));
          }

          // Proceed if refundCredits is not empty
          if(refundCredits > 0) {
            // 1. Update cancelled_with_refund flag
            await this.eventRepo.updateCancelledWithRefund(conn, req.eventId, true, req.by.username);

            // 2. Create Refund Transaction Record
            const transaction = {
              memberId: req.memberId,
              type: TransactionType.Refund,
              amount: refundCredits,
              status: TransactionStatus.Success,
              eventId: req.eventId
            } as Transaction;

            await this.creditRepo.createTrx(conn, {
                transaction: transaction,
                by: req.by,
            } as TransactionSaveReq);

            // 3 . Update Member Credits (Refund Back Booking's Credits)
            await this.memberCreditRepo.updateMemberCredit(conn, req.memberId, Math.abs(refundCredits), req.by.username);
          }
        }
      }

      await conn.commit();
      return ret;

    } catch (e) {
      logger.error(`makeBookingByCredit error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  //////////////////
  // Release Hold //
  //////////////////

  async releaseHold(): Promise<number> {
    return this.bookingRepo.releaseHold(pool);
  }

  ///////////////
  // Door Pin  //
  ///////////////
  async saveDoorPin(req: BookingDoorPinSaveReq): Promise<number> {
    return this.bookingRepo.saveDoorPin(pool, req);
  }

  async logPinJob(log: PinIgloohomeLog) {
    await this.pinLogRepo.insertPinLog(pool, log);
  }
}
