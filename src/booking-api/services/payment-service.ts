import logger from "@utils/logger";
import pool from "../../init-pool";
import { BookingStatus, BookingStatusSaveReq } from "../models/booking";
import { PaymentNotify } from "../models/payment";
import { InitSessionReq } from "../models/session";
import { BookingRepo } from "../repo/booking-repo";
import { PaymentRepo } from "../repo/payment-repo";
import { SessionRepo } from "../repo/session-repo";
import { CreditRepo } from "@booking-api/repo/credit-repo";
import { CreditRepo as MemCreditRepo } from "@member-api/repo/credit-repo";
import { TransactionStatus, TransactionStatusSaveReq } from "@booking-api/models/credit";

export class PaymentService {

  private bookingRepo = new BookingRepo();
  private sessionRepo = new SessionRepo();
  private paymentRepo = new PaymentRepo();
  private creditRepo = new CreditRepo();
  private memberCreditRepo = new MemCreditRepo();

  async insertNotify(req: PaymentNotify): Promise<string> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();
      const ret = await this.paymentRepo.insertNotify(conn, req);
      await this.bookingRepo.updateBookingStatus(conn, {
        paymentReqId: req.paymentReqId,
        bookingStatus: BookingStatus.Confirmed,
        by: req.by,
      } as BookingStatusSaveReq);
      await this.sessionRepo.initSession(conn, {
        paymentReqId: req.paymentReqId,
        by: req.by,
      } as InitSessionReq);
      await conn.commit();
      logger.info(`insertNotify() success, id=${ret}`);
      return ret;
    } catch (e) {
      logger.error(`insertNotify() error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async insertNotifyTopup(req: PaymentNotify): Promise<string> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // 1. Insert Notify Results
      const ret = await this.paymentRepo.insertNotify(conn, req);

      // 2. Validate Trx Record
      const trx = await this.creditRepo.getTrxByPaymentReqId(conn, req.paymentReqId);
      if(!trx) {
        throw "Transaction not found!";
      }

      // 3. Update Trx Status to Confirmed Payment
      await this.creditRepo.updateTransactionStatus(conn, {
        paymentReqId: req.paymentReqId,
        paymentId: req.paymentId,
        status: TransactionStatus.ConfirmedPayment,
        by: req.by,
      } as TransactionStatusSaveReq);

      // 4. Update Member Credits and Expired Date with Purchased Package's Credits
      await this.memberCreditRepo.updateMemberCredit(conn, trx.memberId, trx.amount, req.by, +(process.env.CREDITS_TOPUP_EXTENDS_MONTHS || 6));

      await conn.commit();
      logger.info(`insertNotifyTopup() success, id=${ret}`);
      return ret;
    } catch (e) {
      logger.error(`insertNotifyTopup() error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }
}
