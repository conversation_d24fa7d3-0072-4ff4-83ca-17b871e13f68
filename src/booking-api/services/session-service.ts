import pool from "../../init-pool";
import { Session, SessionNotesSaveReq, SessionRecordCompleteReq, SessionRecordSaveReq } from "../models/session";
import { SessionRepo } from "@booking-api/repo/session-repo";
import { BookingRepo } from "@booking-api/repo/booking-repo";
import { BookingCancelReq, BookingCancelResult} from '@booking-api/models/booking';
import logger from "@utils/logger";
import { DateTime } from "luxon";

export class SessionService {

  private sessionRepo = new SessionRepo();
  private bookingRepo = new BookingRepo();

  async findByBookingId(bookingId: string): Promise<Session|undefined> {
    return this.sessionRepo.findByBookingId(pool, bookingId);
  }

  async findByBookingIds(bookingIds: string[]): Promise<Session[]|undefined> {
    return this.sessionRepo.findByBookingIds(pool, bookingIds);
  }

  async saveSessionState(req: SessionNotesSaveReq): Promise<number> {
    return this.sessionRepo.saveSessionState(pool, req);
  }

  async saveSessionTarget(req: SessionNotesSaveReq): Promise<number> {
    return this.sessionRepo.saveSessionTarget(pool, req);
  }

  async saveSessionRecord(req: SessionRecordSaveReq): Promise<number> {
    return this.sessionRepo.saveSessionRecord(pool, req);
  }

  async completeSessionRecord(req: SessionRecordCompleteReq): Promise<number> {
    return this.sessionRepo.completeSessionRecord(pool, req);
  }

  async cancelReq(req: BookingCancelReq): Promise<BookingCancelResult> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // check if email exists
      const booking = await this.bookingRepo.findBookingById(conn, req.eventId!);
      if (!booking) {
        throw "Booking not found!"; // fail fast
      }
      if (booking.instructorId !== req.userId!) {
        throw "Booking does not belong to user!"; // fail fast
      }

      const cancelReq = await this.bookingRepo.findCancelReq(conn, {
        eventId: req.eventId,
      });
      if( cancelReq ) {
        throw "Cancel booking request already exists!";
      }

      // New check: Ensure cancellation is only allowed before the booking starts
      const timezone = booking.timezone || 'Asia/Singapore';
      const now = DateTime.now().setZone(timezone);

      const startAt = DateTime.fromFormat(`${booking.startDate} ${booking.startTime}`, 'yyyy-MM-dd HH:mm:ss', { zone: timezone });
      logger.debug(`eventId=${booking.id}, userId=${booking.memberId}, startAtStr=${startAt}`);
      if (now > startAt) {
        throw "Cannot cancel booking after it has started!";
      }

      req.free = true;
      req.approved = false; // (!) important: pending from backoffice approval
      const token = await this.bookingRepo.createCancelReq(conn, req);
      await conn.commit();
      return {
        token: token,
        free: true,
      } as BookingCancelResult;
    } catch (e) {
      logger.error(`cancelReq error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }
}
