import { SearchAt, LatLng, SearchCriteria } from "@booking-api/models/search";
import { BookingService } from "./booking-service";
import { EquipmentService } from "@studio-cms/services/equipment-service";
import { InstructorService } from "@instructor-api/services/instructor-service";
import { MemberService } from "@member-api/services/member-service";
import { StudioService } from "@studio-cms/services/studio-service";
import { SearchService } from "./search-service";
import { Event, EventSaveReq } from "@studio-cms/models/event";
import { BookingFees } from "@booking-api/models/search";
import { BookingType, BookingStatus, BookingDoorPinSaveReq } from "@booking-api/models/booking";
import { randomDigits, randomUppers } from "@utils/random";
import eventEmitter from "@utils/event-emitter";
import { createPinJob } from "@utils/igloohome";
import { PinIgloohomeLog } from '../models/pin-log';
import logger from "@utils/logger";
import { DateTime } from "luxon";
import { By } from "@shared/models";

export class BookingFlowService {
  private bookingService = new BookingService();
  private equipService = new EquipmentService();
  private instrService = new InstructorService();
  private memberService = new MemberService();
  private studioService = new StudioService();
  private searchService = new SearchService();

  async validateSlotAndCredits({
    latLng, distance, at, studioId, equipId, instrId, memberId, promoCode
  }: {
    latLng: LatLng,
    distance: number,
    at: SearchAt,
    studioId: string,
    equipId: string,
    instrId?: string,
    memberId: string,
    promoCode?: string,
  }): Promise<{ valid: boolean, reason?: string, equip?: any, instr?: any, memberCredit?: any, bookingFees?: BookingFees }> {
    // Equipment
    const equip = await this.equipService.findById(equipId);
    if (!equip) return { valid: false, reason: "equipment not found" };
    // Instructor (optional)
    let instr = undefined;
    if (instrId) {
      instr = await this.instrService.findById(instrId);
      if (!instr) return { valid: false, reason: "instructor not found" };
    }

    const member = await this.memberService.findByFirebaseUid(memberId);
    if (!member) return { valid: false, reason: "member not found" };

    const timezone = member.timezone ?? 'Asia/Singapore';

    // Slot availability
    const criteria = {
      latLng,
      distance,
      at,
      studioId,
      instrId,
      equipId,
      equipTypes: [],
      instrLessOnly: false,
      faveStudioOnly: false,
      faveInstrOnly: false,
      byId: memberId,
      includeEquipment: true,
      timezone: timezone,
    } as SearchCriteria;
    const results = await this.searchService.search(criteria);
    if (
      results.length != 1 ||
      results[0].groups?.length < 1 ||
      results[0].groups[0].slots?.length != 1 ||
      results[0].equipment?.length != 1
    ) {
      return { valid: false, reason: "slot no longer available" };
    }
    // Member credit
    const memberCredit = await this.memberService.findMemberCreditByMemberId(memberId);
    if (!memberCredit) return { valid: false, reason: "member's credit not found" };
    // Credit expiry
    const now = DateTime.now().setZone(timezone);
    if (memberCredit.creditExpiredDate && DateTime.fromFormat(memberCredit.creditExpiredDate, "yyyy-MM-dd", { zone: 'UTC' }).setZone(timezone) < now) {
      return { valid: false, reason: "credits have expired" };
    }
    // Booking fees
    const bookingFees = await this.searchService.getBookingFees(at, equipId, instrId, promoCode, memberId);
    if (memberCredit.currentCredits < +bookingFees.totalCredits) {
      return { valid: false, reason: `insufficient credits: required=${bookingFees.totalCredits}, available=${memberCredit.currentCredits}` };
    }
    return { valid: true, equip, instr, memberCredit, bookingFees };
  }

  generateBookingRef() {
    return `${randomUppers(3)}-${randomDigits(3)}`;
  }

  async createBooking({
    equip, instr, at, studioId, equipId, instrId, memberId, by, bookedByAdmin, bookingFees, promoCode
  }: {
    equip: any,
    instr?: any,
    at: SearchAt,
    studioId: string,
    equipId: string,
    instrId?: string,
    memberId: string,
    by: By,
    bookedByAdmin: boolean,
    bookingFees: BookingFees,
    promoCode?: string,
  }): Promise<{ bookingId: string, event: Event }> {
    const member = await this.memberService.findByFirebaseUid(memberId);
    if (!member) throw new Error("member not found");

    const timezone = member.timezone ?? 'Asia/Singapore';

    const now = DateTime.now().setZone(timezone);
    const expireAt = now.plus({ minutes: 15 });
    const bookingRef = this.generateBookingRef();
    const event = {
      studioId: equip.studioId,
      type: BookingType[BookingType.Booking],
      bookingRefNo: bookingRef,
      bookingStatus: BookingStatus[BookingStatus.Confirmed],
      startDate: at.date,
      startTime: at.timeStart,
      endDate: at.date,
      endTime: at.timeEnd,
      equipmentId: equipId,
      instructorId: instrId,
      memberId: memberId,
      holdAt: now,
      holdEnds: expireAt,
      bookedByAdmin,
      countryCode: member.countryCode,
      timezone: timezone,
      promoCode: promoCode,
    } as Event;
    const bookingId = await this.bookingService.makeBookingByCredit({ event, by } as EventSaveReq, bookingFees);
    return { bookingId, event };
  }

  async handleDoorPinIfNeeded({ booking, event, by }: { booking: any, event: Event, by: string }) {
    try {
      // Fetch studio config for door pin integration
      const configs = await this.studioService.getStudioConfig(booking.studioId);
      const deviceId = configs.find(c => c.key === 'igloo_device_id')?.value;
      const bridgeId = configs.find(c => c.key === 'igloo_bridge_id')?.value;
      if (deviceId && bridgeId) {
        // Generate PIN using Igloohome API
        const pinJobResult = await createPinJob({
          date: event.startDate,
          startTime: event.startTime,
          endTime: event.endTime,
          timezone: 'Asia/Singapore', // TODO: use dynamic timezone in future
        }, { deviceId, bridgeId });
        const pinLog: PinIgloohomeLog = {
          bookingId: booking.id,
          memberId: booking.memberId,
          jobId: pinJobResult.jobId,
          pin: pinJobResult.pin,
          accessName: pinJobResult.accessName,
          startIso: pinJobResult.startIso,
          endIso: pinJobResult.endIso,
          apiResponse: JSON.stringify(pinJobResult),
          error: pinJobResult.error,
          createdBy: by,
        };
        await this.bookingService.logPinJob(pinLog);
        if (pinJobResult.error || !pinJobResult.pin) {
          logger.error(`[${by}] Failed to create door PIN: ${pinJobResult.error}`);
        } else {
          await this.bookingService.saveDoorPin({
            eventId: booking.id,
            memberId: booking.memberId,
            doorPin: pinJobResult.pin,
            by: { username: by },
          } as BookingDoorPinSaveReq);
        }
      }
    } catch (error) {
      logger.error(`[${by}] Failed to create door PIN: ${error}`);
    }
  }

  emitSessionConfirmed({ by, booking }) {
    eventEmitter.emit('session-confirmed', {
      by,
      id: booking.id,
      data: booking,
    });
  }
} 