import logger from "@utils/logger";
import pool from "../../init-pool";
import { MemberRepo } from "@member-api/repo/member-repo";
import { CreditRepo } from "../repo/credit-repo";
import { PaymentRepo } from "../repo/payment-repo";
import { BookingRepo } from "../repo/booking-repo";
import { InstructorRepo } from "@instructor-api/repo/instructor-repo";
import { Package, Transaction, TransactionSaveReq, TransactionType, TransactionSearchCriteria, Payment, CreditExtendExpiry, TransactionStatus } from "@booking-api/models/credit";
import { CreditRepo as MemCreditRepo } from "@member-api/repo/credit-repo";
import { Page, PageOfData } from "@shared/services";
import { DateTime } from "luxon";

export class CreditService {
    private creditRepo = new CreditRepo();
    private paymentRepo = new PaymentRepo();
    private bookingRepo = new BookingRepo();
    private memberRepo = new MemberRepo();
    private memberCreditRepo = new MemCreditRepo();
    private instructorRepo = new InstructorRepo();

    async getPackages(currency: string, validDate: boolean = false): Promise<Package[]> {
        return this.creditRepo.getPackages(pool, currency, validDate);
    }

    async getPackagesByMemberIdEligible(memberId: string, currency: string): Promise<Package[]> {
        const packages = await this.getPackages(currency, true);
        if (!memberId) {
            return packages;
        }

        if(packages.length > 0) {
            // Get all package IDs
            const packageIds = packages.map(pkg => pkg.id);

            // Check first-time only restriction - get packages that should be excluded
            const hasPreviousTopups = await this.creditRepo.hasMemberPreviousTopups(pool, memberId);
            const firstTimeOnlyPackages = hasPreviousTopups ?
                packages.filter(pkg => pkg.firstTimeOnly).map(pkg => pkg.id) : [];

            // Check instructor-only restriction
            const isInstructor = await this.instructorRepo.findByFirebaseUid(pool, memberId) !== undefined;
            const instructorOnlyPackages = !isInstructor ?
                packages.filter(pkg => pkg.instructorOnly).map(pkg => pkg.id) : [];

            // Get purchase counts for all packages in one query
            const purchaseCounts = await this.creditRepo.getPurchaseCountsForPackages(pool, memberId, packageIds);
            // Filter packages based on all criteria
            const filteredPackages = packages.filter(pkg => {
                // Skip first-time only packages if member has previous topups
                if (firstTimeOnlyPackages.includes(pkg.id)) {
                    return false;
                }

                // Skip instructor-only packages if member is not an instructor
                if (instructorOnlyPackages.includes(pkg.id)) {
                    return false;
                }

                // Check purchase limit
                if (pkg.purchaseLimit) {
                    const purchaseCount = purchaseCounts.get(pkg.id) || 0;
                    if (purchaseCount >= pkg.purchaseLimit) {
                        return false;
                    }
                }

                return true;
            });

            return filteredPackages;
        }

        return packages;
    }

    async getPackageById(id: string): Promise<Package|undefined> {
        return this.creditRepo.getPackageById(pool, id);
    }

    async createTrx(req: TransactionSaveReq): Promise<string> {
        return this.creditRepo.createTrx(pool, req);
    }

    async getTrxByPaymentReqId(paymentReqId: string): Promise<Transaction|undefined> {
        const transaction = await this.creditRepo.getTrxByPaymentReqId(pool, paymentReqId);
        if (!transaction) return undefined;

        // Eager load package if exists
        if (transaction.packageId) {
            const packageData = await this.getPackageById(transaction.packageId);
            transaction.package = packageData;
        }

        // Eager load payment notify if exists
        if (transaction.paymentReqId) {
            const notifies = await this.paymentRepo.getNotifiesByPaymentReqIds(pool, [transaction.paymentReqId]);
            if (notifies.length > 0) {
                try {
                    const payload = JSON.parse(notifies[0].payload);
                    transaction.payment = {
                        amount: +payload.amount,
                        currency: payload.currency
                    } as Payment;
                } catch (e) {
                    logger.error(`Failed to parse payment notify payload: ${e}`);
                }
            }
        }

        // Eager load booking if exists
        if (transaction.eventId) {
            const booking = await this.bookingRepo.findBookingById(pool, transaction.eventId);
            transaction.booking = booking;
        }

        return transaction;
    }

    async findTransactions(
        criteria: TransactionSearchCriteria,
        page: Page
    ): Promise<PageOfData<Transaction>> {
        const result = await this.creditRepo.findTransactions(pool, criteria, page);
        const transactions = result.items;

        // Eager load packages
        const packageIds = transactions
            .filter(t => t.packageId)
            .map(t => t.packageId!);

        if (packageIds.length > 0) {
            const packages = await this.getPackagesByIds(packageIds);
            const packageMap = new Map(packages.map(p => [p.id, p]));

            transactions.forEach(transaction => {
                if (transaction.packageId) {
                    transaction.package = packageMap.get(transaction.packageId);
                }
            });
        }

        // Eager load payment notifies
        const paymentReqIds = transactions
            .filter(t => t.paymentReqId)
            .map(t => t.paymentReqId!);

        if (paymentReqIds.length > 0) {
            const notifies = await this.paymentRepo.getNotifiesByPaymentReqIds(pool, paymentReqIds);
            const notifyMap = new Map(notifies.map(n => [n.paymentReqId, n]));

            transactions.forEach(transaction => {
                if (transaction.paymentReqId) {
                    const notify = notifyMap.get(transaction.paymentReqId);
                    if (notify) {
                        try {
                            const payload = JSON.parse(notify.payload);
                            transaction.payment = {
                                amount: +payload.amount,
                                currency: payload.currency
                            } as Payment;
                        } catch (e) {
                            logger.error(`Failed to parse payment notify payload: ${e}`);
                        }
                    }
                }
            });
        }

        // Eager load bookings
        const eventIds = transactions
            .filter(t => t.eventId)
            .map(t => t.eventId!);

        if (eventIds.length > 0) {
            const bookings = await this.bookingRepo.findBookingsByIds(pool, eventIds);
            const bookingMap = new Map(bookings.map(b => [b.id, b]));

            transactions.forEach(transaction => {
                if (transaction.eventId) {
                    transaction.booking = bookingMap.get(transaction.eventId);
                }
            });
        }

        return {
            totalItems: result.totalItems,
            totalPages: result.totalPages,
            items: transactions
        } as PageOfData<Transaction>;
    }

    private async getPackagesByIds(ids: string[]): Promise<Package[]> {
        if (ids.length === 0) return [];
        return this.creditRepo.getPackagesByIds(pool, ids);
    }

    async cleanup(): Promise<number> {
        return this.creditRepo.cleanup(pool);
    }

    async processExpiredCredits(by: string): Promise<string[]> {
        let conn = null;
        try {
            conn = await pool.getConnection();
            await conn.beginTransaction();

            // 1. Get expired members before processing
            const expiredMembers = await this.memberCreditRepo.findExpiredCredits(conn);
            const expiredMemberIds = expiredMembers.map(m => m.memberId);

            // 2. Create expiry transactions for all expired credits
            const trxCount = await this.creditRepo.bulkInsertExpiryTransactions(conn, by);
            logger.info(`[${by}] processExpiredCredits() created ${trxCount} expiry transactions`);

            // 3. Then reset all expired credits to zero
            const resetCount = await this.memberCreditRepo.bulkResetExpiredCredits(conn, by);
            logger.info(`[${by}] processExpiredCredits() reset ${resetCount} expired credits`);

            await conn.commit();
            return expiredMemberIds;
        } catch (error) {
            conn && (await conn.rollback());
            logger.error(`[${by}] processExpiredCredits() error! ${error}`);
            throw error;
        } finally {
            conn && conn.release();
        }
    }

    async extendExpiry(req: CreditExtendExpiry): Promise<Transaction> {
        let conn = null;
        try {
            conn = await pool.getConnection();
            await conn.beginTransaction();

            // 1. Check member's credit balance
            const credit = await this.memberCreditRepo.findByMemberId(conn, req.by.uid);
            if (!credit) {
                throw new Error("Member credit record not found");
            }

            // 2. Calculate credits needed based on months
            const creditsPerMonth = parseInt(process.env.CREDITS_RENEW_PER_MONTH || '100');
            const creditsNeeded = req.months * creditsPerMonth;
            if (credit.currentCredits < creditsNeeded) {
                throw new Error(`Insufficient credits. Required: ${creditsNeeded}, Available: ${credit.currentCredits}`);
            }

            // 3. Create transaction record
            const transactionId = await this.creditRepo.createTrx(conn, {
                transaction: {
                    memberId: req.by.uid,
                    type: TransactionType.Renewal,
                    amount: creditsNeeded,
                    status: TransactionStatus.Success,
                },
                by: req.by,
            });

            // 4. Update member's credit balance and extend expiry
            await this.memberCreditRepo.updateMemberCredit(
                conn,
                req.by.uid,
                -Math.abs(creditsNeeded),
                req.by.username,
                req.months
            );

            await conn.commit();
            logger.info(`extendExpiry() success, memberId=${req.by.uid}, credits=${creditsNeeded}, months=${req.months}`);

            // Get the created transaction
            const transaction = await this.creditRepo.getTrxById(conn, transactionId);
            if (!transaction) {
                throw new Error("Failed to retrieve created transaction");
            }

            return transaction;
        } catch (error) {
            conn && (await conn.rollback());
            logger.error(`extendExpiry() error! ${error}`);
            throw error;
        } finally {
            conn && conn.release();
        }
    }

    // Add this method to CreditService class
    async isEligibleForPackage(memberId: string, packageId: string): Promise<{eligible: boolean, reason?: string}> {
        const pkg = await this.getPackageById(packageId);
        if (!pkg) {
            return { eligible: false, reason: "Package not found" };
        }

        // Check date range
        const now = DateTime.now().setZone('UTC');
        if (pkg.validFrom && DateTime.fromISO(pkg.validFrom) > now) {
            return { eligible: false, reason: "Package not yet available" };
        }
        if (pkg.validTo && DateTime.fromISO(pkg.validTo) < now) {
            return { eligible: false, reason: "Package no longer available" };
        }

        // Check first-time only restriction
        if (pkg.firstTimeOnly) {
            const hasPreviousTopups = await this.creditRepo.hasMemberPreviousTopups(pool, memberId);
            if (hasPreviousTopups) {
                return { eligible: false, reason: "Package is for first-time purchases only" };
            }
        }

        // Check purchase limit
        if (pkg.purchaseLimit) {
            const purchaseCount = await this.creditRepo.getMemberPackagePurchaseCount(pool, memberId, packageId);
            if (purchaseCount >= pkg.purchaseLimit) {
                return { eligible: false, reason: `Purchase limit of ${pkg.purchaseLimit} reached for this package` };
            }
        }

        // Check instructor-only restriction
        if (pkg.instructorOnly) {
            const instructor = await this.instructorRepo.findByFirebaseUid(pool,memberId);
            if (!instructor) {
                return { eligible: false, reason: "Package is for instructors only" };
            }
        }

        return { eligible: true };
    }

    async incrementPackagePurchaseCount(transaction: Transaction, by: string): Promise<void> {
        try {
            if (transaction.status === TransactionStatus.ConfirmedPayment && transaction.packageId) {
                await this.creditRepo.incrementPackagePurchaseCount(pool, transaction.memberId, transaction.packageId, by);
            }
        } catch (error) {
            logger.error(`incrementPackagePurchaseCount() error! ${error}`);
            throw error;
        }
    }
}