import pool from "../../init-pool";
import { Transaction, TransactionSaveReq, TransactionStatus, TransactionType } from "@booking-api/models/credit";
import { PromotionRepo, ValidatedPromoCode } from "@booking-api/repo/promotion-repo";
import { PromotionStatus, PromotionType } from "@backoffice-cms/models/promotion";
import { Connection } from "mysql2/promise";
import { By } from "@shared/models";
import { CreditRepo } from "@booking-api/repo/credit-repo";
import { CreditRepo as MemberCreditRepo } from "@member-api/repo/credit-repo";

export class PromotionService {
    private repo = new PromotionRepo();
    private creditRepo = new CreditRepo();
    private memberCreditRepo = new MemberCreditRepo();

    async applyTopupPromoCode(promoCode: string, by: By): Promise<Transaction> {
        const validatedPromo = await this.findAndValidatePromoCode(promoCode, by.uid, PromotionType.TOPUP);
        const { promotion } = validatedPromo;

        const conn = await pool.getConnection();
        try {
            await conn.beginTransaction();

            // 1. Create transaction record in booking-api
            const transactionModel: Transaction = {
                memberId: by.uid,
                amount: promotion.value,
                type: TransactionType.Promotion,
                status: TransactionStatus.Success,
                remark: `Promo Code: ${promoCode} (${promotion.name})`,
            };
            const txReq: TransactionSaveReq = { transaction: transactionModel, by: by };
            const trxId = await this.creditRepo.createTrx(conn, txReq);

            // 2. Update member credit balance in member-api
            await this.memberCreditRepo.updateMemberCredit(conn, by.uid, Math.abs(promotion.value), by.username);

            // 3. Record promo code usage
            await this.recordUsage(conn, validatedPromo.promoCode.id, by.uid, trxId, null);

            await conn.commit();

            const createdTransaction = await this.creditRepo.getTrxById(conn, trxId);
            return createdTransaction!;

        } catch (err) {
            await conn.rollback();
            throw err;
        } finally {
            conn.release();
        }
    }

    async findAndValidatePromoCode(code: string, userId: string, type: PromotionType): Promise<ValidatedPromoCode> {
        // Only find promo code for the requested type
        const result = await this.repo.findPromoCodeByCode(pool, code, type);

        if (!result) {
            throw new Error("Promo code not found.");
        }

        const { promoCode, promotion } = result;

        if (promoCode.status !== PromotionStatus.ACTIVE || promotion.status !== PromotionStatus.ACTIVE) {
            throw new Error("This promo code is not active.");
        }

        const now = new Date();
        if (new Date(promotion.start_date) > now || new Date(promotion.end_date) < now) {
            throw new Error("Promo code is not valid at this time.");
        }

        if (promoCode.assigned_user_id && promoCode.assigned_user_id !== userId) {
            throw new Error("This promo code is not assigned to you.");
        }

        // Per-code limit
        if (promoCode.code_limit) {
            const usageCount = await this.repo.getUsageCount(pool, promoCode.id);
            if (usageCount >= promoCode.code_limit) {
                throw new Error("Promo code has reached its usage limit.");
            }
        }

        // Per-user limit
        if (promotion.per_user_limit) {
            const userUsageCount = await this.repo.getPromotionUsageByUser(pool, promotion.id, userId);
            if (userUsageCount >= promotion.per_user_limit) {
                throw new Error("You have already reached the usage limit for this promotion.");
            }
        }

        // Global limit
        if (promotion.global_limit) {
            const globalUsageCount = await this.repo.getPromotionGlobalUsage(pool, promotion.id);
            if (globalUsageCount >= promotion.global_limit) {
                throw new Error("This promotion has reached its global usage limit.");
            }
        }

        return result;
    }

    async recordUsage(conn: Connection, promoCodeId: number, userId: string, ctrxId: string | null, eventId: string | null): Promise<number> {
        return this.repo.insertPromoCodeUsage(conn, promoCodeId, userId, ctrxId, eventId);
    }
}