import {
  BookingFees,
  BookingFeeType,
  BookingLineItem,
  DayType,
  LatLng,
  SearchAt,
  SearchCriteria,
  SearchEquipment,
  SearchInstructor,
  SearchResult,
  SearchResultGroup,
  SearchResultWrapper,
  SearchSlot,
  SearchStudio
} from "@booking-api/models/search";
import { SearchRepo } from "@booking-api/repo/search-repo";
import { CreditRepo } from "@booking-api/repo/credit-repo";
import { PublicRepo } from "@public-api/repo/public-repo";
import { SessionRepo } from "../repo/session-repo";
import { EquipmentType } from "@studio-cms/models/equipment";
import { StudioEquipment } from "@studio-cms/models/studio";
import { Big } from "big.js";
import { DateTime } from 'luxon';
import pool from "../../init-pool";
import logger from "@utils/logger";
import { calculateFee, convertCreditsToFee } from "@shared/fee-calculator";
import { SessionStatus } from "@booking-api/models/booking";
import { StudioRepo } from "@studio-cms/repo/studio-repo";
import { InstructorRepo } from "@instructor-api/repo/instructor-repo";
import { PromotionService } from "./promotion-service";
import { PromotionType } from "@backoffice-cms/models/promotion";

// ALLOW_BOOK_SELF is for development convenience
const allowBookSelf = process.env.APP_ALLOW_BOOK_SELF == "true";

export class SearchService {

  private publicRepo = new PublicRepo();
  private searchRepo = new SearchRepo();
  private sessionRepo = new SessionRepo();
  private creditRepo = new CreditRepo();
  private studioRepo = new StudioRepo();
  private instructorRepo = new InstructorRepo();
  private promotionService = new PromotionService();

  async equipmentTypes(): Promise<EquipmentType[]> {
    return this.searchRepo.findEquipmentTypes(pool);
  }

  private mergeSearchResultGroups(
    group1: SearchResultGroup,
    group2: SearchResultGroup,
  ): SearchResultGroup {
    const instr1 = group1.instructor!;
    const instr2 = group2.instructor!;
    if (instr1.id !== instr2.id) {
      throw "Can only merge search result groups for the same instructor!";
    }
    return {
      instructor: instr1,
      slots: [
        ...group1.slots,
        ...group2.slots,
      ],
    } as SearchResultGroup;
  }

  private mergeSearchResults(
    criteria: SearchCriteria,
    results1: SearchResult[],
    results2: SearchResult[],
  ): SearchResult[] {
    const map = new Map<String, SearchResult>();
    for (const result of results1) {
      const studioId = result.studio.id;
      if (map.has(studioId)) {
        map.set(studioId, this.mergeSearchResult(criteria, map.get(studioId)!, result));
      } else {
        map.set(studioId, result);
      }
    }
    for (const result of results2) {
      const studioId = result.studio.id;
      if (map.has(studioId)) {
        map.set(studioId, this.mergeSearchResult(criteria, map.get(studioId)!, result));
      } else {
        map.set(studioId, result);
      }
    }
    return Array.from(map.values());
  }

  private mergeSearchResult(
    criteria: SearchCriteria,
    result1: SearchResult,
    result2: SearchResult,
  ): SearchResult {
    const studio1 = result1.studio;
    const studio2 = result2.studio;
    if (studio1.id !== studio2.id) {
      throw "Can only merge search results for the same studio!";
    }

    const instrLessSlots = [] as SearchSlot[];
    result1.groups.filter(o => !o.instructor).forEach(o => instrLessSlots.push(...o.slots));
    result2.groups.filter(o => !o.instructor).forEach(o => instrLessSlots.push(...o.slots));

    const instrSlots = new Map<String, SearchResultGroup>();
    [result1, result2].forEach(result => {
      result.groups.filter(o => !!o.instructor).forEach(o => {
        const instrId = o.instructor!.id;
        if (instrSlots.has(instrId)) {
          instrSlots.set(instrId, this.mergeSearchResultGroups(instrSlots.get(instrId)!, o));
        } else {
          instrSlots.set(instrId, o);
        }
      });
    })

    return {
      studio: studio1,
      groups: [
        ...(!!studio1.instrLess && !criteria.faveInstrOnly && !criteria.instrId ? [{
          // instructor: <none>,
          slots: instrLessSlots.sort((a, b) => a.time.localeCompare(b.time)),
        }] : []),
        ...Array.from(instrSlots.values()).map(o => ({
          instructor: o.instructor,
          slots: o.slots.sort((a, b) => a.time.localeCompare(b.time)),
        })),
      ],
    } as SearchResult;
  }

  async searchExtended(criteria: SearchCriteria): Promise<SearchResultWrapper> {
    const timezone = criteria.timezone || 'Asia/Singapore';
    const now = DateTime.now().setZone(timezone);
    let at = DateTime.fromFormat(`${criteria.at.date}${criteria.at.time}`, 'yyyy-MM-ddHH:mm', { zone: timezone });
    const today = now.toFormat('yyyy-MM-dd') === at.toFormat('yyyy-MM-dd');

    let minTime: DateTime;
    let maxTime: DateTime;
    const atNextDay = at.plus({ days: 1 }).startOf('day');
    const isWithinNextDayThreshold = criteria.at.time < "23:00";

    // Ensure minTime is not less than current time
    if (today) {
      // For today, use current time as minimum, rounded up to the next slot
      let ceiled = now.plus({ minutes: 30 - (now.minute % 30) }).startOf('minute');
      minTime = ceiled;
      // Ensure minTime is strictly after now
      if (minTime <= now) {
        minTime = minTime.plus({ minutes: 30 });
      }
    } else {
      // For future dates, use start of day
      minTime = at.startOf('day');
    }

    if (isWithinNextDayThreshold) {
      maxTime = DateTime.fromFormat(`${criteria.at.date}23:00`, 'yyyy-MM-ddHH:mm', { zone: timezone });
    } else {
      const atNextDayStr = atNextDay.toFormat('yyyy-MM-dd');
      maxTime = DateTime.fromFormat(`${atNextDayStr}23:00`, 'yyyy-MM-ddHH:mm', { zone: timezone });
    }

    // If minTime is after maxTime, adjust maxTime to be at least 30 minutes after minTime
    if (minTime > maxTime) {
      maxTime = minTime.plus({ minutes: 30 });
    }

    // Optimized slot search loop
    let results: SearchResult[] = [];
    const searchedTimes = new Set<string>();
    const step = 30; // minutes
    at = minTime;
    let timePoints: DateTime[] = [at];
    let upper = at;
    let slotsFound = 0;
    let studiosFound = 0;

    while ((slotsFound / (studiosFound || 1)) < 48) {
      // Expand upper (forward in time)
      if (upper < maxTime) {
        upper = upper.plus({ minutes: step });
        if (upper <= maxTime) timePoints.push(upper);
      }
      // If upper bound reached, break
      if (upper >= maxTime) break;

      // Search all new timePoints not yet searched
      for (const t of timePoints) {
        const key = t.toISO();
        if (!searchedTimes.has(key)) {
          searchedTimes.add(key);
          const c = { ...criteria, at: new SearchAt(t.toFormat('yyyy-MM-dd'), t.toFormat('HH:mm')) };
          const r = await this.search(c, false);
          results = this.mergeSearchResults(criteria, results, r);
        }
      }

      // Recalculate slotsFound/studiosFound
      studiosFound = results.length;
      slotsFound = 0;
      results.forEach(o => {
        o.groups.filter(o => !!o.instructor).forEach(o => {
          slotsFound += o.slots.length;
        });
      });
    }

    results = results.map(result => ({
      ...result,
      groups: result.groups.filter(group => group.slots && group.slots.length > 0)
    }))
    .filter(result => result.groups && result.groups.length > 0);

    // Add Associated Items (Studio)
    let studioIds = results.map(o => o.studio.id);
    const studioRating = await this.publicRepo.getStudioRatingByIds(pool, studioIds);

    const ratingsMap = new Map<string, { rating: number; total: number }>();
    studioRating.forEach(({ id, rating, total }) => {
      ratingsMap.set(id, { rating, total });
    });

    const equipments = await this.publicRepo.getStudioEquipmentTypeByIds(pool, studioIds);
    const equipmentsMap = new Map<string, StudioEquipment[]>();
    // Group equipments by studioId
    equipments.forEach(({ studioId, typeId, typeName }) => {
      if (!equipmentsMap.has(studioId)) {
        equipmentsMap.set(studioId, []);
      }
      equipmentsMap.get(studioId)?.push({ studioId, typeId, typeName });
    });

    let favStudios = [];
    criteria.byId && (favStudios = await this.publicRepo.getFaveStudiosByUserId(pool, criteria.byId));

    // Add Associated Items (Instructor)
    let instrIds = [];
    results.forEach(o => o.groups.forEach(o => { o.instructor && instrIds.push(o.instructor.id) }));

    const instrRatings = await this.publicRepo.getInstructorsRatingByIds(pool, instrIds);
    const instrRatingsMap = new Map<string, { rating: number; total: number }>();
    instrRatings.forEach(({ id, rating, total }) => {
      instrRatingsMap.set(id, { rating, total });
    });

    let favInstrs = [];
    criteria.byId && (favInstrs = await this.publicRepo.getFaveInstructorsByUserId(pool, criteria.byId));

    results = results.map((result) => {
      const ratingInfo = ratingsMap.get(result.studio.id);
      const groups = result.groups.map((group) => {
        if (group.instructor) {
          const instrRatingInfo = instrRatingsMap.get(group.instructor.id);
          group.instructor = {
            ...group.instructor,
            ...{
              avgRating: instrRatingInfo?.rating ?? 0,
              totalRatingCount: instrRatingInfo?.total ?? 0,
              fav: favInstrs.includes(group.instructor.id)
            }
          }
        }
        return group;
      });
      return {
        ...result,
        ...{
          studio: {
            ...result.studio,
            avgRating: ratingInfo?.rating ?? 0,
            totalRatingCount: ratingInfo?.total ?? 0,
            equipments: equipmentsMap.get(result.studio.id) || [],
            fav: favStudios.includes(result.studio.id)
          }
        },
        ...groups
      };
    });

    return {
      extendStart: minTime.toFormat('yyyy-MM-dd HH:mm:ss'),
      extendEnd: maxTime.toFormat('yyyy-MM-dd HH:mm:ss'),
      results: results,
    } as SearchResultWrapper;
  }

  async search(criteria: SearchCriteria, associatedItems: Boolean = true): Promise<SearchResult[]> {
    // logger.debug(`search() criteria=${JSON.stringify(criteria)}`);

    const searchDateTime = DateTime.fromFormat(
      `${criteria.at.date}${criteria.at.time}`,
      "yyyy-MM-ddHH:mm",
      { zone: criteria.timezone || 'Asia/Singapore' }
    );
    const now = DateTime.now().setZone(criteria.timezone || 'Asia/Singapore');
    if (searchDateTime < now) {
      logger.debug(`Search datetime ${criteria.at.date} ${criteria.at.time} is in the past, now is ${now.toFormat('yyyy-MM-dd HH:mm:ss')}`);
      return [];
    }

    // Bookings do not span across days currently
    if (criteria.at.time > '23:00') return [];

    // Derive day type - WD, WE, PH
    criteria.dayType = await this.dayType(criteria.at);

    const instrIdsIn = await this.findInstructorIds(criteria);
    // if (instrIdsIn.length === 0) return []; // (!) cannot terminate here yet,
                                               // as there may be instrLess studios.

    if (
      (instrIdsIn.length === 0)
      && (criteria.faveInstrOnly || criteria.instrId)
    ) {
      return []; // member searched for instructors,
                 // no need to look for instrLess studios.
    }

    const studioIdsIn = await this.findStudioIds(criteria);
    if (studioIdsIn.length === 0) return [];

    const equipIdsIn = await this.findEquipmentIds(criteria, studioIdsIn);
    if (equipIdsIn?.length === 0) return [];

    const studios = await this.searchRepo.findStudiosByEquipmentIds(pool, criteria, equipIdsIn);
    for (const o of studios) {
      o.pictures = await this.studioRepo.prefixStudioPictures(pool, o.pictures);
    }
    // studios.forEach(o => logger.debug(`studio=${o.name} (id=${o.id})`));

    let allowInstrLess = false;
    if(criteria.byId) {
      const instructor = await this.publicRepo.findInstructorById(pool, criteria.byId);
      if(instructor) {
        allowInstrLess = true;
      } else {
        const session = await this.searchRepo.findCompletedSessionByMemberId(pool, criteria.byId);
        if(session) {
          allowInstrLess = true;
        }
      }
    }

    const includeEquipment = criteria.includeEquipment ?? false;

    const ret = [] as SearchResult[];
    for (const studio of studios) {
      const instructors = await this.findInstructors(criteria, instrIdsIn, studio);
      for (const o of instructors) {
        o.pictures = await this.instructorRepo.prefixInstructorPictures(pool, o.pictures);
      }
      let equipments: SearchEquipment[]|undefined = undefined;
      const instrRatingsMap = new Map<string, { rating: number; total: number }>();
      let favInstrs = [];
      if (includeEquipment) {
        const availableEquips = await this.searchRepo.findEquipmentByIds(pool, studio.id, equipIdsIn, criteria.at);
        if( availableEquips.length > 0) {
          equipments = [] as SearchEquipment[];
          const bookingFees = await this.getBookingFeesByEquipIds(
            criteria.at,
            availableEquips.map(e => e.id),
            criteria.instrId
          );

          for (const availableEquip of availableEquips) {
            const bookingFee = bookingFees.get(availableEquip.id)!;
            bookingFee !== undefined && equipments.push({
              ...availableEquip,
              fees: bookingFee
            } as SearchEquipment);
          }
        }
      }

      // with associated items
      if(associatedItems) {
        const studioRating = await this.publicRepo.getStudioRatingByIds(pool, [studio.id]);
        if(studioRating[0]) {
          studio.avgRating = studioRating[0].rating ?? 0;
          studio.totalRatingCount = studioRating[0].total ?? 0;
        }

        const studioEquipments = await this.publicRepo.getStudioEquipmentTypeByIds(pool, [studio.id]);
        studio.equipments = studioEquipments || [];

        if(criteria.byId) {
          const favStudios = await this.publicRepo.getFaveStudiosByUserId(pool, criteria.byId, [studio.id]);
          studio.fav = favStudios.includes(studio.id);
        }

        let instrIds = instructors.map(instructor => instructor.id);
        const instrRatings = await this.publicRepo.getInstructorsRatingByIds(pool, instrIds);
        instrRatings.forEach(({ id, rating, total }) => {
          instrRatingsMap.set(id, { rating, total });
        });
        criteria.byId && (favInstrs = await this.publicRepo.getFaveInstructorsByUserId(pool, criteria.byId));
      }
      ret.push({
        studio: {
          ...studio,
          ...(!!criteria.latLng && !!studio.latLng) && {distance: SearchService.distanceInKm(criteria.latLng, studio.latLng)}
        },
        groups: [
          ...(!!allowInstrLess && !!studio.instrLess && !criteria.faveInstrOnly && !criteria.instrId ? [{
            // instructor: <none>,
            slots: [{
              time: criteria.at.time,
            } as SearchSlot]
          }] : []),
          ...instructors.map((o) => {
            const instrRatingInfo = instrRatingsMap.get(o.id);
            return {
                instructor: {
                ...o,
                ...instrRatingInfo && {
                  avgRating: instrRatingInfo.rating ?? 0,
                  totalRatingCount: instrRatingInfo.total ?? 0,
                },
                ...favInstrs.includes(o.id) && {
                  fav: favInstrs.includes(o.id)
                }
              },
              slots: [{
                time: criteria.at.time,
              } as SearchSlot]
            } as SearchResultGroup
          }),
        ],
        ...includeEquipment && { equipment: equipments },
      } as SearchResult);
    }
    return ret;
  }

  private async findInstructorIds(
    criteria: SearchCriteria,
  ): Promise<string[]> {
    let instrIdsIn = (criteria.instrId) ? [criteria.instrId] : undefined;

    if (criteria.instrLessOnly) {
      instrIdsIn = [];
    }

    // instructors working on slot
    const workHoursInstrIds = await this.searchRepo
      .findWorkHoursInstructorIds(pool, criteria, instrIdsIn);
    // logger.debug(`findWorkHoursInstructorIds() results=${JSON.stringify(workHoursInstrIds)}`);
    instrIdsIn = !!instrIdsIn
      ? instrIdsIn!.filter(o => workHoursInstrIds.includes(o)) // (!) includes
      : workHoursInstrIds;

    // instructors with rates on slot
    const ratesInstrIds = await this.searchRepo
      .findRatesInstructorIds(pool, criteria, instrIdsIn);
    // logger.debug(`findRatesInstructorIds() results=${JSON.stringify(ratesInstrIds)}`);
    instrIdsIn = !!instrIdsIn
      ? instrIdsIn!.filter(o => ratesInstrIds.includes(o)) // (!) includes
      : ratesInstrIds;

    // booked instructors
    const bookedInstrIds = await this.searchRepo
      .findBookedInstructorIds(pool, criteria, instrIdsIn);
    // logger.debug(`findBookedInstructorIds() results=${JSON.stringify(bookedInstrIds)}`);
    instrIdsIn = instrIdsIn.filter(o => !bookedInstrIds.includes(o)); // (!) NOT includes

    // blocked instructors
    const blockedInstrIds = await this.searchRepo
      .findBlockedInstructorIds(pool, criteria, instrIdsIn);
    // logger.debug(`findBlockedInstructorIds() results=${JSON.stringify(blockedInstrIds)}`);
    instrIdsIn = instrIdsIn.filter(o => !blockedInstrIds.includes(o)); // (!) NOT includes

    if (!allowBookSelf && criteria.byId) {
      instrIdsIn = instrIdsIn.filter(o => o !== criteria.byId); // (!) EXCLUDES self
    }

    /* moved up
    // instructors working on slot
    const workHoursInstrIds = await this.searchRepo
      .findWorkHoursInstructorIds(pool, criteria, instrIdsIn);
    logger.debug(`findWorkHoursInstructorIds() results=${JSON.stringify(workHoursInstrIds)}`);
    instrIdsIn = instrIdsIn.filter(o => workHoursInstrIds.includes(o)); // (!) includes
    */

    return instrIdsIn;
  }

  private async findStudioIds(
    criteria: SearchCriteria,
  ): Promise<string[]> {
    let studioIdsIn = (criteria.studioId) ? [criteria.studioId] : undefined;

    // matched studios
    const matchedStudioIds = await this.searchRepo
      .findMatchedStudioIds(pool, criteria, studioIdsIn);
    // logger.debug(`findMatchedStudioIds() results=${JSON.stringify(matchedStudioIds)}`);
    studioIdsIn = !!studioIdsIn
      ? studioIdsIn.filter(o => matchedStudioIds.includes(o)) // (!) includes
      : matchedStudioIds;

    // blocked studios
    const blockedStudioIds = await this.searchRepo
      .findBlockedStudioIds(pool, criteria, studioIdsIn);
    // logger.debug(`findBlockedStudioIds() results=${JSON.stringify(blockedStudioIds)}`);
    studioIdsIn = studioIdsIn.filter(o => !blockedStudioIds.includes(o)); // (!) NOT includes

    // distance within
    await this.searchRepo
      .cacheDistanceToStudios(pool, criteria.latLng, studioIdsIn);
    const studioIdsWithinDistance = await this.searchRepo
      .findStudioIdsWithinDistance(pool, criteria, studioIdsIn);
    // logger.debug(`studioIdsWithinDistance=${JSON.stringify(studioIdsWithinDistance)}`);
    studioIdsIn = studioIdsIn.filter(o => studioIdsWithinDistance.includes(o)); // (!) includes

    return studioIdsIn;
  }

  private async findEquipmentIds(
    criteria: SearchCriteria,
    studioIdsIn: string[],
  ): Promise<string[]> {
    let equipIdsIn = (criteria.equipId) ? [criteria.equipId] : undefined;

    // equipment availability
    const availEquipIds = await this.searchRepo
      .findAvailableEquipmentIds(pool, criteria, studioIdsIn, equipIdsIn);
    // logger.debug(`availEquipIds=${JSON.stringify(availEquipIds)}`);
    equipIdsIn = !!equipIdsIn
      ? equipIdsIn.filter(o => availEquipIds.includes(o)) // (!) includes
      : availEquipIds;

    /* unused
    // equipment with rates on slot
    const ratesEquipIds = await this.searchRepo
      .findRatesEquipmentIds(pool, criteria, studioIdsIn, equipIdsIn);
    // logger.debug(`findRatesEquipmentIds() results=${JSON.stringify(ratesEquipIds)}`);
    equipIdsIn = !!equipIdsIn
      ? equipIdsIn!.filter(o => ratesEquipIds.includes(o)) // (!) includes
      : equipIdsIn;
    */

    // blocked equipment
    const blockedEquipIds = await this.searchRepo
      .findBlockedEquipmentIds(pool, criteria, equipIdsIn);
    // logger.debug(`blockedEquipIds=${JSON.stringify(blockedEquipIds)}`);
    equipIdsIn = equipIdsIn.filter(o => !blockedEquipIds.includes(o)); // (!) NOT includes

    return equipIdsIn;
  }

  private async findInstructors(
    criteria: SearchCriteria,
    instrIdsIn: string[],
    studio: SearchStudio,
  ): Promise<SearchInstructor[]> {
    const matchedInstrIds = [];
    for (const instrId of instrIdsIn) {
      // Get mobility
      const mobility = await this.searchRepo
        .findInstructorMobility(pool, criteria, instrId);
      if (!mobility) {
        /*
        logger.debug(`Instructor (id=${instrId}) has no mobility for ${criteria.at.toString()}`);
        */
        continue;
      }

      // Within mobility
      const studioIdsIn = [studio.id];
      await this.searchRepo
        .cacheDistanceToStudios(pool, mobility.latLng, studioIdsIn);
      const studioIds = await this.searchRepo
        .findStudioIdsWithinMobility(pool, mobility, studioIdsIn);
      if (studioIds.length == 0) {
        /*
        logger.debug(`Instructor (id=${instrId}) has no studios within mobility`
          + ` (dow=${mobility.dow},`
          + ` latlng=${JSON.stringify(mobility.latLng)},`
          + ` distance=${mobility.distance})`);
        */
        continue;
      }

      matchedInstrIds.push(instrId);
    }
    return this.searchRepo.findInstructorsByIds(pool, matchedInstrIds);
  }

  async findEquipmentFee(
    equipId: string,
    dayType: DayType,
    time: string,
  ): Promise<string> {
    return this.searchRepo.findEquipmentFee(pool, equipId, dayType, time);
  }

  async findInstructorFee(
    equipId: string,
    dayType: DayType,
    time: string,
  ): Promise<string> {
    return this.searchRepo.findInstructorFee(pool, equipId, dayType, time);
  }

  async findOthersFee(): Promise<string> {
    return this.searchRepo.findOthersFee(pool);
  }

  async getBookingFees(
    at: SearchAt,
    equipId: string,
    instrId?: string,
    promoCode?: string,
    memberId?: string
  ): Promise<BookingFees> {
    // Derive day type - WD, WE, PH
    const dayType = await this.dayType(at);

    const equipmentFee = await this.findEquipmentFee(equipId, dayType, at.time);
    const instructorFee = !!instrId
      ? await this.findInstructorFee(instrId, dayType, at.time)
      : undefined;
    const othersFee = await this.findOthersFee();

    // convert price to credits
    const exchangeRate = await this.creditRepo.getLatestExchangeRate(pool, 'SGD');

    const equipmentFeeCalc = calculateFee(+equipmentFee, +exchangeRate.bookingRate);
    const instructorFeeCalc = instructorFee ? calculateFee(+instructorFee, +exchangeRate.bookingRate) : undefined;
    const othersFeeCalc = calculateFee(+othersFee, +exchangeRate.bookingRate);

    let total = Big(0);
    let totalCredits = Big(0);
    const fees = [] as BookingLineItem[];

    // Equipment (aka Studio) fee
    fees.push({
      name: "Studio Fee",
      type: BookingFeeType.studio,
      price: equipmentFeeCalc.fee,
      credit: equipmentFeeCalc.credits,
      currency: 'SGD',
    } as BookingLineItem);
    total = total.add(equipmentFee);
    totalCredits = totalCredits.add(equipmentFeeCalc.credits);

    // Instructor fee
    if (instructorFeeCalc) {
      fees.push({
        name: "Instructor Fee",
        type: BookingFeeType.instructor,
        price: instructorFeeCalc.fee,
        credit: instructorFeeCalc.credits,
        currency: 'SGD',
      } as BookingLineItem);
      total = total.add(instructorFee);
      totalCredits = totalCredits.add(instructorFeeCalc.credits);
    }

    // Others fee
    fees.push({
      name: "Platform",
      type: BookingFeeType.others,
      price: othersFeeCalc.fee,
      credit: othersFeeCalc.credits,
      currency: 'SGD',
    } as BookingLineItem);
    total = total.add(othersFee);
    totalCredits = totalCredits.add(othersFeeCalc.credits);

    if (promoCode && memberId) {
      try {
        const validatedPromo = await this.promotionService.findAndValidatePromoCode(
          promoCode,
          memberId,
          PromotionType.BOOKING
        );
        if (validatedPromo) {
          const promoFee = convertCreditsToFee(validatedPromo.promotion.value, +exchangeRate.bookingRate);
          fees.push({
            name: `Promo Code: ${validatedPromo.promoCode.code} (${validatedPromo.promotion.name})`,
            type: BookingFeeType.promotion,
            price: promoFee.toFixed(2),
            credit: validatedPromo.promotion.value.toString(),
            currency: 'SGD',
            sign: '-',
          } as BookingLineItem);
          total = total.sub(promoFee);
          totalCredits = totalCredits.sub(validatedPromo.promotion.value);
        }
      } catch (e: any) {
        logger.warn(`[search-service] promo code validation failed for ${promoCode} by ${memberId}: ${e.message}`);
      }
    }

    return {
      items: fees,
      total: total.toFixed(2),
      totalCredits: totalCredits.toFixed(0),
      currency: 'SGD',
    } as BookingFees;
  }

  async getBookingFeesByEquipIds(
    at: SearchAt,
    equipIds: string[],
    instrId?: string,
  ): Promise<Map<string, BookingFees>> {
    // Derive day type - WD, WE, PH
    const dayType = await this.dayType(at);

    const equipmentFees = await this.searchRepo.findEquipmentFees(
      pool,
      equipIds,
      dayType,
      at.time
    );
    const instructorFee = !!instrId
      ? await this.findInstructorFee(instrId, dayType, at.time)
      : undefined;
    const othersFee = await this.findOthersFee();
    const exchangeRate = await this.creditRepo.getLatestExchangeRate(pool, 'SGD');

    const feesMap = new Map<string, BookingFees>();
    equipIds.forEach(equipId => {
      // convert price to credits
      const equipmentFee = equipmentFees.get(equipId)!;
      const equipmentFeeCalc = calculateFee(+equipmentFee, +exchangeRate.bookingRate);
      const instructorFeeCalc = instructorFee ? calculateFee(+instructorFee, +exchangeRate.bookingRate) : undefined;
      const othersFeeCalc = calculateFee(+othersFee, +exchangeRate.bookingRate);

      let total = Big(0);
      let totalCredits = Big(0);
      const fees = [] as BookingLineItem[];

      // Equipment (aka Studio) fee
      fees.push({
        name: "Studio Fee",
        type: BookingFeeType.studio,
        price: equipmentFeeCalc.fee,
        credit: equipmentFeeCalc.credits,
        currency: 'SGD',
      } as BookingLineItem);
      total = total.add(equipmentFee);
      totalCredits = totalCredits.add(equipmentFeeCalc.credits);

      // Instructor fee
      if (instructorFeeCalc) {
        fees.push({
          name: "Instructor Fee",
          type: BookingFeeType.instructor,
          price: instructorFeeCalc.fee,
          credit: instructorFeeCalc.credits,
          currency: 'SGD',
        } as BookingLineItem);
        total = total.add(instructorFee);
        totalCredits = totalCredits.add(instructorFeeCalc.credits);
      }

      // Others fee
      fees.push({
        name: "Platform",
        type: BookingFeeType.others,
        price: othersFeeCalc.fee,
        credit: othersFeeCalc.credits,
        currency: 'SGD',
      } as BookingLineItem);
      total = total.add(othersFee);
      totalCredits = totalCredits.add(othersFeeCalc.credits);

      feesMap.set(equipId, {
        items: fees,
        total: total.toFixed(2),
        totalCredits: totalCredits.toFixed(0),
        currency: 'SGD',
      } as BookingFees);
    });

    return feesMap;
  }

  private async dayType(at: SearchAt): Promise<DayType> {
    const isPH = await this.searchRepo.isPublicHoliday(pool, at.date);
    if (isPH) {
      return DayType.PH;
    }
    return ([6, 7].includes(at.dow))
      ? DayType.WE
      : DayType.WD;
  }

  // Ref: Calculate distance between two latitude-longitude points?
  // https://stackoverflow.com/a/27943
  // (Haversine formula)
  static distanceInKm(
    from: LatLng, to: LatLng,
  ) {
    const lat1 = from.lat;
    const lng1 = from.lng;
    const lat2 = to.lat;
    const lng2 = to.lng;

    const R = 6371; // Radius of the earth in km
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lng2 - lng1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in km
  }

  private static deg2rad(deg: number) {
    return deg * (Math.PI / 180)
  }

  async getServerTimezone(): Promise<string> {
    return this.searchRepo.getServerTimezone(pool);
  }
}
