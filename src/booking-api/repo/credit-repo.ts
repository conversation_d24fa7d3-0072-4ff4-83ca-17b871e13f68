import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import logger from "@utils/logger";
import { Package, Transaction, TransactionSaveReq, TransactionStatus, TransactionStatusSaveReq, ExchangeRate, TransactionType, TransactionSearchCriteria } from "@booking-api/models/credit";
import { Page, PageOfData, Sort } from "@shared/services";

export class CreditRepo {

    async getPackages(conn: Connection, currency: string = 'SGD', validDate: boolean = false): Promise<Package[]> {
        const columns = ["*"];
        const clauses = [];
        const params = [];
        let order = " order by sequence asc";

        {
            clauses.push("(status is true)");
        }

        clauses.push("currency = ?");
        params.push(currency);

        if (validDate) {
            clauses.push("(valid_from IS NULL OR valid_from <= NOW())");
            clauses.push("(valid_to IS NULL OR valid_to >= NOW())");
        }

        // select items
        let selectSql = `select ${columns.join(", ")} from credit01package`;
        if (clauses.length !== 0) {
            selectSql += ` where ${clauses.join(" and ")}`
        }
        selectSql += order;

        const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
        return results.map(o => CreditRepo.toPackage(o));
    }

    async getPackageById(conn: Connection, id: string): Promise<Package|undefined> {
        const sql = "select * from credit01package where id = ?";
        const params = [id];
        const [results] = await conn.query<RowDataPacket[]>(sql, params);

        if (results.length == 0) {
        return undefined;
        }
        return CreditRepo.toPackage(results[0]);
    }

    async createTrx(conn: Connection, req: TransactionSaveReq): Promise<string> {
        const sql = `insert into credit03transaction (
            id, member_id, type, amount, status,
            transaction_ref, payment_req_id, payment_id, package_id, event_id,
            remark,
            created_by
          ) values (
            ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ?
          )`;
        const by = req.by.username;
        const transaction = req.transaction;
        const uuid = crypto.randomUUID();
        logger.debug(`[${by}] create() transaction=${JSON.stringify(transaction)}`)
        const params = [
            uuid,
            transaction.memberId,
            transaction.type,
            transaction.amount,
            transaction.status ?? TransactionStatus.Success,
            transaction.transactionRef ?? null,
            transaction.paymentReqId ?? null,
            transaction.paymentId ?? null,
            transaction.packageId ?? null,
            transaction.eventId ?? null,
            transaction.remark ?? null,
            by,
        ];
        const [results] = await conn.execute<ResultSetHeader>(sql, params);
        logger.info(`[${by}] created transaction, id=${uuid}, numRows=${results.affectedRows}`);
        return uuid;
    }

    async updateTransactionStatus(
        conn: Connection, req: TransactionStatusSaveReq,
      ): Promise<number> {
        const sql = `update credit03transaction set
          status = ?,
          payment_id = ?,
          updated_by = ?
          where payment_req_id = ? and status != ?`;
        const by = req.by;
        logger.debug(`[${by}] updateTransactionStatus() req=${JSON.stringify(req)}`)
        const params = [
            req.status,
            req.paymentId ?? null,
            by,
            req.paymentReqId,
            req.status,
        ];
        const [results] = await conn.execute<ResultSetHeader>(sql, params);
        const affectedRows = results.affectedRows;
        logger.info(`[${by}] updateTransactionStatus() payReqId=${req.paymentReqId}, numRows=${affectedRows}`);
        return affectedRows;
    }

    async getTrxByPaymentReqId(conn: Connection, paymentReqId: string): Promise<Transaction|undefined> {
        const sql = `select
            id,
            member_id,
            type,
            amount,
            status,
            transaction_ref,
            payment_req_id,
            payment_id,
            package_id,
            event_id,
            remark,
            created_by,
            updated_by,
            created_at,
            updated_at
            from credit03transaction
            where payment_req_id = ?`;
        const [results] = await conn.query<RowDataPacket[]>(sql, [paymentReqId]);

        if (results.length === 0) {
            return undefined;
        }

        return CreditRepo.toTransaction(results[0]);
    }

    async getTrxById(conn: Connection, id: string): Promise<Transaction|undefined> {
        const sql = `select
            id,
            member_id,
            type,
            amount,
            status,
            transaction_ref,
            payment_req_id,
            payment_id,
            package_id,
            event_id,
            remark,
            created_by,
            updated_by,
            created_at,
            updated_at
            from credit03transaction
            where id = ?`;
        const [results] = await conn.query<RowDataPacket[]>(sql, [id]);

        if (results.length === 0) {
            return undefined;
        }

        return CreditRepo.toTransaction(results[0]);
    }

    async getTrxByEventId(conn: Connection, eventId: string): Promise<Transaction|undefined> {
        const sql = `select
            id,
            member_id,
            type,
            amount,
            status,
            transaction_ref,
            payment_req_id,
            payment_id,
            package_id,
            event_id,
            remark,
            created_by,
            updated_by,
            created_at,
            updated_at
            from credit03transaction
            where event_id = ?
            and deleted is false`;
        const [results] = await conn.query<RowDataPacket[]>(sql, [eventId]);

        if (results.length === 0) {
            return undefined;
        }

        return CreditRepo.toTransaction(results[0]);
    }

    async bulkInsertExpiryTransactions(conn: Connection, by: string): Promise<number> {
        const sql = `
            INSERT INTO credit03transaction (
                id, member_id, type, amount, status, created_by, created_at, updated_by, updated_at
            )
            SELECT
                UUID(),
                c.member_id,
                ?,
                c.current_credits,
                ?,
                ?,
                NOW(),
                ?,
                NOW()
            FROM usr05credit c
            WHERE c.credit_expired_date < NOW()
              AND c.current_credits > 0`;

        const params = [
            TransactionType.Expiry,
            TransactionStatus.Success,
            by,
            by
        ];

        const [results] = await conn.execute<ResultSetHeader>(sql, params);
        logger.info(`[${by}] bulkInsertExpiryTransactions() inserted ${results.affectedRows} transactions`);
        return results.affectedRows;
    }

    static toPackage(row: any): Package {
        return {
            id: row['id'],
            name: row['name'],
            currency: row['currency'],
            price: row['price'],
            credits: row['credits'],
            bonusCredits: row['bonus_credits'],
            status: row['status'],
            firstTimeOnly: row['first_time_only'] === 1,
            instructorOnly: row['instructor_only'] === 1,
            validFrom: row['valid_from'],
            validTo: row['valid_to'],
            purchaseLimit: row['purchase_limit'],
            sequence: row['sequence']
        }
    }

    static toTransaction(row: any): Transaction {
        return {
            id: row['id'],
            memberId: row['member_id'],
            type: row['type'],
            amount: +row['amount'],
            status: row['status'],
            transactionRef: row['transaction_ref'],
            paymentReqId: row['payment_req_id'],
            paymentId: row['payment_id'],
            packageId: row['package_id'],
            eventId: row['event_id'],
            remark: row['remark'],
            createdAt: row['created_at'],
            updatedAt: row['updated_at'],
        }
    }

    static toExchangeRate(row: any): ExchangeRate {
        return {
            id: row['id'],
            currency: row['currency'],
            purchaseRate: +row['purchase_rate'],
            bookingRate: +row['booking_rate'],
            effectiveDate: row['effective_date']
        }
    }

    async getLatestExchangeRate(conn: Connection, currency: string): Promise<ExchangeRate|undefined> {
        const sql = `select
            id,
            currency,
            purchase_rate,
            booking_rate,
            effective_date
            from credit02exchange_rate
            where currency = ?
            and effective_date <= CURDATE()
            order by effective_date desc
            limit 1`;
        const [results] = await conn.query<RowDataPacket[]>(sql, [currency]);

        if (results.length === 0) {
            return undefined;
        }

        return CreditRepo.toExchangeRate(results[0]);
    }

    async findTransactions(
        conn: Connection,
        criteria: TransactionSearchCriteria,
        page: Page
    ): Promise<PageOfData<Transaction>> {
        // prepare clauses and params
        const clauses = [];
        const params = [];
        {
            clauses.push("(member_id = ?)");
            params.push(criteria.memberId);

            clauses.push("(deleted is false)");
        }

        if (criteria.types && criteria.types.length > 0) {
            clauses.push("(type in (?))");
            params.push(criteria.types);
        }

        if (criteria.status && criteria.status.length > 0) {
            clauses.push("(status in (?))");
            params.push(criteria.status);
        }

        if (criteria.startDate) {
            clauses.push("(created_at >= ?)");
            params.push(criteria.startDate);
        }

        if (criteria.endDate) {
            clauses.push("(created_at <= ?)");
            params.push(criteria.endDate);
        }

        // Get total count
        let countSql = `select count(*) as count from credit03transaction`;
        if (clauses.length !== 0) {
            countSql += ` where ${clauses.join(" and ")}`
        }
        const [countResults] = await conn.query<RowDataPacket[]>(countSql, params);
        const totalItems = countResults[0].count;
        const totalPages = Math.ceil(totalItems / page.size);

        // select transactions with pagination
        let sql = `select
            id,
            member_id,
            type,
            amount,
            status,
            transaction_ref,
            payment_req_id,
            payment_id,
            package_id,
            event_id,
            remark,
            created_by,
            updated_by,
            created_at,
            updated_at
            from credit03transaction`;
        if (clauses.length !== 0) {
            sql += ` where ${clauses.join(" and ")}`
        }
        sql += " order by created_at desc";
        sql += " limit ? offset ?";

        const offset = page.toZeroBasedOffset();
        params.push(page.size, offset);

        const [results] = await conn.query<RowDataPacket[]>(sql, params);
        return {
            totalItems,
            totalPages,
            items: results.map(o => CreditRepo.toTransaction(o))
        } as PageOfData<Transaction>;
    }

    /////////////////////////////
    // Cleanup Pending Payment //
    /////////////////////////////
    async cleanup(conn: Connection): Promise<number> {
        const sql = `update credit03transaction
            set deleted = true,
                deleted_at = now(),
                deleted_by = ?,
                updated_by = ?
            where status = ?
            and now() > timestampadd(minute, 15, created_at)
            and deleted is false`;

        const by = "CleanupScheduler";
        logger.debug(`[${by}] cleanup()`);
        const params = [
            by,
            by,
            TransactionStatus.PendingPayment
        ];

        const [results] = await conn.execute<ResultSetHeader>(sql, params);
        const affectedRows = results.affectedRows;
        logger.info(`[${by}] cleanup ${affectedRows} pending payment older than 15 minutes`);
        return affectedRows;
    }

    async getPackagesByIds(conn: Connection, ids: string[]): Promise<Package[]> {
        if (ids.length === 0) return [];
        const qMarks = Array(ids.length).fill("?").join(",");
        const sql = `select * from credit01package where id in (${qMarks})`;
        const [results] = await conn.query<RowDataPacket[]>(sql, ids);
        return results.map(o => CreditRepo.toPackage(o));
    }

    // Check if member has any previous topups
    async hasMemberPreviousTopups(conn: Connection, memberId: string): Promise<boolean> {
        const sql = `SELECT COUNT(*) as count FROM credit03transaction
                    WHERE member_id = ? AND type = ? AND status = ?`;
        const params = [memberId, TransactionType.Topup, TransactionStatus.ConfirmedPayment];

        const [results] = await conn.query<RowDataPacket[]>(sql, params);
        return results[0]['count'] > 0;
    }

    // Get member's purchase count for a specific package
    async getMemberPackagePurchaseCount(conn: Connection, memberId: string, packageId: string): Promise<number> {
        const sql = `SELECT purchase_count FROM credit04package_purchase
                    WHERE member_id = ? AND package_id = ?`;
        const params = [memberId, packageId];

        const [results] = await conn.query<RowDataPacket[]>(sql, params);
        return results.length > 0 ? results[0]['purchase_count'] : 0;
    }

    async getPurchaseCountsForPackages(conn: Connection, memberId: string, packageIds: string[]): Promise<Map<string, number>> {
        if (packageIds.length === 0) {
            return new Map();
        }

        const placeholders = packageIds.map(() => '?').join(',');
        const sql = `SELECT package_id, purchase_count FROM credit04package_purchase
                    WHERE member_id = ? AND package_id IN (${placeholders})`;
        const params = [memberId, ...packageIds];

        const [results] = await conn.query<RowDataPacket[]>(sql, params);

        // Create a map of package_id to purchase_count
        const purchaseCounts = new Map<string, number>();
        results.forEach(row => {
            purchaseCounts.set(row['package_id'], row['purchase_count']);
        });

        return purchaseCounts;
    }

    // Increment package purchase count
    async incrementPackagePurchaseCount(conn: Connection, memberId: string, packageId: string, by: string): Promise<void> {
        const sql = `INSERT INTO credit04package_purchase
                    (id, member_id, package_id, purchase_count, created_by, updated_by)
                    VALUES (UUID(), ?, ?, 1, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    purchase_count = purchase_count + 1,
                    updated_by = ?,
                    updated_at = NOW()`;
        const params = [memberId, packageId, by, by, by];

        await conn.execute(sql, params);
        logger.info(`[${by}] Upserted package purchase tracking for member=${memberId}, package=${packageId}`);
    }
}