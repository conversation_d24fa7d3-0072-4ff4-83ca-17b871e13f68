import { Connection, ResultSetHeader } from "mysql2/promise";
import { PinIgloohomeLog } from '../models/pin-log';

export class PinLogRepo {
  async insertPinLog(conn: Connection, log: PinIgloohomeLog): Promise<number> {
    const sql = `
      INSERT INTO pin01igloohome_log
        (booking_id, member_id, job_id, pin, access_name, start_iso, end_iso, api_response, error, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      log.bookingId,
      log.memberId,
      log.jobId || null,
      log.pin || null,
      log.accessName || null,
      log.startIso || null,
      log.endIso || null,
      log.apiResponse || null,
      log.error || null,
      log.createdBy,
    ];
    const [result] = await conn.execute<ResultSetHeader>(sql, params);
    return result.affectedRows;
  }
}