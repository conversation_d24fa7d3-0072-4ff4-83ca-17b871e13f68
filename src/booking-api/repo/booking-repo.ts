import { Page } from "@shared/services";
import { Event, EventCancelBy } from "@studio-cms/models/event";
import { EventRepo } from "@studio-cms/repo/event-repo";
import { EventSupportRepo } from "@studio-cms/repo/event-support-repo";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import {
  BookingCancel,
  BookingCancelFindCriteria,
  BookingCancelReq,
  BookingDoorPinSaveReq,
  BookingFeedbackSaveReq,
  BookingFindCriteria,
  BookingNoFeedbackSaveReq,
  BookingStatus,
  BookingStatusSaveReq,
  BookingType,
  ScheduleFindCriteria,
  SessionFindCriteria
} from "../models/booking";

export class BookingRepo {

  private eventSupportRepo = new EventSupportRepo();

  async updateBookingStatus(
    conn: Connection, req: BookingStatusSaveReq,
  ): Promise<number> {
    const sql = `update evt01events set
      booking_status = ?,
      updated_by = ?
      where payment_req_id = ? and booking_status != ?`;
    const by = req.by;
    const bookingStatus = BookingStatus[req.bookingStatus];
    logger.debug(`[${by}] updateBookingStatus() req=${JSON.stringify(req)}`)
    const params = [
      bookingStatus,
      by,
      req.paymentReqId,
      bookingStatus,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updateBookingStatus() payReqId=${req.paymentReqId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async findBookings(
    conn: Connection,
    criteria: BookingFindCriteria,
    page?: Page,
  ): Promise<Event[]> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    {
      clauses.push("(member_id = ?)");
      params.push(criteria.memberId);

      clauses.push("(type = ?)");
      params.push(BookingType[BookingType.Booking]);

      clauses.push("(deleted is false)");
    }
    if(criteria.upcomingAndOngoing) {
      clauses.push("((CONVERT_TZ(end_at, \`timezone\`, 'UTC') >= now()) and (booking_status = ?))");
      params.push(BookingStatus[BookingStatus.Confirmed]);
    }
    if (criteria.upcomingOnly) {
      clauses.push("((CONVERT_TZ(start_at, \`timezone\`, 'UTC') >= now()) and (booking_status = ?))");
      params.push(BookingStatus[BookingStatus.Confirmed]);
    }
    if (criteria.ongoingOnly) {
      clauses.push("((CONVERT_TZ(start_at, \`timezone\`, 'UTC') <= now()) and (CONVERT_TZ(end_at, \`timezone\`, 'UTC') >= now()) and (booking_status = ?))");
      params.push(BookingStatus[BookingStatus.Confirmed]);
    }
    if (criteria.pastOnly) {
      clauses.push("((CONVERT_TZ(end_at, \`timezone\`, 'UTC') < now()))");
    }
    if (criteria.cancelledOnly) {
      clauses.push("(cancelled is true OR EXISTS (select event_id from evt03cancel_req c where c.event_id = e.id and c.approved is false and c.deleted is false))");
    } else {
      const placeholders = [EventCancelBy[EventCancelBy.Instructor], EventCancelBy[EventCancelBy.Studio]].map(() => '?').join(',');
      params.push(...[EventCancelBy[EventCancelBy.Instructor], EventCancelBy[EventCancelBy.Studio]]);
      clauses.push(`(cancelled is false AND NOT EXISTS (select event_id from evt03cancel_req c where c.event_id = e.id and c.deleted is false and c.req_by IN (${placeholders})))`);
    }
    if (criteria.feedbackOnly) {
      clauses.push(`(id in (
        select event_id from evt02sessions
        where member_id = ?
        and m_has_feedback is false
      ) and CONVERT_TZ(start_at, \`timezone\`, 'UTC') < now())`);
      params.push(criteria.memberId);
    }

    // select items
    let selectSql = "select e.* from evt01events e";
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }
    if (criteria.upcomingOnly || criteria.upcomingAndOngoing) {
      // sort by start asc for upcomingOnly
      selectSql += " order by start_date asc, start_time asc";
    } else {
      selectSql += " order by start_date desc, start_time desc";
    }
    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    logger.info(`debug bookings api - ${conn.format(selectSql, params)}`);
    const [results1] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = [];
    for (const result of results1) {
      const ret = EventRepo.toEvent(result);
      await this.addAssociatedItems(conn, ret);
      items.push(ret);
    }
    return items;
  }

  async findBookingsByIds(conn: Connection, ids: string[]): Promise<Event[]> {
    if (ids.length === 0) return [];

    const placeholders = ids.map(() => '?').join(',');
    const sql = `select * from evt01events where id in (${placeholders}) and type = ? and deleted is false`;
    const params = [...ids, BookingType[BookingType.Booking]];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    const items = [];
    for (const result of results) {
      const ret = EventRepo.toEvent(result);
      await this.addAssociatedItems(conn, ret);
      items.push(ret);
    }
    return items;
  }

  async findBookingById(conn: Connection, id: string): Promise<Event|undefined> {
    const sql =
      "select * from evt01events where id = ? and type = ? and deleted is false";
    const params = [
      id,
      BookingType[BookingType.Booking],
    ];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    const ret = EventRepo.toEvent(results[0]);
    await this.addAssociatedItems(conn, ret);
    return ret;
  }

  async findByPaymentReqId(
    conn: Connection, paymentReqId: string,
  ): Promise<Event|undefined> {
    const sql = "select * from evt01events where payment_req_id = ?";
    const params = [paymentReqId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return EventRepo.toEvent(results[0]);
  }

  async findSessions(
    conn: Connection,
    criteria: SessionFindCriteria,
    page?: Page,
  ): Promise<Event[]> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    {
      clauses.push("(instr_id = ?)");
      params.push(criteria.instructorId);

      clauses.push("(type = ?)");
      params.push(BookingType[BookingType.Booking]);

      clauses.push("(deleted is false)");
    }
    if(criteria.upcomingAndOngoing) {
      clauses.push("((CONVERT_TZ(end_at, \`timezone\`, 'UTC') >= now()) and (booking_status = ?))");
      params.push(BookingStatus[BookingStatus.Confirmed]);
    }
    if (criteria.upcomingOnly) {
      clauses.push("((CONVERT_TZ(start_at, \`timezone\`, 'UTC') >= now()) and (booking_status = ?))");
      params.push(BookingStatus[BookingStatus.Confirmed]);
    }
    if (criteria.ongoingOnly) {
      clauses.push("((CONVERT_TZ(start_at, \`timezone\`, 'UTC') <= now()) and (CONVERT_TZ(end_at, \`timezone\`, 'UTC') >= now()) and (booking_status = ?))");
      params.push(BookingStatus[BookingStatus.Confirmed]);
    }
    if (criteria.pastOnly) {
      clauses.push("((CONVERT_TZ(end_at, \`timezone\`, 'UTC') < now()))");
    }

    if(criteria.pastBookingId) {
      clauses.push(`(exists (
        select sub_e.member_id from (select member_id, start_at from evt01events where id = ?) as sub_e where sub_e.member_id = e.member_id and sub_e.start_at > e.start_at
      ) and (cancelled is false) and (exists (select event_id from evt02sessions s where s.event_id = e.id and s.completed is true)))`);
      params.push(criteria.pastBookingId);
    }

    if (criteria.cancelledOnly) {
      clauses.push("(cancelled is true OR EXISTS (select event_id from evt03cancel_req c where c.event_id = e.id and c.approved is false and c.deleted is false))");
    } else {
      const placeholders = [EventCancelBy[EventCancelBy.Instructor], EventCancelBy[EventCancelBy.Studio]].map(() => '?').join(',');
      params.push(...[EventCancelBy[EventCancelBy.Instructor], EventCancelBy[EventCancelBy.Studio]]);
      clauses.push(`(cancelled is false AND NOT EXISTS (select event_id from evt03cancel_req c where c.event_id = e.id and c.deleted is false and c.req_by IN (${placeholders})))`);
    }

    // select items
    let selectSql = "select * from evt01events e";
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }
    if (criteria.upcomingOnly || criteria.upcomingAndOngoing) {
      // sort by start asc for upcomingOnly
      selectSql += " order by start_date asc, start_time asc";
    } else {
      selectSql += " order by start_date desc, start_time desc";
    }
    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    logger.info(`debug bookings api - ${conn.format(selectSql, params)}`);
    const [results1] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = [];
    for (const result of results1) {
      const ret = EventRepo.toEvent(result);
      await this.addAssociatedItems(conn, ret);
      items.push(ret);
    }
    return items;
  }

  private async addAssociatedItems(conn: Connection, event: Event) {
    if (event.studioId) {
      event.studio = await this.eventSupportRepo.findEventStudioById(conn, event.studioId);
    }
    if (event.equipmentId) {
      event.equipment = await this.eventSupportRepo.findEventEquipmentById(conn, event.equipmentId);
    }
    if (event.instructorId) {
      event.instructor = await this.eventSupportRepo.findEventInstructorById(conn, event.instructorId);
    }
    if (event.memberId) {
      event.member = await this.eventSupportRepo.findEventMemberById(conn, event.memberId);
    }
  }

  /* unused
  async saveBookingState(
    conn: Connection, req: BookingNotesSaveReq,
  ): Promise<number> {
    const sql = "update evt02sessions set state = ?, updated_by = ? where event_id = ? and member_id = ?";
    const by = req.by.username;
    logger.debug(`[${by}] saveBookingState() req=${JSON.stringify(req)}`)
    const params = [
      req.value!,
      by,
      req.bookingId!,
      req.memberId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] saveBookingState() bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async saveBookingTarget(
    conn: Connection, req: BookingNotesSaveReq,
  ): Promise<number> {
    const sql = "update evt02sessions set target = ?, updated_by = ? where event_id = ? and member_id = ?";
    const by = req.by.username;
    logger.debug(`[${by}] saveBookingTarget() req=${JSON.stringify(req)}`)
    const params = [
      req.value!,
      by,
      req.bookingId!,
      req.memberId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] saveBookingTarget() bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }
  */

  async saveFeedback(
    conn: Connection, req: BookingFeedbackSaveReq,
  ): Promise<number> {
    const sql = `update evt02sessions
      set m_has_feedback = ?,
          m_feedback = ?,
          m_instructor_feedback = ?,
          m_studio_feedback = ?,
          m_instructor_rating = ?,
          m_studio_rating = ?,
          m_feedback_at = now(),
          is_private = ?,
          updated_by = ?
      where event_id = ? and member_id = ?`;

    const by = req.by.username;
    logger.debug(`[${by}] saveFeedback() req=${JSON.stringify(req)}`);

    const params = [
      true, // m_has_feedback
      req.feedback! ?? '',
      req.instructorFeedback! ?? '',
      req.studioFeedback! ?? '',
      +req.instructorRating!,
      +req.studioRating!,
      req.isPrivate,
      by,   // updated_by
      req.eventId!,
      req.memberId!,
    ];

    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] saveFeedback() eventId=${req.eventId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async noFeedback(
    conn: Connection, req: BookingNoFeedbackSaveReq,
  ): Promise<number> {
    const sql = `update evt02sessions
      set m_has_feedback = ?, m_no_feedback = ?,
      updated_by = ?
      where event_id = ? and member_id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] noFeedback() req=${JSON.stringify(req)}`)
    const params = [
      true, // m_has_feedback
      true, // m_no_feedback
      by,   // updated_by
      req.eventId!,
      req.memberId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] noFeedback() eventId=${req.eventId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  //////////////
  // Schedule //
  //////////////

  async schedule(
    conn: Connection, criteria: ScheduleFindCriteria,
  ): Promise<Event[]> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    if (criteria.userId) {
      clauses.push("(instr_id = ? or member_id = ?)");
      params.push(criteria.userId);
      params.push(criteria.userId);
    }
    {
      clauses.push(
        "(type = ? and booking_status = ? and cancelled is false)"
      );
      params.push(BookingType[BookingType.Booking]);
      params.push(BookingStatus[BookingStatus.Confirmed]);
    }
    if (criteria.month) {
      clauses.push(`(
          date_format(start_date, '%Y-%m') = ?
          or
          date_format(end_date, '%Y-%m') = ?
        )`);
      params.push(criteria.month);
      params.push(criteria.month);
    }
    if (criteria.day) {
      clauses.push(`(start_date = ? or end_date = ?)`);
      params.push(criteria.day);
      params.push(criteria.day);
    }
    clauses.push("(deleted is false)");

    // select items
    let selectSql = "select * from evt01events";
    selectSql += ` where ${clauses.join(" and ")}`
    selectSql += ` order by start_date, start_time, end_date, end_time`;
    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = [];
    for (const result of results) {
      const ret = EventRepo.toEvent(result);
      await this.addAssociatedItems(conn, ret);
      items.push(ret);
    }
    return items;
  }

  ////////////
  // Cancel //
  ////////////

  async findCancelReq(
    conn: Connection, criteria: BookingCancelFindCriteria,
  ): Promise<BookingCancel|undefined> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    if (criteria.token) {
      clauses.push("(id = ?)");
      params.push(criteria.token);
    }
    if (criteria.reqBy) {
      clauses.push("(req_by = ?)");
      params.push(EventCancelBy[criteria.reqBy]);
    }
    if (criteria.userId) {
      clauses.push("(user_id = ?)");
      params.push(criteria.userId);
    }
    if (criteria.eventId) {
      clauses.push("(event_id = ?)");
      params.push(criteria.eventId);
    }
    if (criteria.approved !== undefined) {
      clauses.push("(approved = ?)");
      params.push(criteria.approved);
    }
    if (criteria.isValidRange) {
      clauses.push("(TIMESTAMPDIFF(SECOND, at, NOW()) <= 135)"); // 2mins + 15secs buffer
    }

    clauses.push("(deleted is false)");

    // select items
    let selectSql = "select * from evt03cancel_req";
    selectSql += ` where ${clauses.join(" and ")}`;
    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    return results.length === 0 ? undefined : this.toBookingCancel(results[0]);
  }

  async findCancelReqsByBookingIds(
    conn: Connection, eventIds: string[], approved?: boolean|undefined, reqBys?: string[]|undefined,
  ): Promise<BookingCancel[]> {
    if (eventIds.length === 0) return [];

    const placeholders = eventIds.map(() => '?').join(',');
    const clauses = [];
    const params = [];
    if(eventIds.length) {
      clauses.push(`(event_id in (${placeholders}))`);
      params.push(...eventIds);
    }
    {
      clauses.push("(deleted is false)");
    }
    if(reqBys) {
      let reqByPlaceholders = reqBys.map(() => '?').join(',');
      clauses.push(`(req_by in (${reqByPlaceholders}))`);
      params.push(...reqBys);
    }
    if (approved !== undefined) {
      clauses.push("(approved = ?)");
      params.push(approved);
    }
    let selectSql = `select * from evt03cancel_req`;

    selectSql += ` where ${clauses.join(" and ")}`;
    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    return results.map(row => this.toBookingCancel(row));
  }

  private toBookingCancel(row: any): BookingCancel {
    return {
      reqBy: row['req_by'],
      userId: row['user_id']!,
      eventId: row['event_id']!,
      reason: row['reason'] ?? undefined,
      approved: row['approved'] ?? false,
      free: row['free']!,
      at: row['at']!,
    } as BookingCancel;
  }

  async createCancelReq(
    conn: Connection, req: BookingCancelReq,
  ): Promise<string> {
    const sql = `insert into evt03cancel_req (
        id, req_by, user_id, event_id, at, free, reason, approved, approved_at, approved_by, created_by
      ) values (
        ?, ?, ?, ?, now(), ?, ?, ?, CASE WHEN ? = 1 THEN now() ELSE NULL END, ?, ?
      )`;
    const by = req.by.username;
    const uuid = crypto.randomUUID();
    logger.debug(`[${by}] createCancelReq() req=${JSON.stringify(req)}`)
    const params = [
      uuid,
      EventCancelBy[req.reqBy], // cancelled_by (Studio, Instructor, Member)
      req.userId!,
      req.eventId!,
      !!req.free,
      req.reason! ?? null,
      req.approved!,
      req.approved! ? 1: 0,
      req.approved! ? by: null,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] createCancelReq() uuid=${uuid}`);
    return uuid;
  }

  //////////////////
  // Release Hold //
  //////////////////

  async releaseHold(conn: Connection): Promise<number> {
    const sql = `update evt01events
      set deleted = ?, deleted_at = now(), deleted_by = ?,
      updated_by = ?
      where (booking_status is not null and booking_status = ?)
      and (hold_ends is not null)
      and (now() > timestampadd(second, ?, hold_ends))
      and deleted is false`;
    const by = "release-hold";
    const params = [
      true, // deleted
      by,   // deleted_by
      by,   // updated_by
      BookingStatus[BookingStatus.PendingPayment],
      30,   // buffer seconds
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    if (affectedRows !== 0) {
      logger.info(`[${by}] releaseHold() affectedRows=${affectedRows}`);
    }
    return affectedRows;
  }

  ///////////////
  // Door Pin  //
  ///////////////
  async saveDoorPin(conn: Connection, req: BookingDoorPinSaveReq): Promise<number> {
    const sql = `update evt02sessions set door_pin = ?, updated_by = ? where event_id = ? and member_id = ?`;
    const by = req.by.username;
    const params = [req.doorPin!, by, req.eventId!, req.memberId!];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] saveDoorPin() eventId=${req.eventId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async countBookingsAndSessions(
    conn: Connection,
    criteria: BookingFindCriteria,
  ): Promise<{ bookings: number; sessions: number }> {
    const buildCountQuery = (idField: string) => {
      const clauses = [];
      const params = [];

      // Base conditions
      clauses.push(`(${idField} = ?)`);
      params.push(criteria.memberId);

      clauses.push("(type = ?)");
      params.push(BookingType[BookingType.Booking]);

      clauses.push("(deleted is false)");

      // Additional filters
      if(criteria.upcomingAndOngoing) {
        clauses.push("((CONVERT_TZ(end_at, \`timezone\`, 'UTC') >= now()) and (booking_status = ?))");
        params.push(BookingStatus[BookingStatus.Confirmed]);
      }
      if (criteria.upcomingOnly) {
        clauses.push("((CONVERT_TZ(start_at, \`timezone\`, 'UTC') >= now()) and (booking_status = ?))");
        params.push(BookingStatus[BookingStatus.Confirmed]);
      }
      if (criteria.ongoingOnly) {
        clauses.push("((CONVERT_TZ(start_at, \`timezone\`, 'UTC') <= now()) and (CONVERT_TZ(end_at, \`timezone\`, 'UTC') >= now()) and (booking_status = ?))");
        params.push(BookingStatus[BookingStatus.Confirmed]);
      }
      if (criteria.pastOnly) {
        clauses.push("((CONVERT_TZ(end_at, \`timezone\`, 'UTC') < now()))");
      }
      if (criteria.cancelledOnly) {
        clauses.push("(cancelled is true or exists (select event_id from evt03cancel_req c where c.event_id = e.id and c.deleted is false and c.approved is false))");
      } else {
        const placeholders = [EventCancelBy[EventCancelBy.Instructor], EventCancelBy[EventCancelBy.Studio]].map(() => '?').join(',');
        params.push(...[EventCancelBy[EventCancelBy.Instructor], EventCancelBy[EventCancelBy.Studio]]);
        clauses.push(`(cancelled is false and not exists (select event_id from evt03cancel_req c where c.event_id = e.id and c.deleted is false and c.req_by IN (${placeholders})))`);
      }
      if (criteria.feedbackOnly && idField === 'member_id') {
        clauses.push(`(id in (
          select event_id from evt02sessions
          where member_id = ?
          and m_has_feedback is false
        ) and CONVERT_TZ(start_at, \`timezone\`, 'UTC') < now())`);
        params.push(criteria.memberId);
      }

      return {
        sql: `select count(*) as count from evt01events e where ${clauses.join(" and ")}`,
        params
      };
    };

    // Get counts for both bookings and sessions
    const bookingQuery = buildCountQuery('member_id');
    const sessionQuery = buildCountQuery('instr_id');
    const [bookingResults] = await conn.query<RowDataPacket[]>(bookingQuery.sql, bookingQuery.params);
    const [sessionResults] = await conn.query<RowDataPacket[]>(sessionQuery.sql, sessionQuery.params);

    return {
      bookings: bookingResults[0].count,
      sessions: sessionResults[0].count
    };
  }
}
