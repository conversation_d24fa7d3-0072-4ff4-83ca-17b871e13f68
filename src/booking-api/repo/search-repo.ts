import { BookingStatus, BookingType } from "@booking-api/models/booking";
import {
  DayType,
  InstructorMobility,
  LatLng,
  SearchAt,
  SearchCriteria,
  SearchEquipment,
  SearchEquipmentType,
  SearchInstructor,
  SearchStudio
} from "@booking-api/models/search";
import { SearchService } from "@booking-api/services/search-service";
import { toMySQLDate } from "@shared/date-time";
import { EquipmentType } from "@studio-cms/models/equipment";
import { parse } from "date-fns";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import logger from "@utils/logger";
import { Session } from "@booking-api/models/session";
import { SessionRepo } from "./session-repo";

export class SearchRepo {

  /////////////
  // Studios //
  /////////////

  /* unused
  async findAllStudioIds(
    conn: Connection,
  ): Promise<string[]> {
    const sql = `select * from pub01studios where deleted is false`;
    const [results] = await conn.query<RowDataPacket[]>(sql);
    return results.map(o => o['id']!);
  }
  */

  async findBlockedStudioIds(
    conn: Connection,
    criteria: SearchCriteria,
    studioIdsIn: string[],
  ): Promise<string[]> {
    if (studioIdsIn.length === 0) {
      return []; // nothing else to do
    }

    const clauses = [];
    const params = [];
    if (studioIdsIn) {
      const qMarks = Array(studioIdsIn.length).fill("?").join(",")
      clauses.push(`(studio_id in (${qMarks}))`);
      params.push(...studioIdsIn);
    }
    { // type
      clauses.push(`(type = ?)`);
      params.push(BookingType[BookingType.StudioBlocked]);
    }
    { // date range
      // a) full day
      //    `start_date` --- slot_date --- `end_date`
      // b) not full day
      //    (`start_at` < slot_end) and (`end_at` > slot_start)
      clauses.push(`((
          (full_day is true) and
          (start_date <= ?) and (end_date >= ?)
        ) or (
          (full_day is not true) and
          (start_at < ?) and (end_at > ?)
        ))`);
      params.push(...[
        criteria.at.date,    // (!) slot_date
        criteria.at.date,    // (!) slot_date
        //
        criteria.at.endAt,   // (!) slot_end
        criteria.at.startAt, // (!) slot_start
      ]);
    }
    { // cancelled, deleted
      clauses.push("(cancelled is false)");
      clauses.push("(deleted is false)");
    }

    let sql = `select studio_id from evt01events`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /*
    logger.debug(`findBlockedStudioIds() sql=${sql}`);
    logger.debug(`findBlockedStudioIds() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => o['studio_id']!);
  }

  async findStudioIdsWithinDistance(
    conn: Connection,
    criteria: SearchCriteria,
    studioIdsIn: string[],
  ): Promise<string[]> {
    if (studioIdsIn.length === 0) {
      return []; // nothing else to do
    }

    const clauses = [];
    const params = [];
    if (studioIdsIn) {
      const qMarks = Array(studioIdsIn.length).fill("?").join(",")
      clauses.push(`(id in (${qMarks}))`);
      params.push(...studioIdsIn);
    }
    { // distance within
      clauses.push(`(latlng in (
          select latlng_to from app03dist_cache
          where latlng_from = ? and distance <= ?
        ))`);
      params.push(...[
        criteria.latLng.toCacheString(),
        criteria.distance,
      ]);
    }
    { // active
      clauses.push("(active is true)");
    }
    { // deleted
      clauses.push("(deleted is false)");
    }

    let sql = `select * from pub01studios`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /*
    logger.debug(`findStudioIdsWithinDistance() sql=${sql}`);
    logger.debug(`findStudioIdsWithinDistance() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => o['id']!);
  }

  // studioIdsIn
  // faveStudioOnly (and !!byId)
  // instrless
  // equipTypes
  // deleted
  async findMatchedStudioIds(
    conn: Connection,
    criteria: SearchCriteria,
    studioIdsIn: string[]|undefined,
  ): Promise<string[]> {
    if (studioIdsIn?.length === 0) {
      return []; // nothing else to do
    }

    const clauses = [];
    const params = [];
    if (studioIdsIn) {
      const qMarks = Array(studioIdsIn.length).fill("?").join(",")
      clauses.push(`(id in (${qMarks}))`);
      params.push(...studioIdsIn);
    }
    if (criteria.faveStudioOnly && criteria.byId) {
      clauses.push(`(id in (
          select studio_id from usr02fave_studios
          where user_id = ?
        ))`);
      params.push(criteria.byId);
    }

    if (criteria.preferredStudioFromFaveInstrOnly && criteria.byId) {
      clauses.push(`(id in (
          select studio_id from usr03fave_instrs fi INNER JOIN instr05pref_studios ps ON fi.instr_id = ps.user_id where fi.user_id = ?
        ))`);
      params.push(criteria.byId);
    }

    if (criteria.instrLessOnly) {
      clauses.push("(instrless is true)");
    }
    if (criteria.equipTypes && criteria.equipTypes.length > 0) {
      const qMarks = Array(criteria.equipTypes.length).fill("?").join(",");
      clauses.push(`(id in (
          select studio_id from vw_stu04equipment
          where type_id in (${qMarks}) and deleted is false
        ))`);
      params.push(...criteria.equipTypes);
    }
    { // active
      clauses.push("(active is true)");
    }
    { // deleted
      clauses.push("(deleted is false)");
    }

    let sql = `select * from pub01studios`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /*
    logger.debug(`findMatchedStudioIds() sql=${sql}`);
    logger.debug(`findMatchedStudioIds() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => o['id']!);
  }

  async findStudiosByEquipmentIds(
    conn: Connection,
    criteria: SearchCriteria,
    equipIdsIn: string[],
  ): Promise<SearchStudio[]> {
    if (equipIdsIn.length === 0) {
      return []; // nothing else to do
    }

    const date = parse(criteria.at.date, "yyyy-MM-dd", new Date());
    const qMarks = Array(equipIdsIn.length).fill("?").join(",")
    const sql = `select * from pub01studios where id in (
      select studio_id from stu04equipment
      where id in (${qMarks})
      and (
        (start_date is null or start_date <= ?)
        and (end_date is null or end_date >= ?)
      )
      and deleted is false
    ) and active is true and deleted is false`;
    const params = [];
    params.push(...equipIdsIn);
    params.push(toMySQLDate(date)); // start_date
    params.push(toMySQLDate(date)); // end_date

    /*
    logger.debug(`findStudiosByEquipmentIds() sql=${sql}`);
    logger.debug(`findStudiosByEquipmentIds() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => this.toSearchStudio(o));
  }

  private toSearchStudio(row: any): SearchStudio {
    return {
      id: row['id'],
      name: row['name'],
      address: row['address'],
      pictures: JSON.parse(row['pictures'] ?? '[]'),
      usagePolicies: row['usage'],
      instrLess: row['instrless'] === 1,
      latLng: row['lat'] && row['lng']? new LatLng(
        +row['lat'],
        +row['lng'],
      ): null
    } as SearchStudio;
  }

  /////////////////
  // Instructors //
  /////////////////

  /* unused
  async findAllInstructorIds(
    conn: Connection,
  ): Promise<string[]> {
    const sql = `select * from pub02instructors where deleted is false`;
    const [results] = await conn.query<RowDataPacket[]>(sql);
    return results.map(o => o['id']!);
  }
  */

  async findBookedInstructorIds(
    conn: Connection,
    criteria: SearchCriteria,
    instrIdsIn: string[],
  ): Promise<string[]> {
    if (instrIdsIn.length === 0) {
      return []; // nothing else to do
    }

    const clauses = [];
    const params = [];
    if (instrIdsIn) {
      const qMarks = Array(instrIdsIn.length).fill("?").join(",")
      clauses.push(`(instr_id in (${qMarks}))`);
      params.push(...instrIdsIn);
    }
    { // type
      clauses.push(`(type = ?)`);
      params.push(BookingType[BookingType.Booking]);
    }
    { // status
      clauses.push(`(booking_status is not null and booking_status in (?, ?))`);
      params.push(BookingStatus[BookingStatus.PendingPayment]);
      params.push(BookingStatus[BookingStatus.Confirmed]);
    }
    { // date range
      // a) full day
      //    `start_date` --- slot_date --- `end_date`
      // b) not full day
      //    (`start_at` < slot_end) and (`end_at` > slot_start)
      clauses.push(`((
          (full_day is true) and
          (start_date <= ?) and (end_date >= ?)
        ) or (
          (full_day is not true) and
          (start_at < ?) and (end_at > ?)
        ))`);
      params.push(...[
        criteria.at.date,    // (!) slot_date
        criteria.at.date,    // (!) slot_date
        //
        criteria.at.endAt,   // (!) slot_end
        criteria.at.startAt, // (!) slot_start
      ]);
    }
    { // cancelled, deleted
      clauses.push("(cancelled is false)");
      clauses.push("(deleted is false)");
    }

    let sql = `select instr_id from evt01events`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /*
    logger.debug(`findBookedInstructorIds() sql=${sql}`);
    logger.debug(`findBookedInstructorIds() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => o['instr_id']!);
  }

  // Blackouts
  async findBlockedInstructorIds(
    conn: Connection,
    criteria: SearchCriteria,
    instrIdsIn: string[],
  ): Promise<string[]> {
    if (instrIdsIn.length === 0) {
      return []; // nothing else to do
    }

    const clauses = [];
    const params = [];
    if (instrIdsIn) {
      const qMarks = Array(instrIdsIn.length).fill("?").join(",")
      clauses.push(`(instr_id in (${qMarks}))`);
      params.push(...instrIdsIn);
    }
    { // date range
      // a) full day
      //    `start_date` --- slot_date --- `end_date`
      // b) not full day
      //    (`start_at` < slot_end) and (`end_at` > slot_start)
      clauses.push(`((
          (full_day is true) and
          (start_date <= ?) and (end_date >= ?)
        ) or (
          (full_day is not true) and
          (start_at < ?) and (end_at > ?)
        ))`);
      params.push(...[
        criteria.at.date,    // (!) slot_date
        criteria.at.date,    // (!) slot_date
        //
        criteria.at.endAt,   // (!) slot_end
        criteria.at.startAt, // (!) slot_start
      ]);
    }
    { // deleted
      clauses.push("(deleted is false)");
    }

    let sql = `select instr_id from instr07blackouts`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /*
    logger.debug(`findBlockedInstructorIds() sql=${sql}`);
    logger.debug(`findBlockedInstructorIds() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => o['instr_id']!);
  }

  // Instructors working on slot
  async findWorkHoursInstructorIds(
    conn: Connection,
    criteria: SearchCriteria,
    instrIdsIn: string[]|undefined,
  ): Promise<string[]> {
    if (instrIdsIn?.length === 0) {
      return []; // nothing else to do
    }

    const clauses = [];
    const params = [];
    if (instrIdsIn) {
      const qMarks = Array(instrIdsIn.length).fill("?").join(",")
      clauses.push(`(instr_id in (${qMarks}))`);
      params.push(...instrIdsIn);
    }
    if (criteria.faveInstrOnly && criteria.byId) {
      clauses.push(`(instr_id in (
          select instr_id from usr03fave_instrs
          where user_id = ?
        ))`);
      params.push(criteria.byId);
    }
    { // dayTypes
      clauses.push(`(type in (?, ?))`);
      params.push(...[
        DayType[DayType.ALL],
        DayType[criteria.dayType!],
      ]);
    }
    { // start, end
      // (`start` <= timeStart) and (`end` >= timeEnd)
      clauses.push(`((start <= ?) and (end >= ?))`);
      params.push(...[
        criteria.at.timeStart,
        criteria.at.timeEnd == '00:00' ? '24:00' : criteria.at.timeEnd,
      ]);
    }
    { // exclude deleted instructors
      clauses.push(`(instr_id not in (
        select id from instr01instructors where deleted is true
      ))`);
    }
    if (criteria.equipTypes && criteria.equipTypes.length > 0) {
      const qMarks = Array(criteria.equipTypes.length).fill("?").join(",");
      clauses.push(`(exists (
          select i08e.instr_id from instr08equipment i08e
          where i08e.type_id in (${qMarks}) and i08e.instr_id = instr_id
        ))`);
      params.push(...criteria.equipTypes);
    }

    let sql = `select * from instr03work_hours`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /*
    logger.debug(`findWorkHoursInstructorIds() sql=${sql}`);
    logger.debug(`findWorkHoursInstructorIds() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => o['instr_id']!);
  }

  // Instructors with rates on slot
  async findRatesInstructorIds(
    conn: Connection,
    criteria: SearchCriteria,
    instrIdsIn: string[]|undefined,
  ): Promise<string[]> {
    if (instrIdsIn?.length === 0) {
      return []; // nothing else to do
    }

    const clauses = [];
    const params = [];
    if (instrIdsIn) {
      const qMarks = Array(instrIdsIn.length).fill("?").join(",")
      clauses.push(`(instr_id in (${qMarks}))`);
      params.push(...instrIdsIn);
    }
    { // dayTypes
      clauses.push(`(type in (?, ?))`);
      params.push(...[
        DayType[DayType.ALL],
        DayType[criteria.dayType!],
      ]);
    }
    { // start, end
      // (`start` <= timeStart) and (`end` >= timeEnd)
      clauses.push(`((start <= ?) and (end >= ?))`);
      params.push(...[
        criteria.at.timeStart,
        criteria.at.timeEnd == '00:00' ? '24:00' : criteria.at.timeEnd,
      ]);
    }

    let sql = `select * from instr02rates`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /*
    logger.debug(`findRatesInstructorIds() sql=${sql}`);
    logger.debug(`findRatesInstructorIds() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => o['instr_id']!);
  }

  async findInstructorMobility(
    conn: Connection,
    criteria: SearchCriteria,
    instrId: string,
  ): Promise<InstructorMobility|undefined> {
    let sql = "select * from instr04mobility where instr_id = ? and dow = ?";
    const params = [instrId, criteria.at.dow];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    if (results.length == 0) {
      return undefined;
    }
    return this.toInstructorMobility(results[0]);
  }

  private toInstructorMobility(row: any): InstructorMobility {
    return {
      dow: row['dow'],
      latLng: new LatLng(+row['lat'], +row['lng']),
      distance: row['distance'],
    } as InstructorMobility;
  }

  async findStudioIdsWithinMobility(
    conn: Connection,
    mobility: InstructorMobility,
    studioIdsIn: string[],
  ): Promise<string[]> {
    if (studioIdsIn.length === 0) {
      return []; // nothing else to do
    }

    const clauses = [];
    const params = [];
    if (studioIdsIn) {
      const qMarks = Array(studioIdsIn.length).fill("?").join(",")
      clauses.push(`(id in (${qMarks}))`);
      params.push(...studioIdsIn);
    }
    { // distance within
      clauses.push(`(latlng in (
          select latlng_to from app03dist_cache
          where latlng_from = ? and distance <= ?
        ))`);
      params.push(...[
        mobility.latLng.toCacheString(),
        mobility.distance,
      ]);
    }
    { // deleted
      clauses.push("(deleted is false)");
    }

    let sql = `select * from pub01studios`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /*
    logger.debug(`findStudioIdsWithinMobility() sql=${sql}`);
    logger.debug(`findStudioIdsWithinMobility() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => o['id']!);
  }

  async findInstructorsByIds(
    conn: Connection,
    instrIds: string[],
  ): Promise<SearchInstructor[]> {
    if (instrIds.length == 0) {
      return []; // no need to continue
    }
    const qMarks = Array(instrIds.length).fill("?").join(",")
    const sql = `select * from pub02instructors
      where id in (${qMarks}) and deleted is false
      and onboarded is true`;
    const params = [...instrIds];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => this.toSearchInstructor(o));
  }

  private toSearchInstructor(row: any): SearchInstructor {
    return {
      id: row['id'],
      name: row['name'],
      descr: row['descr'],
      pictures: JSON.parse(row['pictures'] ?? '[]'),
    } as SearchInstructor;
  }

  ///////////////
  // Equipment //
  ///////////////

  // studioIdsIn
  // equipTypes
  // dayTypes
  // start, end
  async findAvailableEquipmentIds(
    conn: Connection,
    criteria: SearchCriteria,
    studioIdsIn: string[],
    equipIdsIn: string[]|undefined,
  ): Promise<string[]> {
    if (studioIdsIn.length === 0
      || equipIdsIn?.length === 0) {
      return []; // nothing else to do
    }

    const clauses = [];
    const params = [];

    if (equipIdsIn) {
      const qMarks = Array(equipIdsIn.length).fill("?").join(",")
      clauses.push(`(equip_id in (${qMarks}))`);
      params.push(...equipIdsIn);
    }
    if (studioIdsIn) {
      const qMarks = Array(studioIdsIn.length).fill("?").join(",")
      clauses.push(`(studio_id in (${qMarks}))`);
      params.push(...studioIdsIn);
    }
    // filter only selected equipments
    if (criteria.equipTypes && criteria.equipTypes.length > 0) {
      const qMarks = Array(criteria.equipTypes.length).fill("?").join(",");
      const date = parse(criteria.at.date, "yyyy-MM-dd", new Date());
      clauses.push(`(exists (
          select s4e.id from stu04equipment s4e where s4e.type_id in (${qMarks}) and s4e.id = equip_id and (
            (start_date is null or start_date <= ?)
            and (end_date is null or end_date >= ?)
          )
        ))`);
      params.push(...criteria.equipTypes);
      params.push(toMySQLDate(date)); // start_date
      params.push(toMySQLDate(date)); // end_date
    }
    { // dayTypes
      clauses.push(`(type in (?, ?))`);
      params.push(...[
        DayType[DayType.ALL],
        DayType[criteria.dayType!],
      ]);
    }
    { // start, end
      // (`start` <= timeStart) and (`end` >= timeEnd)
      clauses.push(`((start <= ?) and (end >= ?))`);
      params.push(...[
        criteria.at.timeStart,
        criteria.at.timeEnd == '00:00' ? '24:00' : criteria.at.timeEnd,
      ]);
    }

    let sql = `select equip_id from vw_stu05equip_avail`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /*
    logger.debug(`findAvailableEquipmentIds() sql=${sql}`);
    logger.debug(`findAvailableEquipmentIds() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => o['equip_id']!);
  }

  /* unused
  // Equipment with rates on slot
  async findRatesEquipmentIds(
    conn: Connection,
    criteria: SearchCriteria,
    studioIdsIn: string[],
    equipIdsIn: string[]|undefined,
  ): Promise<string[]> {
    if (studioIdsIn.length === 0
      || equipIdsIn?.length === 0) {
      return []; // nothing else to do
    }

    const clauses = [];
    const params = [];
    if (equipIdsIn) {
      const qMarks = Array(equipIdsIn.length).fill("?").join(",")
      clauses.push(`(equip_id in (${qMarks}))`);
      params.push(...equipIdsIn);
    }
    if (studioIdsIn) {
      const qMarks = Array(studioIdsIn.length).fill("?").join(",")
      clauses.push(`(studio_id in (${qMarks}))`);
      params.push(...studioIdsIn);
    }
    { // dayTypes
      clauses.push(`(type in (?, ?))`);
      params.push(...[
        DayType[DayType.ALL],
        DayType[criteria.dayType],
      ]);
    }
    { // start, end
      // (`start` <= timeEnd) and (`end` >= timeStart)
      clauses.push(`((start <= ?) and (end >= ?))`);
      params.push(...[
        criteria.at.timeEnd,
        criteria.at.timeStart,
      ]);
    }

    let sql = `select * from vw_stu05equip_avail`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /-
    logger.debug(`findRatesEquipmentIds() sql=${sql}`);
    logger.debug(`findRatesEquipmentIds() params=${JSON.stringify(params)}`);
    -/
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => o['equip_id']!);
  }
  */

  async findBlockedEquipmentIds(
    conn: Connection,
    criteria: SearchCriteria,
    equipIdsIn: string[],
  ): Promise<string[]> {
    if (equipIdsIn.length === 0) {
      return []; // nothing else to do
    }

    const clauses = [];
    const params = [];
    if (equipIdsIn) {
      const qMarks = Array(equipIdsIn.length).fill("?").join(",")
      clauses.push(`(equip_id in (${qMarks}))`);
      params.push(...equipIdsIn);
    }
    /* not needed, already done in findMatchedStudioIds()
    if (criteria.equipTypes && criteria.equipTypes.length > 0) {
      const qMarks = Array(criteria.equipTypes.length).fill("?").join(",");
      clauses.push(`(equip_id in (
          select id from stu04equipment where type_id in (${qMarks})
        ))`);
      params.push(...criteria.equipTypes);
    }
    */
    { // type
      clauses.push(`(type in (?, ?))`);
      params.push(...[
        BookingType[BookingType.EquipmentBlocked],
        BookingType[BookingType.Booking],
      ]);
    }
    { // date range
      // a) full day
      //    `start_date` --- slot_date --- `end_date`
      // b) not full day
      //    (`start_at` < slot_end) and (`end_at` > slot_start)
      clauses.push(`((
          (full_day is true) and
          (start_date <= ?) and (end_date >= ?)
        ) or (
          (full_day is not true) and
          (start_at < ?) and (end_at > ?)
        ))`);
      params.push(...[
        criteria.at.date,    // (!) slot_date
        criteria.at.date,    // (!) slot_date
        //
        criteria.at.endAt,   // (!) slot_end
        criteria.at.startAt, // (!) slot_start
      ]);
    }
    { // cancelled, deleted
      clauses.push("(cancelled is false)");
      clauses.push("(deleted is false)");
    }

    let sql = `select equip_id from evt01events`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /*
    logger.debug(`findAvailableEquipmentIds() sql=${sql}`);
    logger.debug(`findAvailableEquipmentIds() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => o['equip_id']!);
  }

  async findEquipmentByIds(
    conn: Connection,
    studioId: string,
    equipIds: string[],
    at: SearchAt,
  ): Promise<SearchEquipment[]> {
    if (equipIds.length == 0) {
      return []; // no need to continue
    }
    const params = [];
    const date = parse(at.date, "yyyy-MM-dd", new Date());
    const qMarks = Array(equipIds.length).fill("?").join(",")
    const sql = `select * from vw_stu04equipment
      where id in (${qMarks})
      and (
        (start_date is null or start_date <= ?)
        and (end_date is null or end_date >= ?)
      )
      and studio_id = ? and deleted is false`;
    params.push(...equipIds);
    params.push(toMySQLDate(date)); // start_date
    params.push(toMySQLDate(date)); // end_date
    params.push(studioId);
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => this.toSearchEquipment(o));
  }

  private toSearchEquipment(row: any): SearchEquipment {
    return {
      id: row['id'],
      studioId: row['studio_id'],
      type: {
        id: row['type_id'],
        name: row['type_name'],
      } as SearchEquipmentType,
      code: row['code'],
      privacy: row['privacy'] === 1,
    } as SearchEquipment;
  }

  async findEquipmentTypes(conn: Connection): Promise<EquipmentType[]> {
    const sql = "select * from stu03equip_types where deleted is false order by sort";
    const [results] = await conn.query<RowDataPacket[]>(sql);
    return results.map(o => this.toEquipmentType(o));
  }

  private toEquipmentType(row: any): EquipmentType {
    return {
      id: row['id'],
      name: row['name'],
    } as EquipmentType;
  }

  //////////
  // Fees //
  //////////

  async findEquipmentFee(
    conn: Connection,
    equipId: string,
    dayType: DayType,
    time: string,
  ): Promise<string> {
    // (!) inclusive start, exclusive end
    // Eg, given the following 2 ranges:
    //     09:00 - 12:00
    //     12:00 - 14:00
    // a time of 12:00 will match the second range.
    const sql = `select * from stu05equip_avail
      where equip_id = ? and type in (?, ?) and
      (start <= ? and end > ?)`;
    const params = [
      equipId,
      DayType[dayType],
      DayType[DayType.ALL],
      time,
      time,
    ];

    /*
    logger.debug(`findEquipmentFee() sql=${sql}`);
    logger.debug(`findEquipmentFee() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    if (results.length == 0) {
      throw `Equipment (id=${equipId}) is`
        + ` NOT available on dayType=${DayType[dayType]}, time=${time}`;
    }
    const price = results[0]['price']!;
    return (+price).toFixed(2);
  }

  async findEquipmentFees(
    conn: Connection,
    equipIds: string[],
    dayType: DayType,
    time: string,
  ): Promise<Map<string, string>> {
    if (equipIds.length === 0) {
      return new Map();
    }

    // (!) inclusive start, exclusive end
    // Eg, given the following 2 ranges:
    //     09:00 - 12:00
    //     12:00 - 14:00
    // a time of 12:00 will match the second range.
    const qMarks = Array(equipIds.length).fill("?").join(",");
    const sql = `select equip_id, price from stu05equip_avail
      where equip_id in (${qMarks}) and type in (?, ?) and
      (start <= ? and end > ?)`;
    const params = [
      ...equipIds,
      DayType[dayType],
      DayType[DayType.ALL],
      time,
      time,
    ];

    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    // Create a map of equipment ID to price
    const feeMap = new Map<string, string>();
    results.forEach(row => {
      feeMap.set(row['equip_id'], (+row['price']).toFixed(2));
    });

    // Check if all equipment IDs have fees
    const missingIds = equipIds.filter(id => !feeMap.has(id));
    if (missingIds.length > 0) {
      throw `Equipment(s) (id=${missingIds.join(", ")}) are`
        + ` NOT available on dayType=${DayType[dayType]}, time=${time}`;
    }

    return feeMap;
  }

  async findInstructorFee(
    conn: Connection,
    instrId: string,
    dayType: DayType,
    time: string,
  ): Promise<string> {
    // (!) inclusive start, exclusive end
    // Eg, given the following 2 ranges:
    //     09:00 - 12:00
    //     12:00 - 14:00
    // a time of 12:00 will match the second range.
    const sql = `select * from instr02rates
      where instr_id = ? and type in (?, ?) and
      (start <= ? and end > ?)`;
    const params = [
      instrId,
      DayType[dayType],
      DayType[DayType.ALL],
      time,
      time,
    ];

    /*
    logger.debug(`findInstructorFee() sql=${sql}`);
    logger.debug(`findInstructorFee() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    if (results.length == 0) {
      throw `Instructor (id=${instrId}) has`
        + ` NO rates on dayType=${DayType[dayType]}, time=${time}`;
    }
    const price = results[0]['price']!;
    return (+price).toFixed(2);
  }

  async findOthersFee(conn: Connection): Promise<string> {
    const sql = "select * from app05fees where code = ?";
    const params = [
      "booking_processing"
    ];

    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    const price = results?.[0]?.['price'] ?? 0;
    return (+price).toFixed(2);
  }

  async findCompletedSessionByMemberId(
    conn: Connection,
    memberId: string,
  ): Promise<Session|undefined> {
    const sql = "select * from evt02sessions where member_id = ? and completed is true";
    const params = [memberId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results?.[0] ? SessionRepo.toSession(results[0]) : undefined;
  }

  //////////
  // Session //
  //////////

  /////////////
  // Support //
  /////////////

  async cacheDistanceToStudios(
    conn: Connection,
    fromLatLng: LatLng,
    studioIdsIn: string[],
  ): Promise<void> {
    if (studioIdsIn.length === 0) {
      return; // nothing else to do
    }

    const clauses = [];
    const params = [];
    if (studioIdsIn) {
      const qMarks = Array(studioIdsIn.length).fill("?").join(",")
      clauses.push(`(id in (${qMarks}))`);
      params.push(...studioIdsIn);
    }
    { // latlng
      clauses.push(`(
        (latlng is not null) and (latlng not in (
        select latlng_to from app03dist_cache where latlng_from = ?
      )))`);
      params.push(fromLatLng.toCacheString());
    }
    { // deleted
      clauses.push("(deleted is false)");
    }

    let sql = `select lat, lng from pub01studios`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    /*
    logger.debug(`cacheDistanceToStudios() sql=${sql}`);
    logger.debug(`cacheDistanceToStudios() params=${JSON.stringify(params)}`);
    */
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    for (const result of results) {
      const toLatLng = new LatLng(+result['lat'], +result['lng']);
      await this.insertDistanceCache(conn, fromLatLng, toLatLng);
    }
    /*
    logger.debug(`cacheDistanceToStudios() - done`);
    */
  }

  async cacheDistanceToStudiosBatch(
    conn: Connection,
    fromLatLng: LatLng,
    studioIdsQuery: string,
  ): Promise<void> {

    const clauses = [];
    const params = [
      fromLatLng.toCacheString(), // latlng_from
      fromLatLng.lat,              // latitude for calculation
      fromLatLng.lng,              // longitude for calculation
      fromLatLng.lat               // latitude for calculation (for sin)
  ];
  if(studioIdsQuery) {
      clauses.push(`(id IN (${studioIdsQuery}))`);
  }
    // Prepare the SQL query to calculate distances and insert them into the cache
    const sql = `
        INSERT INTO app03dist_cache (latlng_from, latlng_to, distance)
        (SELECT ?, latlng, (
            6371 * acos(
                cos(radians(?)) * cos(radians(lat)) * cos(radians(lng) - radians(?)) +
                sin(radians(?)) * sin(radians(lat))
            )
        ) AS distance
        FROM pub01studios
        WHERE ${clauses.join(" and ")}
        AND latlng IS NOT NULL)
        ON DUPLICATE KEY UPDATE distance = VALUES(distance), updated_at = now();
    `;

    // Execute the query with the provided parameters
    await conn.execute(sql, params);
  }

  private async insertDistanceCache(
    conn: Connection, from: LatLng, to: LatLng,
  ): Promise<void> {
    const sql = `insert into app03dist_cache(
        latlng_from, latlng_to, distance
      ) values (
        ?, ?, ?
      )
      on duplicate key update distance = VALUES(distance), updated_at = now();`;
    const params = [
      from.toCacheString(),
      to.toCacheString(),
      SearchService.distanceInKm(from, to),
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
  }

  async isPublicHoliday(
    conn: Connection,
    date: string, // yyyy-MM-DD
  ): Promise<boolean> {
    const sql = `select count(id) as count from app02phs where date = ?`;
    const params = [date];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results[0]['count'] !== 0;
  }

  async getServerTimezone(conn: Connection): Promise<string> {
    const sql = `SELECT @@session.time_zone as timezone`;
    const [results] = await conn.query<RowDataPacket[]>(sql);
    return results[0]['timezone'];
  }
}
