import { Exercise, ExerciseCategory, ExerciseModification } from "@booking-api/models/exercise";
import { Connection, RowDataPacket } from "mysql2/promise";

export class ExerciseRepo {

  async findCategories(
    conn: Connection,
  ): Promise<ExerciseCategory[]> {
    const sql = "select distinct category_id, category from app04exercises";
    const [results] = await conn.query<RowDataPacket[]>(sql);
    return results.map(o => this.toExerciseCategory(o));
  }

  private toExerciseCategory(row: any): ExerciseCategory {
    return {
      id: row['category_id']!,
      name: row['category']!,
    } as ExerciseCategory;
  }

  async findExercisesByCategoryId(
    conn: Connection,
    categoryId: string,
  ): Promise<Exercise[]> {
    const sql = `select distinct exercise_id, name, start_pos
      from app04exercises where category_id = ?`;
    const params = [categoryId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => this.toExercise(o));
  }

  private toExercise(row: any): Exercise {
    return {
      id: row['exercise_id']!,
      name: row['name']!,
      startingPosition: row['start_pos']!,
    } as Exercise;
  }

  async findModificationsByExerciseId(
    conn: Connection,
    exerciseId: string,
  ): Promise<ExerciseModification[]> {
    const sql = `select distinct mod_id, mod_name, mod_descr
      from app04exercises where exercise_id = ? and trim(mod_name) != ?`;
    const params = [exerciseId, ""];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => this.toExerciseModification(o));
  }

  private toExerciseModification(row: any): ExerciseModification {
    return {
      id: row['mod_id']!,
      name: row['mod_name']!,
      descr: row['mod_descr']!,
    } as ExerciseModification;
  }
}
