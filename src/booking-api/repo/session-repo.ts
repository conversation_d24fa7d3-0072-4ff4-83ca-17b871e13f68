import { parse<PERSON><PERSON><PERSON> } from "@utils/helper";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import { BookingStatus, BookingType } from "../models/booking";
import {
  InitSessionReq,
  Session,
  SessionNotes,
  SessionNotesSaveReq,
  SessionRecordCompleteReq,
  SessionRecordSaveReq
} from "../models/session";

export class SessionRepo {

  async initSession(
    conn: Connection, req: InitSessionReq,
  ): Promise<number> {
    const sql = `insert into evt02sessions (
        event_id, instr_id, member_id, state, target,
        created_by
      ) select
        id, instr_id, member_id, ?, ?, ?
        from evt01events
        where payment_req_id = ?
        and type = ? and booking_status = ? and deleted is false`;
    const by = req.by;
    logger.debug(`[${by}] initSession() req=${JSON.stringify(req)}`)
    const params = [
      '', // state
      '', // target
      by, // created_by
      req.paymentReqId,
      BookingType[BookingType.Booking],
      BookingStatus[BookingStatus.Confirmed],
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] initSession() payReqId=${req.paymentReqId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async initSessionByBookingId(
    conn: Connection, req: InitSessionReq,
  ): Promise<number> {
    const sql = `insert into evt02sessions (
        event_id, instr_id, member_id, state, target, created_by
      ) select
        id, instr_id, member_id, ?, ?, ?
        from evt01events
        where id = ?
        and type = ? and booking_status = ? and deleted is false`;
    const by = req.by;
    logger.debug(`[${by}] initSession() req=${JSON.stringify(req)}`)
    const params = [
      '', // state
      '', // target
      by, // created_by
      req.bookingId,
      BookingType[BookingType.Booking],
      BookingStatus[BookingStatus.Confirmed],
    ];

    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] initSession() bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async findByBookingId(
    conn: Connection, bookingId: string,
  ): Promise<Session|undefined> {
    const sql = "select * from evt02sessions where event_id = ?";
    const params = [bookingId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return SessionRepo.toSession(results[0]);
  }

  async findByBookingIds(
    conn: Connection, bookingIds: string[],
  ): Promise<Session[]|undefined> {
    const qMarks = Array(bookingIds.length).fill("?").join(",")
    const sql = `select * from evt02sessions where event_id IN (${qMarks})`;
    const params = [...bookingIds];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    const items = [];
    for (const result of results) {
      const ret = SessionRepo.toSession(result);
      items.push(ret);
    }

    return items;
  }

  static toSession(row: any): Session {
    return {
      bookingId: row['event_id'],
      instructorId: row['instr_id'],
      memberId: row['member_id'],
      notes: {
        state: row['state'],
        target: row['target'],
      } as SessionNotes,
      record: parseJSON(row['record']),
      record_flexi: parseJSON(row['record_flexi']),
      record_mode: row['record_mode'],
      completed: row['completed'] === 1,
      hasMemberFeedback: row['m_has_feedback'] === 1,
      memberFeedback: row['m_feedback'],
      instructorFeedback: row['m_instructor_feedback'],
      instructorRating: row['m_instructor_rating'],
      studioFeedback: row['m_studio_feedback'],
      studioRating: row['m_studio_rating'],
      isPrivateFeedback: row['is_private'] === 1,
      memberNoFeedback: row['m_no_feedback'] === 1,
      doorPin: row['door_pin'] ?? null,
    } as Session;
  }

  async saveSessionState(
    conn: Connection, req: SessionNotesSaveReq,
  ): Promise<number> {
    const sql = `update evt02sessions
      set state = ?, updated_by = ?
      where event_id = ? and instr_id = ? and completed is false`;
    const by = req.by.username;
    logger.debug(`[${by}] saveSessionState() req=${JSON.stringify(req)}`)
    const params = [
      req.value!,
      by,
      req.bookingId!,
      req.instructorId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] saveSessionState() bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async saveSessionTarget(
    conn: Connection, req: SessionNotesSaveReq,
  ): Promise<number> {
    const sql = `update evt02sessions
      set target = ?, updated_by = ?
      where event_id = ? and instr_id = ? and completed is false`;
    const by = req.by.username;
    logger.debug(`[${by}] saveSessionTarget() req=${JSON.stringify(req)}`)
    const params = [
      req.value!,
      by,
      req.bookingId!,
      req.instructorId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] saveSessionTarget() bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async saveSessionRecord(
    conn: Connection, req: SessionRecordSaveReq,
  ): Promise<number> {
    const recordMode = +process.env.SESSION_EXERCISE_MODE!;
    const sql = `update evt02sessions
      set record_mode = ?, record = ?, record_flexi = ?, state = ?, updated_by = ?
      where event_id = ? and instr_id = ?
      and completed is false`;
    const by = req.by.username;
    logger.debug(`[${by}] saveSessionRecord() req=${JSON.stringify(req)}`)
    const params = [
      recordMode,
      recordMode == 1? JSON.stringify(req.record!): null,
      recordMode == 2? JSON.stringify(req.record!): null,
      req.sessionState! ?? "",
      by, // updated_by
      req.bookingId!,
      req.instructorId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] saveSessionRecord() bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async completeSessionRecord(
    conn: Connection, req: SessionRecordCompleteReq,
  ): Promise<number> {
    const recordMode = +process.env.SESSION_EXERCISE_MODE!;
    const sql = `update evt02sessions
      set record_mode = ?, record = ?, record_flexi = ?, state = ?, completed = ?, completed_at = now(), updated_by = ?
      where event_id = ? and instr_id = ?
      and completed is false`;
    const by = req.by.username;
    logger.debug(`[${by}] completeSessionRecord() req=${JSON.stringify(req)}`)
    const params = [
      recordMode,
      recordMode == 1? JSON.stringify(req.record!): null,
      recordMode == 2? JSON.stringify(req.record!): null,
      req.sessionState! ?? "",
      true, // completed
      by,   // updated_by
      req.bookingId!,
      req.instructorId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] completeSessionRecord() bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async exists(conn: Connection, uid: string): Promise<number> {
    const sql = "select count(*) as `count` from evt02sessions where member_id = ? and completed is true";
    const params = [uid];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results[0]['count'];
  }
}
