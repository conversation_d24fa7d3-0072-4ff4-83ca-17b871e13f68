import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import { PaymentNotify } from "../models/payment";

export class PaymentRepo {

  async insertNotify(conn: Connection, notify: PaymentNotify): Promise<string> {
    const sql = `insert into pay01notify (
        id, pay_req_id, payload, verified
      ) values (
        ?, ?, ?, ?
      )`;
    const uuid = crypto.randomUUID();
    const params = [
      uuid,
      notify.paymentReqId ?? null,
      notify.payload,
      notify.verified,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`insertNotify() id=${uuid}, numRows=${results.affectedRows}`);
    return uuid;
  }

  async getNotifiesByPaymentReqIds(conn: Connection, paymentReqIds: string[]): Promise<PaymentNotify[]> {
    if (paymentReqIds.length === 0) return [];

    const placeholders = paymentReqIds.map(() => '?').join(',');
    const sql = `select * from pay01notify where pay_req_id in (${placeholders}) order by created_at desc`;
    const [results] = await conn.query<RowDataPacket[]>(sql, paymentReqIds);

    return results.map(row => ({
      id: row['id'],
      paymentReqId: row['pay_req_id'],
      payload: row['payload'],
      verified: row['verified'],
      createdAt: row['created_at'],
      by: row['created_by']
    }));
  }
}
