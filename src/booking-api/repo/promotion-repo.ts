import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import { Promotion, PromoCode, PromotionType } from "@backoffice-cms/models/promotion";
import logger from "@utils/logger";

export interface ValidatedPromoCode {
    promoCode: PromoCode;
    promotion: Promotion;
}

export class PromotionRepo {

    static toPromoCode(row: any): PromoCode {
        return {
            id: row.id,
            promotion_id: row.promotion_id,
            code: row.code,
            code_limit: row.code_limit,
            assigned_user_id: row.assigned_user_id,
            status: row.status,
            created_at: row.created_at,
            created_by: row.created_by,
        };
    }

    static toPromotion(row: any): Promotion {
        return {
            id: row.promotion_id_p,
            name: row.name,
            description: row.description,
            type: row.type,
            value: row.value,
            start_date: row.start_date,
            end_date: row.end_date,
            per_user_limit: row.per_user_limit,
            global_limit: row.global_limit,
            status: row.promotion_status,
            created_at: '', // Not needed for validation
            created_by: '', // Not needed for validation
        };
    }

    async findPromoCodeByCode(conn: Connection, code: string, type: PromotionType): Promise<ValidatedPromoCode | undefined> {
        const sql = `
            SELECT
                pc.*,
                p.id as promotion_id_p, p.name, p.description, p.type, p.value,
                p.start_date, p.end_date, p.per_user_limit, p.global_limit, p.status as promotion_status
            FROM promo02promo_code pc
            JOIN promo01promotion p ON pc.promotion_id = p.id
            WHERE pc.code = ? AND p.type = ?`;

        const [rows] = await conn.query<RowDataPacket[]>(sql, [code, type]);

        if (rows.length === 0) {
            return undefined;
        }

        const row = rows[0];
        return {
            promoCode: PromotionRepo.toPromoCode(row),
            promotion: PromotionRepo.toPromotion(row)
        };
    }

    async getUsageCount(conn: Connection, promoCodeId: number): Promise<number> {
        const sql = `SELECT COUNT(*) as count FROM promo03promo_code_usage WHERE promo_code_id = ?`;
        logger.debug(`[PromotionRepo] getUsageCount: ${conn.format(sql, [promoCodeId])}`);

        const [rows] = await conn.query<RowDataPacket[]>(sql, [promoCodeId]);
        return rows[0].count;
    }

    async getPromotionUsageByUser(conn: Connection, promotionId: number, userId: string): Promise<number> {
        const sql = `
            SELECT COUNT(*) as count
            FROM promo03promo_code_usage pcu
            JOIN promo02promo_code pc ON pcu.promo_code_id = pc.id
            WHERE pc.promotion_id = ? AND pcu.user_id = ?`;

        logger.debug(`[PromotionRepo] getPromotionUsageByUser: ${conn.format(sql, [promotionId, userId])}`);
        const [rows] = await conn.query<RowDataPacket[]>(sql, [promotionId, userId]);
        return rows[0].count;
    }

    async insertPromoCodeUsage(conn: Connection, promoCodeId: number, userId: string, ctrxId: string | null, eventId: string | null): Promise<number> {
        const [result] = await conn.query<ResultSetHeader>(
            `INSERT INTO promo03promo_code_usage (promo_code_id, user_id, ctrx_id, event_id) VALUES (?, ?, ?, ?)`,
            [promoCodeId, userId, ctrxId, eventId]
        );
        return result.insertId;
    }

    async getPromotionGlobalUsage(conn: Connection, promotionId: number): Promise<number> {
        const sql = `
            SELECT COUNT(*) as count
            FROM promo03promo_code_usage pcu
            JOIN promo02promo_code pc ON pcu.promo_code_id = pc.id
            WHERE pc.promotion_id = ?`;
        logger.debug(`[PromotionRepo] getPromotionGlobalUsage: ${conn.format(sql, [promotionId])}`);
        const [rows] = await conn.query<RowDataPacket[]>(sql, [promotionId]);
        return rows[0].count;
    }
}