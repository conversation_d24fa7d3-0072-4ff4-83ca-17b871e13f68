import { add, format, isValid, parse } from "date-fns";
import { StudioEquipment } from "@studio-cms/models/studio";

export interface SearchCriteria {
  latLng: LatLng,
  distance: number,
  at: SearchAt,
  dayType?: DayType,
  studioId?: string,
  instrId?: string,
  equipId?: string,
  equipTypes?: string[],
  instrLessOnly: boolean,
  faveStudioOnly: boolean,
  faveInstrOnly: boolean,
  preferredStudioFromFaveInstrOnly: boolean,
  searchTerm?: string,
  byId?: string,
  includeEquipment?: boolean,
  experiencedMemOnly?: boolean,
  timezone?: string,
}

export class SearchAt {
  constructor(
    public date: string, // yyyy-MM-dd
    public time: string, // HH:mm
  ) {
    const dateFormat = "yyyy-MM-dd";
    const timeFormat = "HH:mm";
    const d = parse(date, dateFormat, new Date());
    const t = parse(time, timeFormat, new Date());
    if (!isValid(d)) throw `Invalid date! ${d} (expected: yyyy-MM-dd)`;
    if (!isValid(t)) throw `Invalid time! ${t} (expected: HH:mm)`;
    this.dow = parseInt(format(date, "i"), 10);
    this.timeStart = format(t, timeFormat);
    this.timeEnd = format(add(t, {hours: 1}), timeFormat);
    this.startAt = date + ' ' + this.timeStart;
    this.endAt   = date + ' ' + this.timeEnd;
  }
  dow: number;
  timeStart: string;
  timeEnd: string;
  startAt: string;
  endAt: string;

  toString() {
    return `SearchAt(`
      + `date=${this.date}`
      + `, time=${this.time}`
      + `, dow=${this.dow}`
      + `, timeStart=${this.timeStart}`
      + `, timeEnd=${this.timeEnd}`
      + `, startAt=${this.startAt}`
      + `, endAt=${this.endAt}`
      + `)`
  }
}

export class LatLng {
  constructor(
    public lat: number,
    public lng: number,
  ) {
    if (!lat) throw `lat must not be null!`;
    if (!lng) throw `lng must not be null!`;
  }

  // concat lat (3dp) and lng (3dp) with - as delimiter.
  toCacheString(): string {
    const numDP = 3;
    return `${this.lat.toFixed(numDP)}-${this.lng.toFixed(numDP)}`
  }
}

export enum DayType {
  ALL,
  WD, // Weekdays
  WE, // Weekends
  PH, // Public Holidays
}

export interface InstructorMobility {
  dow: number,
  latLng: LatLng,
  distance: number,
}

// ---

export interface SearchResult {
  studio: SearchStudio,
  groups: SearchResultGroup[],
  equipment?: SearchEquipment[],
}

export interface SearchResultWrapper {
  extendStart: string,
  extendEnd: string,
  results: SearchResult[],
}

export interface SearchResultGroup {
  instructor?: SearchInstructor,
  slots: SearchSlot[],
}

export interface SearchStudio {
  id: string,
  name: string,
  address: string,
  pictures: string[],
  usagePolicies?: string,
  instrLess?: boolean,
  avgRating?: number,
  totalRatingCount?: number,
  equipments?: StudioEquipment[],
  fav?: boolean,
  latLng?: LatLng,
  distance?: number,
}

export interface SearchInstructor {
  id: string,
  name: string,
  descr?: string;
  pictures: string[];
  avgRating?: number,
  totalRatingCount?: number;
  fav?: boolean,
  slots: SearchSlot[],
}

export interface SearchSlot {
  time: string,
}

export interface SearchEquipment {
  id: string,
  studioId: string,
  type: SearchEquipmentType,
  code: string,
  privacy: boolean,
  fees?: BookingFees,
}

export interface SearchEquipmentType {
  id: string,
  name: string,
}

// ---

export interface BookingFeesReview {
  studio: SearchStudio;
  instructor?: SearchInstructor;
  equipment: SearchEquipment;
  slot: SearchSlot;
  fees: BookingFees;
}

export enum BookingFeeType {
  studio,
  instructor,
  others,
  promotion,
}

export interface BookingLineItem {
  type: BookingFeeType,
  name: string,
  price: string,
  credit: string,
  currency?: string,
  sign?: string,
}

export interface BookingFees {
  items: BookingLineItem[]
  total: string,
  totalCredits: string,
  currency?: string,
}
