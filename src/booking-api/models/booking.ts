import { By } from "@shared/models";
import { EventCancelBy } from "@studio-cms/models/event";
import { DateTime } from 'luxon';
import logger from "@utils/logger";

export enum BookingType {
  Booking,
  EquipmentBlocked,
  StudioBlocked,
}

export enum BookingStatus {
  PendingPayment,
  Confirmed,
  Cancelled,
}

export enum SessionStatus {
  Upcoming = "upcoming",
  Ongoing = "ongoing",
  Ended = "ended",
  Completed = "completed",
  PendingCancel = "pendingCancel",
  Cancelled = "cancelled",
  Refunded = "refunded",
  InstrNoShow = "instrNoShow"
}

export function determineSessionStatus(event: {
  timezone: string;
  startDate: string;
  startTime?: string;
  endTime?: string;
  cancelled?: boolean;
  cancelledWithRefund?: boolean;
  instrNoShow?: boolean;
  session?: {
    completed?: boolean;
  };
  cancelReq?: {
    approved?: boolean;
  }
}): SessionStatus {
  const timezone = event.timezone || 'Asia/Singapore';
  const now = DateTime.now().setZone(timezone);

  // Create DateTime objects more reliably
  const startDateTime = DateTime.fromFormat(
    `${event.startDate} ${event.startTime || '00:00:00'}`,
    'yyyy-MM-dd HH:mm:ss',
    { zone: timezone }
  );

  const endDateTime = DateTime.fromFormat(
    `${event.startDate} ${event.endTime || '23:59:59'}`,
    'yyyy-MM-dd HH:mm:ss',
    { zone: timezone }
  );

  // Validate DateTime objects
  if (!startDateTime.isValid) {
    logger.error(`Invalid startDateTime: ${startDateTime.invalidReason}`);
    return SessionStatus.Upcoming; // Default fallback
  }

  if (!endDateTime.isValid) {
    logger.error(`Invalid endDateTime: ${endDateTime.invalidReason}`);
    return SessionStatus.Upcoming; // Default fallback
  }

  if(event.cancelReq) {
    if (!event.cancelReq.approved) {
      return SessionStatus.PendingCancel;
    }
  }

  // Check cancellation first
  if (event.cancelled) {
    if (event.cancelledWithRefund) {
      return SessionStatus.Refunded;
    }
    return SessionStatus.Cancelled;
  }

  // Check instructor no-show
  if (event.instrNoShow) {
    return SessionStatus.InstrNoShow;
  }

  // Check completion
  if (event.session?.completed) {
    return SessionStatus.Completed;
  }

  // Time-based status (using Luxon comparison operators)
  if (now < startDateTime) {
    return SessionStatus.Upcoming;
  }

  if (now >= startDateTime && now <= endDateTime) {
    return SessionStatus.Ongoing;
  }

  if (now > endDateTime) {
    return SessionStatus.Ended;
  }

  // Default to upcoming if no other conditions match
  return SessionStatus.Upcoming;
}

export interface BookingFindCriteria {
  memberId: string;
  upcomingAndOngoing?: boolean;
  upcomingOnly?: boolean;
  ongoingOnly?: boolean;
  pastOnly?: boolean;
  cancelledOnly?: boolean;
  feedbackOnly?: boolean;
}

export interface SessionFindCriteria {
  instructorId: string;
  upcomingAndOngoing?: boolean;
  upcomingOnly?: boolean;
  ongoingOnly?: boolean;
  pastOnly?: boolean;
  cancelledOnly?: boolean;
  pastBookingId?: string;
}

export interface BookingStatusSaveReq {
  paymentReqId: string;
  bookingStatus: BookingStatus,
  by: string,
}

/* unused
export interface BookingNotesSaveReq {
  bookingId: string;
  memberId: string;
  value: string;
  by: By,
}
*/

export interface BookingFeedbackSaveReq {
  eventId: string;
  memberId: string;
  feedback?: string;
  instructorFeedback?: string;
  studioFeedback?: string;
  isPrivate?: boolean;
  instructorRating?: number;
  studioRating?: number;
  by: By,
}

export interface BookingNoFeedbackSaveReq {
  eventId: string;
  memberId: string;
  by: By,
}

//////////////
// Schedule //
//////////////

export interface ScheduleFindCriteria {
  userId: string,
  month: string,
  day: string,
}

////////////
// Cancel //
////////////

export interface BookingCancel {
  reqBy: EventCancelBy;
  userId: string;
  eventId: string;
  free?: boolean;
  reason?: string;
  approved?: boolean;
  at: Date,
}

export interface BookingCancelFindCriteria {
  token?: string;
  reqBy?: EventCancelBy;
  userId?: string;
  eventId: string;
  approved?: boolean;
  isValidRange?: boolean; // true if the event is valid cancellation time range
}

export interface BookingCancelReq {
  reqBy: EventCancelBy;
  userId: string;
  eventId: string;
  free?: boolean;
  reason?: string;
  approved?: boolean;
  by: By,
}

export interface BookingCancelResult {
  token: string;
  free: boolean;
}

export interface BookingDoorPinSaveReq {
  eventId: string;
  memberId: string;
  doorPin: string;
  by: By,
}
