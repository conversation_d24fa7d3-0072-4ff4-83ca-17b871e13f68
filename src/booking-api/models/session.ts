import { By } from "@shared/models";

export interface Session {
  bookingId: string;
  instructorId: string;
  memberId: string;
  notes: SessionNotes;
  record: SessionRecord;
  record_flexi: SessionRecordFlexi;
  record_mode: number,
  completed?: boolean,
  hasMemberFeedback?: boolean,
  instructorFeedback?: string;
  instructorRating?: number;
  studioFeedback?: string;
  studioRating?: number;
  feedbackAt?: string,
  isPrivateFeedback?: boolean,
  memberFeedback?: string,
  memberNoFeedback?: boolean,
  doorPin?: string,
}

export interface SessionNotes {
  state: string;
  target: string;
}

export interface SessionRecord {
  record_mode: 1;
  focus?: string;
  exercises?: ExerciseRecord[];
  feedback?: string;
}

export interface SessionRecordFlexi {
  record_mode: 2;
  segments?: string[];
  feedback?: string;
  overallRating?: number,
}

export interface ExerciseRecord_Category {
  id: string,
  name: string,
}

export interface ExerciseRecord_Exercise {
  name: string,
  id: string,
}

export interface ExerciseRecord_Modification {
  id: string,
  name: string,
}

export interface ExerciseRecord {
  category: ExerciseRecord_Category;
  exercise: ExerciseRecord_Exercise;
  modification?: ExerciseRecord_Modification;
  spring: string,
  reps: string,
  duration: string,
  stabilityRating: number,
  formRating: number,
}

export interface InitSessionReq {
  paymentReqId?: string;
  bookingId?: string;
  by: string,
}

export interface SessionNotesSaveReq {
  bookingId: string;
  instructorId: string;
  value: string;
  by: By,
}

export interface SessionRecordSaveReq {
  bookingId: string,
  instructorId: string,
  sessionState?: string,
  record: SessionRecord|SessionRecordFlexi;
  by: By,
}

export interface SessionRecordCompleteReq {
  bookingId: string,
  instructorId: string,
  sessionState?: string,
  record: SessionRecord|SessionRecordFlexi;
  by: By,
}
