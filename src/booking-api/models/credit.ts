import { By } from "@shared/models";
import { Event } from "@studio-cms/models/event";

export interface Package {
    id: string;
    name: string;
    currency: string;
    price: number;
    credits: number;
    bonusCredits: number;
    status: boolean;
    firstTimeOnly?: boolean;
    instructorOnly?: boolean;
    validFrom?: string;
    validTo?: string;
    purchaseLimit?: number;
    sequence?: number;
}

export interface ExchangeRate {
    id: string;
    currency: string;
    purchaseRate: number;
    bookingRate: number;
    effectiveDate: string;
}

export interface Payment {
    amount: number;
    currency: string;
}

export enum TransactionType {
    Topup,
    Booking,
    Refund,
    Expiry,
    Renewal,
    Promotion,
    AdjustmentIn,
    AdjustmentOut,
}

export enum TransactionTypeLabel {
    Topup = "Topup",
    Booking = "Booking",
    Refund = "Refund",
    Expiry = "Expiry",
    Renewal = "Renewal",
    Promotion = "Promotion",
    AdjustmentIn = "Adjustment In",
    AdjustmentOut = "Adjustment Out",
}

// Topup Uses Only
export enum TransactionStatus {
    Success,
    PendingPayment,
    ConfirmedPayment,
    CancelledPayment,
}

export enum TransactionStatusLabel {
    Success = "Success",
    PendingPayment = "PendingPayment",
    ConfirmedPayment = "ConfirmedPayment",
    CancelledPayment = "CancelledPayment",
}

export interface Transaction {
    id?: string;
    memberId: string;
    type: TransactionType;
    amount: number;
    status: TransactionStatus;
    transactionRef?: string;
    paymentReqId?: string;
    paymentId?: string;
    packageId?: string;
    eventId?: string;
    createdAt?: string;
    updatedAt?: string;
    package?: Package;
    payment?: Payment;
    booking?: Event;
    remark?: string;
}

export interface TransactionSaveReq {
    transaction: Transaction,
    by: By,
}

export interface TransactionStatusSaveReq {
    paymentReqId: string;
    paymentId?: string;
    status: TransactionStatus,
    by: string,
}

export interface TransactionSearchCriteria {
    memberId: string;
    types?: TransactionType[];
    status?: TransactionStatus[];
    startDate?: string;
    endDate?: string;
}

export interface CreditExtendExpiry {
  months: number;
  by: By;
}