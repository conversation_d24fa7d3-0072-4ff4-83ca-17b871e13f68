import { ScheduleFindCriteria } from "@booking-api/models/booking";
import { BookingService } from "@booking-api/services/booking-service";
import { SessionService } from "@booking-api/services/session-service";
import { Event } from "@studio-cms/models/event";
import { Session } from "../models/session";
import { toEventJson } from "@studio-cms/routes/shared/response-output";
import { format } from "date-fns";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const bookingService = new BookingService();
const sessionService = new SessionService();

const scheduleCalendar: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const year = req.query.year!.toString();
  const month = req.query.month!.toString();

  const events = await bookingService.schedule({
    month: `${year}-${month.padStart(2, "0")}`,
    userId: uid,
  } as ScheduleFindCriteria);
  const sessionIds = events.map(events => events.id);
  const sessions = sessionIds.length? await sessionService.findByBookingIds(sessionIds): [];
  const sessionsMap = new Map<string, Session>();

  sessions.forEach((session) => {
    sessionsMap.set(session.bookingId, session);
  });

  const results = events.map(event => {
    event.session = sessionsMap.get(event.id);
    return event;
  });

  const map: Map<String, Event[]> = groupByStartDate(results);

  const ret = {};
  for (let [key, value] of map) {
    // @ts-ignore
    ret[key] = value.map(o => toEventJson(o, {
      includeInternalRemarks: false,
      uid: uid
    }));
  }
  res.status(200).json(ret);
};

function groupByStartDate(events: Event[]) {
  const ret = new Map<String, Event[]>();
  for (const event of events) {
    const key = event.startDate; // Already in YYYY-MM-DD format
    if (ret.has(key)) {
      ret.get(key)!.push(event);
    } else {
      ret.set(key, [event]);
    }
  }
  return ret;
}

export default scheduleCalendar;
