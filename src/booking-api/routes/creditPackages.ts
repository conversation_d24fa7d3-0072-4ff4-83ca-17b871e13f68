import { toTopupPackagesJson } from "@booking-api/routes/shared/response-output";
import { CreditService } from "@booking-api/services/credit-service";
import { RequestHandler } from "express";
import { getOptionalIdToken } from "../../init-openapi";

const creditService = new CreditService();

const creditPackages: RequestHandler = async (req, res) => {
    const uid = getOptionalIdToken(req)?.uid;
    const currency = req.userCurrency || 'SGD';
    const results = await creditService.getPackagesByMemberIdEligible(uid, currency);
    res.status(200).json(results.map(e => toTopupPackagesJson(e)));
};

export default creditPackages;
