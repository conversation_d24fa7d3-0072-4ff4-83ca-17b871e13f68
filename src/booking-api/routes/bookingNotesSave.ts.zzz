import { BookingNotesSaveReq } from "@booking-api/models/booking";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { BookingService } from "../services/booking-service";

const bookingService = new BookingService();

const bookingNotesSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;

  const key = body.key!;
  if (![
    'SessionState',
    'SessionTarget',
  ].includes(key)) {
    res.status(400).end('unsupported key');
    return;
  }

  const toSave = {
    bookingId: body.bookingId!,
    memberId: uid, // (!) important
    value: body.value!,
    by: toBy(idToken),
  } as BookingNotesSaveReq;
  if (key === 'SessionState') {
    await bookingService.saveBookingState(toSave);
  } else if (key === 'SessionTarget') {
    await bookingService.saveBookingTarget(toSave);
  }

  res.status(200).end();
};

export default bookingNotesSave;
