import { Package, Transaction, TransactionTypeLabel, TransactionStatusLabel, TransactionType, TransactionStatus } from "@booking-api/models/credit";
import { Exercise, ExerciseCategory, ExerciseModification } from "@booking-api/models/exercise";
import {
  BookingFees,
  BookingFeesReview,
  BookingLineItem,
  SearchEquipment,
  SearchInstructor,
  SearchResult,
  SearchSlot,
  SearchStudio
} from "@booking-api/models/search";
import { EquipmentType } from "@studio-cms/models/equipment";
import { toEventJson } from "@studio-cms/routes/shared/response-output";
import { DateTime } from 'luxon';

export function toEquipmentType<PERSON>son(type: EquipmentType) {
  return {
    id: type.id,
    name: type.name,
  }
}

export function toExerciseCategoryJson(o: ExerciseCategory) {
  return {
    id: o.id,
    name: o.name,
  }
}

export function toExerciseJson(o: Exercise) {
  return {
    id: o.id,
    name: o.name,
    startingPosition: o.startingPosition,
  }
}

export function toExerciseModificationJson(o: ExerciseModification) {
  return {
    id: o.id,
    name: o.name,
    descr: o.descr,
  }
}

export function toSearchResultJson(result: SearchResult) {
  const studio = result.studio;

  // group by equipment type
  let equipmentGroups: Map<String, SearchEquipment[]>|undefined;
  const equipment = result.equipment;
  if (!!equipment) {
    equipmentGroups = new Map<String, SearchEquipment[]>();
    for (const o of equipment) {
      if (equipmentGroups.has(o.type.name)) {
        equipmentGroups.get(o.type.name)!.push(o);
      } else {
        equipmentGroups.set(o.type.name, [o]);
      }
    }
  }

  return {
    studio: toSearchStudioJson(result.studio),
    groups: [
      ...result.groups.filter(o => o.slots.length).map(group => ({
        ...!!group.instructor && {
          instructor: toSearchInstructorJson(group.instructor!),
        },
        ...group.slots.length && {
          slots: group.slots.map(o => toSearchTimeSlotJson(o))
        },
      })),
    ],
    ...(!!equipment && !!equipmentGroups) && {
      equipmentGroups: Array.from(equipmentGroups).map(([key, value]) => ({
        type: key,
        equipment: value.map(o => toSearchEquipmentJson(o)),
      })),
    }
  };
}

export function toSearchStudioJson(studio: SearchStudio) {
  return {
    id: studio.id,
    name: studio.name,
    address: studio.address,
    ...studio.pictures?.length && { picture: studio.pictures[0] },
    ...studio.instrLess && { instrLess: true },
    distance: studio.distance ?? null,
    avgRating: Number(studio.avgRating ?? 0).toFixed(1),
    totalRatingCount: studio.totalRatingCount ?? 0,
    equipments: studio.equipments ?? [],
    fav: studio.fav ?? false,
  };
}

export function toSearchInstructorJson(instr: SearchInstructor) {
  return {
    id: instr.id,
    name: instr.name,
    descr: instr.descr,
    ...instr.pictures?.length && { picture: instr.pictures[0] },
    avgRating: Number(instr.avgRating ?? 0).toFixed(1),
    totalRatingCount: instr.totalRatingCount ?? 0,
    fav: instr.fav ?? false,
  };
}

export function toSearchEquipmentJson(e: SearchEquipment) {
  return {
    id: e.id,
    code: e.code,
    privacy: e.privacy,
    type: {
      id: e.type.id,
      name: e.type.name,
    },
    ...e.fees && {
      fees: toBookingFeesJson(e.fees)},
  };
}

export function toSearchTimeSlotJson(slot: SearchSlot) {
  return {
    time: slot.time,
  };
}

export function toBookingFeesReviewJson(review: BookingFeesReview) {
  return {
    studio: {
      ...toSearchStudioJson(review.studio),
      usagePolicies: review.studio.usagePolicies ?? '',
    },
    ...!!review.instructor && {
      instructor: toSearchInstructorJson(review.instructor)
    },
    equipment: toSearchEquipmentJson(review.equipment),
    slot: toSearchTimeSlotJson(review.slot),
    fees: toBookingFeesJson(review.fees),
  };
}

export function toBookingFeesJson(fees: BookingFees) {
  return {
    items: fees.items.map(o => toBookingLineItemJson(o)),
    total: Number(fees.total).toFixed(2),
    totalCredits: Number(fees.totalCredits).toFixed(0),
    currency: fees.currency ?? 'SGD'
  };
}

export function toBookingLineItemJson(item: BookingLineItem) {
  const price = Number(item.price);
  const credit = Number(item.credit)
  return {
    name: item.name,
    price: price.toFixed(2),
    credit: credit.toFixed(0),
    currency: item.currency,
    sign: item.sign ?? ''
  };
}

export function toTopupPackagesJson(item: Package) {
  return {
    id: item.id,
    name: item.name,
    currency: item.currency,
    price: Number(item.price).toFixed(2),
    credits: Number(item.credits).toFixed(0),
    bonusCredits: Number(item.bonusCredits).toFixed(0),
    status: item.status,
    firstTimeOnly: item.firstTimeOnly || false,
    instructorOnly: item.instructorOnly || false,
    validFrom: item.validFrom,
    validTo: item.validTo,
    purchaseLimit: item.purchaseLimit,
    sequence: item.sequence
  };
}

export function toTransactionJson(transaction: Transaction, {
  uid = null,
  userTimezone = 'Asia/Singapore'
}) {
  let booking = null;
  if (transaction.booking) {
      booking = toEventJson(transaction.booking, {
        includeInternalRemarks: false,
        uid: uid
      });

    }
  return {
      id: transaction.id!,
      memberId: transaction.memberId,
      type: TransactionTypeLabel[TransactionType[transaction.type]],
      amount: transaction.amount.toFixed(2),
      status: TransactionStatusLabel[TransactionStatus[transaction.status]],
      transactionRef: transaction.transactionRef,
      paymentReqId: transaction.paymentReqId,
      paymentId: transaction.paymentId,
      packageId: transaction.packageId,
      package: transaction.package ? {
          id: transaction.package.id,
          name: transaction.package.name,
          currency: transaction.package.currency,
          price: Number(transaction.package.price).toFixed(2),
          credits: Number(transaction.package.credits).toFixed(0),
          bonusCredits: Number(transaction.package.bonusCredits).toFixed(0),
      } : undefined,
      payment: transaction.payment ? {
          amount: Number(transaction.payment.amount).toFixed(2),
          currency: transaction.payment.currency
      } : undefined,
      ...booking && { booking: booking },
      remark: transaction.remark,
      createdAt: DateTime.fromFormat(transaction.createdAt!, "yyyy-MM-dd HH:mm:ss", { zone: 'UTC' }).setZone(userTimezone).toFormat('yyyy-MM-dd HH:mm:ss'),
      ...transaction.updatedAt && { updatedAt: DateTime.fromFormat(transaction.updatedAt!, "yyyy-MM-dd HH:mm:ss", { zone: 'UTC' }).setZone(userTimezone).toFormat('yyyy-MM-dd HH:mm:ss') },
  };
}
