import { SessionRecord, SessionR<PERSON>ord<PERSON>lexi, ExerciseRecord, ExerciseRecord_Category, ExerciseRecord_Exercise, ExerciseRecord_Modification } from "@booking-api/models/session";

// Type guard for ExerciseRecord_Category
function isExerciseCategory(category: any): category is ExerciseRecord_Category {
  return category &&
         typeof category.id === 'string' &&
         typeof category.name === 'string';
}

// Type guard for ExerciseRecord_Exercise
function isExerciseType(exercise: any): exercise is ExerciseRecord_Exercise {
  return exercise &&
         typeof exercise.id === 'string' &&
         typeof exercise.name === 'string';
}

// Type guard for ExerciseRecord_Modification
function isExerciseModification(modification: any): modification is ExerciseRecord_Modification {
  return modification &&
         typeof modification.id === 'string' &&
         typeof modification.name === 'string';
}

// Type guard for ExerciseRecord
function isExerciseRecord(exercise: any): exercise is ExerciseRecord {
  return exercise &&
         isExerciseCategory(exercise.category) &&
         isExerciseType(exercise.exercise) &&
         (!exercise.modification || isExerciseModification(exercise.modification)) &&
         typeof exercise.spring === 'string' &&
         typeof exercise.reps === 'string' &&
         typeof exercise.duration === 'string' &&
         typeof exercise.stabilityRating === 'number' &&
         typeof exercise.formRating === 'number';
}

// Validation for exercises array
export function isValidExercises(exercises: any[]): boolean {
  return Array.isArray(exercises) && exercises.every(isExerciseRecord);
}

// Validation for segments array
export function isValidSegments(segments: any[]): boolean {
  return Array.isArray(segments) && segments.every(segment => typeof segment === 'string');
}

// Type guard for SessionRecord
export function isSessionRecord(record: any): record is SessionRecord {
  return record &&
         record.record_mode === 1 &&
         (typeof record.focus === 'string' || record.focus === undefined) &&
         Array.isArray(record.exercises) &&
         (record.feedback === undefined || typeof record.feedback === 'string') &&
         record.exercises.every(isExerciseRecord);
}

// Type guard for SessionRecordFlexi
export function isSessionRecordFlexi(record: any): record is SessionRecordFlexi {
  return record &&
         record.record_mode === 2 &&
         Array.isArray(record.segments) &&
         record.segments.every(segment => typeof segment === 'string') &&
         (record.feedback === undefined || typeof record.feedback === 'string') &&
         (record.overallRating === undefined || typeof record.overallRating === 'number');
}
