import { toBy } from "@shared/request-parsers";
import { EventNoShowReq } from "@studio-cms/models/event";
import { EventService } from "@studio-cms/services/event-service";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const eventService = new EventService();

const bookingNoShow: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;

  await eventService.noShow({
    eventId: req.params.eventId!,
    memberId: uid, // (!) important
    remarks: body.remarks,
    by: toBy(idToken),
  } as EventNoShowReq);
  res.status(200).json();
};

export default bookingNoShow;
