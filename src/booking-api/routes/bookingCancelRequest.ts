import { BookingCancelReq } from "@booking-api/models/booking";
import { EventCancelBy } from '@studio-cms/models/event';
import { BookingService } from "@booking-api/services/booking-service";
import { toBy } from "@shared/request-parsers";
import { Request<PERSON>and<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";

const bookingService = new BookingService();

const bookingCancelRequest: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  try {

    const result = await bookingService.cancelReq({
      reqBy: EventCancelBy.Member, // (!) important
      userId: uid, // (!) important
      eventId: req.params.eventId!.toString(),
      by: toBy(idToken),
    } as BookingCancelReq);
    res.status(200).json({
      token: result.token,
      free: result.free,
    });
  } catch (error) {
    res.status(400).send(error)
  }
};

export default bookingCancelRequest;
