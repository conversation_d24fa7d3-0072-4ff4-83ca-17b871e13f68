import { verify } from "@utils/hitpay";
import logger from "@utils/logger";
import { RequestHandler } from "express";
import { PaymentNotify } from "../models/payment";
import { PaymentService } from "../services/payment-service";
import { BookingService } from "../services/booking-service";
import { StudioService } from "@studio-cms/services/studio-service";
import eventEmitter from "@utils/event-emitter";
import { BookingDoorPinSaveReq } from "@booking-api/models/booking";
import { By } from "@shared/models";

const paymentService = new PaymentService();
const bookingService = new BookingService();
const studioService = new StudioService();

const hitpayNotify: RequestHandler = async (req, res) => {
  const body = req.body;
  const bodyStringify = JSON.stringify(body);
  logger.debug(`[hitpayNotify] body=${bodyStringify}`);

  const payloadMap = new Map<string, string>();
  Object.keys(body).forEach(key => {
    payloadMap.set(key, body[key]);
  });
  const apiSalt = process.env.HITPAY_API_SALT!;
  const verified = verify(payloadMap, apiSalt);
  logger.debug(`[hitpayNotify] verified=${verified}`);

  await paymentService.insertNotify({
    paymentReqId: body.payment_request_id?.toString(),
    payload: bodyStringify,
    verified: verified,
    by: "HitPayWebhook",
  } as PaymentNotify);

  const event = await bookingService.findByPaymentReqId(body.payment_request_id?.toString());
  if (event) {
    const studio = await studioService.findById(event.studioId);
    if (studio?.hasDoorPin) {
        await bookingService.saveDoorPin({
          eventId: event.id,
          memberId: event.memberId,
          doorPin: "9870123",
          by: {
            username: "HitPayWebhook",
            uid: "HitPayWebhook",
          } as By,
        } as BookingDoorPinSaveReq);
    }

    eventEmitter.emit('session-confirmed', {
      by: "HitPayWebhook",
      id: event.id,
      data: event,
    });
  }

  res.status(200).end();
};

export default hitpayNotify;
