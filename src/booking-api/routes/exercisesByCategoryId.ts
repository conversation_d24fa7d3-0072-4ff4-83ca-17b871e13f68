import { toExercise<PERSON><PERSON> } from "@booking-api/routes/shared/response-output";
import { ExerciseService } from "@booking-api/services/exercise-service";
import { RequestHandler } from "express";

const exerciseService = new ExerciseService();

const exercisesByCategoryId: RequestHandler = async (req, res) => {
  // const idToken = getIdToken(req);
  // const uid = idToken.uid;

  const categoryId = req.params.categoryId!.trim();
  const ret = await exerciseService.findExercisesByCategoryId(categoryId);
  res.status(200).json(
    ret.map(o => toExerciseJson(o))
  );
};

export default exercisesByCategoryId;
