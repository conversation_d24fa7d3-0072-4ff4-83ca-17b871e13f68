import {
  Booking<PERSON>eesR<PERSON>iew,
  LatLng,
  SearchAt,
  SearchCriteria,
  SearchInstructor,
  SearchSlot
} from "@booking-api/models/search";
import { toBookingFeesReviewJson } from "@booking-api/routes/shared/response-output";
import { SearchService } from "@booking-api/services/search-service";
import logger from "@utils/logger";
import { RequestHandler } from "express";
import { getOptionalIdToken } from "../../init-openapi";

const searchService = new SearchService();

const searchEquipmentChoose: RequestHandler = async (req, res) => {
  const latLng = new LatLng(
    +req.query.lat!.toString().trim(),
    +req.query.lng!.toString().trim(),
  );
  const distance = +req.query.distance!.toString().trim();
  const at = new SearchAt(
    req.query.date!.toString().trim(),
    req.query.time!.toString().trim(),
  );
  const studioId = req.query.studioId!.toString().trim();
  const instrId  = req.query.instrId?.toString().trim();
  const equipId  = req.query.equipId!.toString().trim();
  const promoCode = req.query.promoCode?.toString().trim();
  const equipTypes
    = req.query.equipTypes?.toString().trim()
    .split(",").map(o => o.trim());
  const instrLessOnly  = (req.query.instrLessOnly  + "") == "true";
  const faveStudioOnly = (req.query.faveStudioOnly + "") == "true";
  const faveInstrOnly  = (req.query.faveInstrOnly  + "") == "true";

  const criteria = {
    latLng: latLng,
    distance: distance,
    at: at,
    studioId: studioId,
    instrId: instrId,
    equipId: equipId,
    equipTypes: equipTypes,
    instrLessOnly : instrLessOnly,
    faveStudioOnly: faveStudioOnly,
    faveInstrOnly : faveInstrOnly,
    byId: getOptionalIdToken(req)?.uid,
    includeEquipment: true,
    timezone: req.userTimezone,
  } as SearchCriteria;
  const results = await searchService.search(criteria);

  if (
    results.length != 1
    || results[0].groups?.length < 1
    || results[0].groups[0].slots?.length != 1
    || results[0].equipment?.length != 1
  ) {
    logger.info(`searchEquipmentChoose() slot no longer available (a)`);
    res.status(204).end(); // no content
    return;
  }

  let instructor: SearchInstructor|undefined;
  if (!!instrId) {
    const group = results[0].groups.find(o => o.instructor?.id === instrId);
    if (!group) {
      logger.info(`searchEquipmentChoose() slot no longer available (b)`);
      res.status(204).end(); // no content
      return;
    }
    instructor = group.instructor!;
  }
  const studio = results[0].studio;
  const equipment = results[0].equipment[0];

  const bookingFees = await searchService.getBookingFees(at, equipId, instrId, promoCode, criteria.byId);
  const ret = {
    studio: studio,
    instructor: instructor,
    equipment: equipment,
    slot: {
      time: criteria.at.time,
    } as SearchSlot,
    fees: bookingFees,
  } as BookingFeesReview;

  res.status(200).json(toBookingFeesReviewJson(ret));
};

export default searchEquipmentChoose;
