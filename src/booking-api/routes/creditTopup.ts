import { CreditService } from "@booking-api/services/credit-service";
import { <PERSON>quest<PERSON>and<PERSON> } from "express";
import { toBy } from "@shared/request-parsers";
import { getIdToken } from "../../init-openapi";
import logger from "@utils/logger";
import { randomDigits, randomUppers } from "@utils/random";
import axios from 'axios';
import { Transaction, TransactionSaveReq, TransactionStatus, TransactionType } from "@booking-api/models/credit";
import { getCurrentTimeInUserTimezone } from "@shared/middleware/timezone";

const creditService = new CreditService();

const creditTopup: RequestHandler = async (req, res) => {

    const idToken = getIdToken(req);
    const uid = idToken.uid;
    const by = idToken.email;

    // 1. Extract parameters
    const body = req.body;

    logger.debug(`[${by}] ${JSON.stringify(req.body)}`);
    const packageId = body.packageId!.toString().trim();
    const redirect = body.redirectUrl?.trim();

    // 2. Validation
    const topupPackage = await creditService.getPackageById(packageId);
    if (!topupPackage) {
      res.status(204).end("topup package not found"); // no content
      return;
    }

    // Check eligibility
    const eligibility = await creditService.isEligibleForPackage(uid, packageId);
    if (!eligibility.eligible) {
      res.status(403).end(eligibility.reason || "Eligibility check failed");
      return;
    }

    // 3. Generate trx ref no.
    const trxRef = `${randomUppers(5)}-${randomDigits(5)}`;
    logger.debug(`[${by}] creditTopup() trxRef=${trxRef}`);

    // 4. HitPay
    try {
        const now = getCurrentTimeInUserTimezone(req);
        const expireAt = now.plus({ minutes: 15 });
        const webhook = `${process.env.HITPAY_WEBHOOK_URL!}/api/booking/credit/payment-notify`;
        const hitpayApiUrl = `${process.env.HITPAY_API_URL!}/v1/payment-requests`;
        const hitpayApiReq
            = `reference_number=${encodeURIComponent(trxRef)}`
            + `&purpose=ViFit`
            + `&webhook=${encodeURIComponent(webhook)}`
            + `&currency=SGD`
            + `&amount=${encodeURIComponent(topupPackage.price)}`
            + `&expiry_date=${encodeURIComponent(expireAt.toFormat('yyyy-MM-dd HH:mm:ss'))}`
            + (redirect? `&redirect_url=${encodeURIComponent(redirect)}`: '');

        logger.debug(`[${by}] creditTopup() pgApiReq=${hitpayApiReq}`);
        const response = await axios.post(hitpayApiUrl, hitpayApiReq, {
            headers: {
            "User-Agent": "-",
            "Content-Type": "application/x-www-form-urlencoded",
            "X-Requested-With": "XMLHttpRequest",
            "X-BUSINESS-API-KEY": process.env.HITPAY_API_KEY!,
            }
        });
        logger.debug(`[${by}] creditTopup() pgApiResp=${JSON.stringify(response.data)}`);
        const paymentReqId = response.data.id!.trim();
        const checkoutURL = response.data.url!.trim();

        // 5. Create Topup Transaction
        const transaction = {
            memberId: uid,
            type: TransactionType.Topup,
            amount: Number(topupPackage.credits) + Number(topupPackage.bonusCredits),
            status: TransactionStatus.PendingPayment,
            transactionRef: trxRef,
            paymentReqId: paymentReqId,
            packageId: topupPackage.id,
        } as Transaction;

        const transactionId = await creditService.createTrx({
            transaction: transaction,
            by: toBy(idToken),
        } as TransactionSaveReq);

        if (!checkoutURL) {
            throw new Error("checkoutURL is null");
        }
        res.status(200).end(checkoutURL);

    } catch (error) {
        logger.error(`[${by}] creditTopup() error=${JSON.stringify(error?.response?.data) ?? error}`);
        res.status(500).end();
    }
};

export default creditTopup;
