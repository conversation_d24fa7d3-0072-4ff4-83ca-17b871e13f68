import { SearchAt } from "@booking-api/models/search";
import { SearchService } from "@booking-api/services/search-service";
import { InstructorService } from "@instructor-api/services/instructor-service";
import { toBy } from "@shared/request-parsers";
import { Event, EventSaveReq } from "@studio-cms/models/event";
import { EquipmentService } from "@studio-cms/services/equipment-service";
import logger from "@utils/logger";
import { randomDigits, randomUppers } from "@utils/random";
import axios from 'axios';
import { addMinutes, format, isValid, parse } from "date-fns";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { BookingStatus, BookingType } from "../models/booking";
import { BookingService } from "../services/booking-service";
import { getCurrentTimeInUserTimezone } from "@shared/middleware/timezone";

const bookingService = new BookingService();
const equipService = new EquipmentService();
const instrService = new InstructorService();
const searchService = new SearchService();

const bookingMake: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const by = idToken.email;

  // 1. Extract parameters
  const body = req.body;

  logger.debug(`[${by}] ${JSON.stringify(req.body)}`);
  const date = body.date!.toString().trim();
  const time = body.time!.toString().trim();
  const at = new SearchAt(date, time);
  const equipId = body.equipmentId!.toString().trim();
  const instrId = body.instructorId?.toString().trim();
  const redirect = body.redirectUrl?.trim();

  // 2. Validation
  if (!isValid(parse(time, "HH:mm", new Date()))) {
    res.status(400).end(`invalid time: ${time}`); // bad request
    return;
  }
  const equip = await equipService.findById(equipId);
  if (!equip) {
    res.status(204).end("equipment not found"); // no content
    return;
  }
  if (instrId) {
    const instr = await instrService.findById(instrId);
    if (!instr) {
      res.status(204).end("instructor not found"); // no content
      return;
    }
  }

  // 3. Generate booking ref no.
  const bookingRef = `${randomUppers(3)}-${randomDigits(3)}`;
  logger.debug(`[${by}] makeBooking() bookingRef=${bookingRef}`);

  // 4. HitPay
  const now = getCurrentTimeInUserTimezone(req);
  const expireAt = now.plus({ minutes: 15 });
  const bookingFees = await searchService.getBookingFees(at, equipId, instrId);
  const webhook = `${process.env.HITPAY_WEBHOOK_URL!}/api/booking/hitpay-notify`;
  const hitpayApiUrl = `${process.env.HITPAY_API_URL!}/v1/payment-requests`;
  const hitpayApiReq
    = `reference_number=${encodeURIComponent(bookingRef)}`
    + `&purpose=ViFit`
    + `&webhook=${encodeURIComponent(webhook)}`
    + `&currency=SGD`
    + `&amount=${encodeURIComponent(bookingFees.total)}`
    + `&expiry_date=${encodeURIComponent(expireAt.toFormat("yyyy-MM-dd HH:mm:ss"))}`
    + (redirect? `&redirect_url=${encodeURIComponent(redirect)}`: '');

  logger.debug(`[${by}] makeBooking() hitpayApiReq=${hitpayApiReq}`);
  const {data} = await axios.post(hitpayApiUrl, hitpayApiReq, {
    headers: {
      "User-Agent": "-",
      "Content-Type": "application/x-www-form-urlencoded",
      "X-Requested-With": "XMLHttpRequest",
      "X-BUSINESS-API-KEY": process.env.HITPAY_API_KEY!,
    }
  });
  logger.debug(`[${by}] makeBooking() hitpayApiResp=${JSON.stringify(data)}`);
  const paymentReqId = data.id!.trim();
  const checkoutURL = data.url!.trim();

  // 5. Make booking
  const event = {
    studioId: equip.studioId,
    type: BookingType[BookingType.Booking],
    bookingRefNo: bookingRef,
    bookingStatus: BookingStatus[BookingStatus.PendingPayment],
    paymentReqId: paymentReqId,
    startDate: at.date,
    startTime: at.timeStart,
    endDate: at.date,
    endTime: at.timeEnd,
    equipmentId: equipId,
    instructorId: instrId,
    memberId: uid,
    holdAt: now,
    holdEnds: expireAt,
  } as Event;
  const bookingId = await bookingService.makeBooking({
    event: event,
    by: toBy(idToken),
  } as EventSaveReq, bookingFees);

  if (!checkoutURL) {
    res.status(500).end();
  } else {
    res.status(200).end(checkoutURL);
  }
};

export default bookingMake;
