import { SearchAt, LatLng } from "@booking-api/models/search";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { BookingService } from "../services/booking-service";
import { BookingFlowService } from "../services/booking-flow-service";

const bookingService = new BookingService();
const bookingFlow = new BookingFlowService();

const creditBooking: RequestHandler = async (req, res) => {
    const idToken = getIdToken(req);
    const uid = idToken.uid;
    const by = idToken.email;
    const body = req.body;
    const latLng = new LatLng(+body.lat, +body.lng);
    const distance = +body.distance;
    const date = body.date.trim();
    const time = body.time.trim();
    const at = new SearchAt(date, time);
    const studioId = body.studioId.trim();
    const equipId = body.equipmentId.trim();
    const instrId = body.instructorId?.toString().trim();
    const promoCode = body.promoCode?.toString().trim();

    // Validation
    const validation = await bookingFlow.validateSlotAndCredits({
        latLng, distance, at, studioId, equipId, instrId, memberId: uid, promoCode
    });
    if (!validation.valid) {
        res.status(400).end(validation.reason);
        return;
    }
    // Booking creation
    const { bookingId, event } = await bookingFlow.createBooking({
        equip: validation.equip,
        instr: validation.instr,
        at,
        studioId,
        equipId,
        instrId,
        memberId: uid,
        by: toBy(idToken),
        bookedByAdmin: false,
        bookingFees: validation.bookingFees,
        promoCode
    });
    // Post-booking actions
    const booking = await bookingService.bookingById(bookingId);
    if (booking) {
        bookingFlow.emitSessionConfirmed({ by, booking });
        await bookingFlow.handleDoorPinIfNeeded({ booking, event, by });
    }
    res.status(200).json({ bookingId });
};

export default creditBooking;
