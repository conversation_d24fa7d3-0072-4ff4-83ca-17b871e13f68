import { Page } from "@shared/services";
import { toEventJson } from "@studio-cms/routes/shared/response-output";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { SessionFindCriteria } from "../models/booking";
import { BookingService } from "../services/booking-service";
import { SessionService } from "@booking-api/services/session-service";
import { Session } from "../models/session";
import logger from "@utils/logger";

const bookingService = new BookingService();
const sessionService = new SessionService();

const sessions: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const upcomingAndOngoing =  req.query.upcomingAndOngoing + "";
  const upcomingOnly = req.query.upcomingOnly + "";
  const ongoingOnly = req.query.ongoingOnly + "";
  const pastOnly = req.query.pastOnly + "";
  const cancelledOnly = req.query.cancelledOnly + "";
  const pastBookingId = req.query.pastBookingId;
  const page = new Page(+req.params.page, +req.params.pageSize);
  if(pastBookingId) {
    page.size = 3; // pass only latest 3 records
  }
  const ret = await bookingService.sessions({
    instructorId: uid,
    upcomingAndOngoing: (upcomingAndOngoing == "true"),
    upcomingOnly: (upcomingOnly == "true"),
    ongoingOnly: (ongoingOnly == "true"),
    pastOnly: (pastOnly == "true"),
    cancelledOnly: (cancelledOnly == "true"),
    ...pastBookingId && {
      pastBookingId: pastBookingId,
    }
  } as SessionFindCriteria, page);

  const bookingIds = ret.map(booking => booking.id);
  const sessions = bookingIds.length? await sessionService.findByBookingIds(bookingIds): [];
  const sessionsMap = new Map<string, Session>();

  sessions.forEach((session) => {
    sessionsMap.set(session.bookingId, session);
  });

  const cancelReqs = bookingIds.length? await bookingService.findBookingCancelReqsByBookingIds(bookingIds, false): [];
  const cancelReqsMap = new Map<string, any>();
  cancelReqs.forEach((cancelReq) => {
    cancelReqsMap.set(cancelReq.eventId, cancelReq);
  });

  const results = ret.map(event => {
    event.session = sessionsMap.get(event.id);
    event.cancelReq = cancelReqsMap.get(event.id);
    return event;
  });

  res.status(200).json(
    results.map(e => toEventJson(e, {
      includeInternalRemarks: false,
      uid: uid,
    }))
  );
};

export default sessions;
