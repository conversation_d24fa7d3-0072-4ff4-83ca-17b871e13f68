import { toE<PERSON><PERSON><PERSON> } from "@studio-cms/routes/shared/response-output";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { BookingService } from "../services/booking-service";
import { SessionService } from "../services/session-service";
import logger from "@utils/logger";
import { BookingCancelFindCriteria } from "@booking-api/models/booking";

const bookingService = new BookingService();
const sessionService = new SessionService();

const sessionById: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const bookingId = req.params.bookingId!;
  const ret = await bookingService.bookingById(bookingId);

  if (!ret || ret.instructorId != uid) {
    res.status(204).end(); // no content
    return;
  }

  const session = await sessionService.findByBookingId(bookingId);
  if (!session || session.instructorId != uid) {
    res.status(204).json("session not found"); // no content
    return;
  }

  const cancelReq = await bookingService.findBookingCancel({
    eventId: bookingId,
    approved: false, // only unapproved requests
  } as BookingCancelFindCriteria);

  ret.session = session;
  cancelReq && (ret.cancelReq = cancelReq);
  // logger.debug(`[${bookingId}] sessionById() session=${JSON.stringify(ret.session)}`)
  res.status(200).json(toEventJson(ret, {
    includeInternalRemarks: false,
    uid: uid,
  }));
};

export default sessionById;
