import { toExerciseModificationJson } from "@booking-api/routes/shared/response-output";
import { ExerciseService } from "@booking-api/services/exercise-service";
import { RequestHandler } from "express";

const exerciseService = new ExerciseService();

const exerciseModsByExerciseId: RequestHandler = async (req, res) => {
  // const idToken = getIdToken(req);
  // const uid = idToken.uid;

  const exerciseId = req.params.exerciseId!.trim();
  const ret = await exerciseService.findModificationsByExerciseId(exerciseId);
  res.status(200).json(
    ret.map(o => toExerciseModificationJson(o))
  );
};

export default exerciseModsByExerciseId;
