import { LatLng, SearchAt, SearchCriteria } from "@booking-api/models/search";
import { toSearchResultJson } from "@booking-api/routes/shared/response-output";
import { SearchService } from "@booking-api/services/search-service";
import { RequestHandler } from "express";
import { getOptionalIdToken } from "../../init-openapi";
import logger from "@utils/logger";

const searchService = new SearchService();

const searchEquipment: RequestHandler = async (req, res) => {

  try {

    const latLng = new LatLng(
      +req.query.lat!.toString().trim(),
      +req.query.lng!.toString().trim(),
    );
    const distance = +req.query.distance!.toString().trim();
    const at = new SearchAt(
      req.query.date!.toString().trim(),
      req.query.time!.toString().trim(),
    );
    const studioId = req.query.studioId!.toString().trim();
    const instrId  = req.query.instrId?.toString().trim();
    const equipTypes
      = req.query.equipTypes?.toString().trim()
      .split(",").map(o => o.trim());
    const instrLessOnly  = (req.query.instrLessOnly  + "") == "true";
    const faveStudioOnly = (req.query.faveStudioOnly + "") == "true";
    const faveInstrOnly  = (req.query.faveInstrOnly  + "") == "true";

    const results = await searchService.search({
      latLng: latLng,
      distance: distance,
      at: at,
      studioId: studioId,
      instrId: instrId,
      equipTypes: equipTypes,
      instrLessOnly : instrLessOnly,
      faveStudioOnly: faveStudioOnly,
      faveInstrOnly : faveInstrOnly,
      byId: getOptionalIdToken(req)?.uid,
      includeEquipment: true,
      timezone: req.userTimezone,
    } as SearchCriteria);

    res.status(200).json({
      results: results.map(o => toSearchResultJson(o))
    });
  } catch (error) {
    logger.error(`Error in searchEquipment: ${error}`);
    res.status(500).json({ error: "Internal server error" });
  }
};

export default searchEquipment;
