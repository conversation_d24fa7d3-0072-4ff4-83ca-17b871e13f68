import { verify } from "@utils/hitpay";
import logger from "@utils/logger";
import { RequestHandler } from "express";
import { PaymentNotify } from "../models/payment";
import { PaymentService } from "../services/payment-service";
import { CreditService } from "@booking-api/services/credit-service";
import eventEmitter from "@utils/event-emitter";
import { BookingDoorPinSaveReq } from "@booking-api/models/booking";
import { By } from "@shared/models";

const paymentService = new PaymentService();
const creditService = new CreditService();

const creditTopupNotify: RequestHandler = async (req, res) => {
  const body = req.body;
  const bodyStringify = JSON.stringify(body);
  logger.debug(`[creditTopupNotify] body=${bodyStringify}`);

  const payloadMap = new Map<string, string>();
  Object.keys(body).forEach(key => {
    payloadMap.set(key, body[key]);
  });
  const apiSalt = process.env.HITPAY_API_SALT!;
  const verified = verify(payloadMap, apiSalt);
  logger.debug(`[creditTopupNotify] verified=${verified}`);

  await paymentService.insertNotifyTopup({
    paymentReqId: body.payment_request_id?.toString(),
    paymentId: body.payment_id?.toString(),
    payload: bodyStringify,
    verified: verified,
    by: "HitPayWebhook",
  } as PaymentNotify);

  const transaction = await creditService.getTrxByPaymentReqId(body.payment_request_id?.toString());

  if (transaction) {
    eventEmitter.emit('transaction-topup', {
      by: "HitPayWebhook",
      id: transaction.id,
      data: transaction,
    });
  }

  res.status(200).end();
};

export default creditTopupNotify;
