import { CreditService } from "../services/credit-service";
import { TransactionType, TransactionStatus, TransactionSearchCriteria } from "../models/credit";
import { toTransactionJson } from "./shared/response-output";
import { getIdToken } from "../../init-openapi";
import { RequestHandler } from "express";
import logger from "@utils/logger";
import { Page } from "@shared/services";
import { isValid, parse } from "date-fns";

const creditService = new CreditService();

const creditTransactions: RequestHandler = async (req, res) => {
    try {
        const idToken = getIdToken(req);
        const uid = idToken.uid;

        const page = new Page(+req.params.page, +req.params.pageSize);

        const criteria: TransactionSearchCriteria = {
            memberId: uid,
        };

        if (req.query.types) {
            const typeStrings = (req.query.types as string).split(',');
            criteria.types = typeStrings.map(t => TransactionType[t as keyof typeof TransactionType]);
        }

        if (req.query.status) {
            const statusStrings = (req.query.status as string).split(',');
            criteria.status = statusStrings.map(s => TransactionStatus[s as keyof typeof TransactionStatus]);
        }

        if (req.query.startDate) {
            criteria.startDate = `${req.query.startDate} 00:00:00` as string;
            if (!isValid(parse(criteria.startDate, "yyyy-MM-dd HH:mm:ss", new Date()))) {
                throw new Error(`invalid date format: ${criteria.startDate}`);
            }
        }
        if (req.query.endDate) {
            criteria.endDate = `${req.query.endDate} 23:59:59` as string;
            if (!isValid(parse(criteria.endDate, "yyyy-MM-dd HH:mm:ss", new Date()))) {
                throw new Error(`invalid date format: ${criteria.endDate}`);
            }
        }

        const result = await creditService.findTransactions(criteria, page);
        res.status(200).json(result.items.map(e => toTransactionJson(e, { uid: uid, userTimezone: req.userTimezone })));
    } catch (e) {
        logger.error(e);
        res.status(e instanceof Error? 400: 500).json({ error: e instanceof Error ? e.message : 'Internal server error' });
    }
};

export default creditTransactions;