import { BookingCancelFindCriteria } from "@booking-api/models/booking";
import { BookingService } from "@booking-api/services/booking-service";
import { toBy } from "@shared/request-parsers";
import { EventCancelBy, EventCancelReq } from "@studio-cms/models/event";
import { RequestHand<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import eventEmitter from "@utils/event-emitter";

const bookingService = new BookingService();

const bookingCancel: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;
  const eventId = req.params.eventId!;

  const found = await bookingService.findBookingCancel({
    token: body.token!,
    reqBy: EventCancelBy.Member, // (!) important
    userId: uid!,
    eventId: eventId,
    isValidRange: true,
  } as BookingCancelFindCriteria);

  if (!found) {
    res.status(204).end("invalid token!");
    return;
  }

  await bookingService.cancel({
    eventId: req.params.eventId!,
    memberId: uid, // (!) important
    cancelBy: EventCancelBy.Member, // (!) important
    cancelFree: found.free,
    cancelReason: body.reason,
    by: toBy(idToken),
  } as EventCancelReq);

  const event = await bookingService.bookingById(eventId);
  if (event) {
    eventEmitter.emit('session-cancelled', {
      by: idToken.email,
      id: eventId,
      data: event,
    });
  }
  res.status(200).json();
};

export default bookingCancel;
