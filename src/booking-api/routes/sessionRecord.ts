import { SessionRecord, SessionRecordFlexi, SessionRecordSaveReq } from "@booking-api/models/session";
import { FcmMessage, FcmMessageCreateReq, FcmMessageStatus } from "@fcm/models/fcm-message";
import { FcmService } from "@fcm/services/fcm-service";
import { toBy } from "@shared/request-parsers";
import logger from "@utils/logger";
import { RequestHandler } from "express";
import { auth } from "firebase-admin";
import { getIdToken } from "../../init-openapi";
import { SessionService } from "../services/session-service";
import { DateTime } from 'luxon';
import DecodedIdToken = auth.DecodedIdToken;

const sessionService = new SessionService();
const fcmService = new FcmService();

const sessionRecord: RequestHandler = async (req, res) => {
  const recordMode = +process.env.SESSION_EXERCISE_MODE!;
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;

  const bookingId = req.params.sessionId!;
  const completed = body.completed;
  let record = null;
  switch (recordMode) {
    case 1:
      record = { exercises: body.exercises } as SessionRecord;
      break;
    case 2:
      record = { segments: body.segments } as SessionRecordFlexi
    default:
      break;
  }
  logger.debug(`uid=${uid}, completed=${completed}`);
  await sessionService.saveSessionRecord({
    bookingId: bookingId,
    instructorId: uid, // (!) important
    record: record,
    completed: body.completed === true,
    by: toBy(idToken),
  } as SessionRecordSaveReq);

  if (completed) {
    try {
      await scheduleFcmMessage(idToken, bookingId);
    } catch (err) {
      logger.error(`Error @ scheduleFcmMessage! ${JSON.stringify(err)}`);
    }
  }

  res.status(200).end();
};

async function scheduleFcmMessage(
  idToken: DecodedIdToken,
  bookingId: string,
) {
  const now = DateTime.now().setZone('UTC');
  const session = await sessionService.findByBookingId(bookingId);
  await fcmService.scheduleMessage({
    message: {
      userId: session!.memberId, // (!) send to member
      title: "Session complete!",
      body: "We hope you enjoyed your session!" +
        "\n\nShare your feedback to help us improve and enhance your experience.",
      go: `/bookings/${bookingId}?feedback=true`,
      at: now,
      scheduledAt: now,
      status: FcmMessageStatus.Scheduled,
    } as FcmMessage,
    by: idToken.email,
  } as FcmMessageCreateReq);
}

export default sessionRecord;
