import { BookingNoFeedbackSaveReq } from "@booking-api/models/booking";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { BookingService } from "../services/booking-service";

const bookingService = new BookingService();

const bookingNoFeedback: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const toSave = {
    eventId: req.params.eventId!,
    memberId: uid, // (!) important
    by: toBy(idToken),
  } as BookingNoFeedbackSaveReq;
  await bookingService.noFeedback(toSave);
  res.status(200).end();
};

export default bookingNoFeedback;
