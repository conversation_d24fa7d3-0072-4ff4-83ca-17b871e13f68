import { toBy } from "@shared/request-parsers";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { SessionNotesSaveReq } from "../models/session";
import { SessionService } from "../services/session-service";

const sessionService = new SessionService();

const sessionNotesSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;

  const key = body.key!;
  if (![
    'SessionState',
    'SessionTarget',
  ].includes(key)) {
    res.status(400).end('unsupported key');
    return;
  }

  const toSave = {
    bookingId: req.params.bookingId!.toString(),
    instructorId: uid, // (!) important
    value: body.value!,
    by: toBy(idToken),
  } as SessionNotesSaveReq;
  if (key === 'SessionState') {
    await sessionService.saveSessionState(toSave);
  } else if (key === 'SessionTarget') {
    await sessionService.saveSessionTarget(toSave);
  }

  res.status(200).end();
};

export default sessionNotesSave;
