import { Request<PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import {
  FcmMessage,
  FcmMessageCreateReq
} from "@fcm/models/fcm-message";
import { FcmService } from "@fcm/services/fcm-service";
import { addDays } from "date-fns";

const fcmService = new FcmService();

const bookingFeedbackLater: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const bookingId = req.params.eventId!;

  // Member Give Review Reminder
  await fcmService.scheduleSessionReviewReminderFcmMessage(idToken.email, uid, bookingId, 2);
  res.status(200).end();
};

export default bookingFeedbackLater;
