import { BookingFeedbackSaveReq } from "@booking-api/models/booking";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { BookingService } from "../services/booking-service";
import { SessionService } from "../services/session-service";
import eventEmitter from "@utils/event-emitter";

const bookingService = new BookingService();
const sessionService = new SessionService();

const bookingFeedback: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;
  const bookingId = req.params.eventId!;

  const toSave: BookingFeedbackSaveReq = {
    eventId: bookingId,
    memberId: uid, // (!) important
    feedback: body.feedback!?.toString().trim(),
    instructorFeedback: body.instructorFeedback!?.toString().trim(),
    studioFeedback: body.studioFeedback!?.toString().trim(),
    isPrivate: body.isPrivate || false, // Default to false if not provided
    instructorRating: body.instructorRating ?? 0,
    studioRating: body.studioRating ?? 0,
    by: toBy(idToken),
  };

  await bookingService.saveFeedback(toSave);
  const session = await sessionService.findByBookingId(bookingId);
    if (session) {
      eventEmitter.emit('session-feedback', {
        by: idToken.email,
        id: bookingId,
        data: session,
      });
    }
  res.status(200).end();
};

export default bookingFeedback;
