import { toExerciseCategoryJson } from "@booking-api/routes/shared/response-output";
import { ExerciseService } from "@booking-api/services/exercise-service";
import { RequestHandler } from "express";

const exerciseService = new ExerciseService();

const exerciseCategories: RequestHandler = async (req, res) => {
  // const idToken = getIdToken(req);
  // const uid = idToken.uid;

  const ret = await exerciseService.findCategories();
  res.status(200).json(
    ret.map(o => toExerciseCategoryJson(o))
  );
};

export default exerciseCategories;
