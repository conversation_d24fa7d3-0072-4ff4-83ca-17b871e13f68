import { ScheduleFindCriteria } from "@booking-api/models/booking";
import { BookingService } from "@booking-api/services/booking-service";
import { SessionService } from "@booking-api/services/session-service";
import { toEventJson } from "@studio-cms/routes/shared/response-output";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { Session } from "../models/session";

const bookingService = new BookingService();
const sessionService = new SessionService();

const scheduleByDay: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const day = req.query.day!.toString();
  const events = await bookingService.schedule({
    day: day,
    userId: uid,
  } as ScheduleFindCriteria);

  const sessionIds = events.map(events => events.id);
  const sessions = sessionIds.length? await sessionService.findByBookingIds(sessionIds): [];
  const sessionsMap = new Map<string, Session>();

  sessions.forEach((session) => {
    sessionsMap.set(session.bookingId, session);
  });

  const results = events.map(event => {
    event.session = sessionsMap.get(event.id);
    return event;
  });

  res.status(200).json(
    results.map(o => toEventJson(o, {
      includeInternalRemarks: false,
      uid: uid
    }))
  );
};

export default scheduleByDay;
