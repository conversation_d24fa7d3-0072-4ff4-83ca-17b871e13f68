import { SessionRecord, SessionRecordFlexi, SessionRecordSaveReq } from "@booking-api/models/session";
import { toBy } from "@shared/request-parsers";
import { isSessionRecord, isSessionRecordFlexi } from "./shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { SessionService } from "../services/session-service";
import logger from "@utils/logger";

const sessionService = new SessionService();

const sessionRecordSave: RequestHandler = async (req, res) => {
  const recordMode = +process.env.SESSION_EXERCISE_MODE!;
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const state = req.body?.state ?? "";
  const record = {...req.body};
  delete record.state;
  record.record_mode = recordMode;
  const bookingId = req.params.bookingId!.toString();

  // Validate record type based on mode
  if (recordMode === 1 && !isSessionRecord(record)) {
    res.status(400).json({ error: 'Invalid record format. Expected SessionRecord type.' });
    return;
  }
  if (recordMode === 2 && !isSessionRecordFlexi(record)) {
    res.status(400).json({ error: 'Invalid record format. Expected SessionRecordFlexi type.' });
    return;
  }

  try {
    const toSave = {
      bookingId: bookingId,
      instructorId: uid,
      sessionState: state,
      record: recordMode === 1 ? record as SessionRecord : record as SessionRecordFlexi,
      by: toBy(idToken),
    } as SessionRecordSaveReq;

    await sessionService.saveSessionRecord(toSave);
    res.status(200).end();
  } catch (error) {
    logger.error(`Error saving session record: ${error}`);
    res.status(500).json({ error: 'Failed to save session record' });
  }
};

export default sessionRecordSave;
