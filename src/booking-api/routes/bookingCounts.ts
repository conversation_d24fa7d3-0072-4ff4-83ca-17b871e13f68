import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { BookingService } from "../services/booking-service";
import { BookingFindCriteria } from "../models/booking";
import { getIdToken } from "../../init-openapi";

const bookingService = new BookingService();

const bookingCounts: RequestHandler = async (req, res) => {
    const idToken = getIdToken(req);
    const uid = idToken.uid;

    const upcomingAndOngoing =  req.query.upcomingAndOngoing + "";
    const upcomingOnly = req.query.upcomingOnly + "";
    const ongoingOnly = req.query.ongoingOnly + "";
    const pastOnly = req.query.pastOnly + "";
    const cancelledOnly = req.query.cancelledOnly + "";
    const feedbackOnly = req.query.feedbackOnly + "";

    const criteria = {
        memberId: uid,
        upcomingOnly: upcomingOnly === "true",
        ongoingOnly: ongoingOnly === "true",
        pastOnly: pastOnly === "true",
        cancelledOnly: cancelledOnly === "true",
        feedbackOnly:feedbackOnly === "true",
        upcomingAndOngoing: upcomingAndOngoing === "true",
    } as BookingFindCriteria;

    const service = new BookingService();
    const counts = await bookingService.bookingCounts(criteria);
    res.json(counts);
} 

export default bookingCounts;
