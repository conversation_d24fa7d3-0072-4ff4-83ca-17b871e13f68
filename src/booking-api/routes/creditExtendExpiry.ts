import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Router } from "express";
import { CreditService } from "../services/credit-service";
import { CreditExtendExpiry } from "../models/credit";
import { toTransactionJson } from "./shared/response-output";
import { toBy } from "@shared/request-parsers";
import { getIdToken } from "../../init-openapi";

const creditService = new CreditService();

const creditExtendExpiry: RequestHandler = async (req, res) => {
    try {
        const idToken = getIdToken(req);
        const creditExtendExpiryReq: CreditExtendExpiry = {
          months: req.body.months,
          by: toBy(idToken),
        };
        const transaction = await creditService.extendExpiry(creditExtendExpiryReq);
        res.json(toTransactionJson(transaction, { uid: idToken.uid, userTimezone: req.userTimezone }));
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
};


export default creditExtendExpiry;