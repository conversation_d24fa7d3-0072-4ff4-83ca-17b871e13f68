import { toBy } from "@shared/request-parsers";
import { BookingCancelReq } from "@booking-api/models/booking";
import { EventCancelBy } from "@studio-cms/models/event";
import { SessionService } from "@booking-api/services/session-service";
import { RequestHand<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
const sessionService = new SessionService();

const sessionCancelRequest: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;
  const eventId = req.params.eventId!;
  try {
    const result = await sessionService.cancelReq({
      reqBy: EventCancelBy.Instructor, // (!) important
      userId: uid, // (!) important
      eventId: eventId,
      reason: body.reason,
      by: toBy(idToken),
    } as BookingCancelReq);

    res.status(200).json({
      token: result.token,
      free: result.free,
    });
  } catch (error) {
    res.status(400).send(error)
  }
};

export default sessionCancelRequest;
