import { toEquipmentTypeJson } from "@booking-api/routes/shared/response-output";
import { SearchService } from "@booking-api/services/search-service";
import { RequestHandler } from "express";

const searchService = new SearchService();

const equipmentTypes: RequestHandler = async (req, res) => {
  // const idToken = getIdToken(req);
  // const uid = idToken.uid;

  const ret = await searchService.equipmentTypes();
  res.status(200).json(
    ret.map(o => toEquipmentTypeJson(o)) // (!) use the one in booking-api
  );
};

export default equipmentTypes;
