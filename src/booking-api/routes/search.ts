import { LatLng, SearchAt, SearchCriteria } from "@booking-api/models/search";
import { toSearchResultJson } from "@booking-api/routes/shared/response-output";
import { SearchService } from "@booking-api/services/search-service";
import { formatDate } from "date-fns";
import { RequestHandler } from "express";
import { getOptionalIdToken } from "../../init-openapi";

const searchService = new SearchService();

const search: RequestHandler = async (req, res) => {

  const latLng = new LatLng(
    +req.query.lat!.toString().trim(),
    +req.query.lng!.toString().trim(),
  );
  const distance = +req.query.distance!.toString().trim();
  const at = new SearchAt(
    req.query.date!.toString().trim(),
    req.query.time!.toString().trim(),
  );
  const studioId = req.query.studioId?.toString().trim();
  const instrId  = req.query.instrId?.toString().trim();
  const equipId  = req.query.equipId?.toString().trim();
  const equipTypes
    = req.query.equipTypes?.toString().trim()
    .split(",").map(o => o.trim());
  const instrLessOnly  = (req.query.instrLessOnly  + "") == "true";
  const faveStudioOnly = (req.query.faveStudioOnly + "") == "true";
  const faveInstrOnly  = (req.query.faveInstrOnly  + "") == "true";
  const preferredStudioFromFaveInstrOnly  = (req.query.preferredStudioFromFaveInstrOnly  + "") == "true";
  const extend  = (req.query.extend  + "") == "true";

  const searchCriteria = {
    latLng: latLng,
    distance: distance,
    at: at,
    studioId: studioId,
    instrId: instrId,
    equipId: equipId,
    equipTypes: equipTypes,
    instrLessOnly : instrLessOnly,
    faveStudioOnly: faveStudioOnly,
    faveInstrOnly : faveInstrOnly,
    preferredStudioFromFaveInstrOnly: preferredStudioFromFaveInstrOnly,
    byId: getOptionalIdToken(req)?.uid,
    timezone: req.userTimezone,
  } as SearchCriteria;

  if (extend) {
    const results = await searchService.searchExtended(searchCriteria);
    res.status(200).json({
      extendStart: results.extendStart,
      extendEnd: results.extendEnd,
      results: results.results
        .filter(o => !!o.studio.instrLess || o.groups.length !== 0)
        .map(o => toSearchResultJson(o))
    });
  } else {
    const results = await searchService.search(searchCriteria);
    res.status(200).json({
      results: results
        .filter(o => !!o.studio.instrLess || o.groups.length !== 0)
        .map(o => toSearchResultJson(o))
      });
  }
};

export default search;
