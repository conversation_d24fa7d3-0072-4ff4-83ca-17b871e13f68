import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import logger from "@utils/logger";
import { PromotionService } from "@booking-api/services/promotion-service";
import { toBy } from "@shared/request-parsers";
import { toTransactionJson } from "@booking-api/routes/shared/response-output";

const promotionService = new PromotionService();

const promoCodeApply: RequestHandler = async (req, res) => {
    const idToken = getIdToken(req);
    const by = idToken.email;
    const { promoCode } = req.body;

    if (!promoCode) {
        res.status(400).json({ message: "promoCode is required." });
        return;
    }

    logger.debug(`[${by}] applying topup promo code: ${promoCode}`);

    try {
        // This method will be implemented in the next steps.
        const transaction = await promotionService.applyTopupPromoCode(promoCode, toBy(idToken));
        res.status(200).json({
            success: true,
            message: "Promo code applied successfully.",
            data: toTransaction<PERSON>son(transaction, { uid: idToken.uid })
        });
    } catch (err) {
        logger.error(`[${by}] failed to apply topup promo code: ${err.message}`);
        res.status(400).json({ success: false, message: err.message });
    }
};

export default promoCodeApply;