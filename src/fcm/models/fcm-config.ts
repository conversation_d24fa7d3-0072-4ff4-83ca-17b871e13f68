import { By } from "@shared/models";

export interface FcmConfig {
  blackoutStart: string;
  blackoutEnd: string;
}

export interface FcmConfigSaveReq {
  userId: string;
  blackoutStart: string;
  blackoutEnd: string;
  by: string;
}

export interface FcmBlackoutSaveReq {
  userId: string;
  time: string;
  by: By;
}

export interface FcmBlackoutTimeSaveReq {
  userId: string;
  start: string;
  end: string;
  by: By;
}
