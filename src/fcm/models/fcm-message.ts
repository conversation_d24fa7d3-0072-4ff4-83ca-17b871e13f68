export enum FcmMessageStatus {
  Scheduled,
  DoNotSend,
  Sending,
  Sent,
  Failed,
}

const FcmMessageStatusMap = new Map<string, FcmMessageStatus>();
FcmMessageStatusMap.set("Scheduled", FcmMessageStatus.Scheduled);
FcmMessageStatusMap.set("DoNotSend", FcmMessageStatus.DoNotSend);
FcmMessageStatusMap.set("Sending",   FcmMessageStatus.Sending);
FcmMessageStatusMap.set("Sent",      FcmMessageStatus.Sent);
FcmMessageStatusMap.set("Failed",    FcmMessageStatus.Failed);

export class FcmMessageStatusHelper {
  static toFcmMessageStatus(str: string) {
    if (!FcmMessageStatusMap.has(str)) {
      throw `Invalid FcmMessageStatus! ${str}`;
    }
    return FcmMessageStatusMap.get(str);
  }
}

export interface FcmMessageFindCriteria {
  userId?: string;
  status?: FcmMessageStatus;
  scheduledToSend: boolean;
}

export interface FcmMessage {
  id: string;
  userId: string;
  title: string;
  body: string;
  go: string;
  at: Date;
  scheduledAt: Date;
  status: FcmMessageStatus;
  isRead?: boolean;
}

export interface FcmMessageStatusSetReq {
  messageId: string;
  userId: string;
  status: FcmMessageStatus;
  incrementAttempt: boolean;
  fcmResponse?: string;
  by: string;
}

export interface FcmMessageCreateReq {
  message: FcmMessage;
  by: string;
}

export interface FcmMessageRescheduleReq {
  message: FcmMessage;
  rescheduleTo: Date;
  by: string;
}
