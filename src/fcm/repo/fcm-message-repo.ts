import { Page, Sort } from "@shared/services";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import {
  FcmMessage,
  FcmMessageCreateReq,
  FcmMessageFindCriteria,
  FcmMessageRescheduleReq,
  FcmMessageStatus,
  FcmMessageStatusHelper,
  FcmMessageStatusSetReq
} from "../models/fcm-message";

export class FcmMessageRepo {

  async find(
    conn: Connection,
    criteria: FcmMessageFindCriteria,
    page: Page,
    sort?: Sort,
  ): Promise<FcmMessage[]> {
    const clauses = [];
    const params = [];
    if (criteria.userId) {
      clauses.push("(`user_id` = ?)");
      params.push(criteria.userId);
    }
    if (criteria.status != undefined) {
      clauses.push("(`status` = ?)");
      params.push(FcmMessageStatus[criteria.status]!);
    }
    if (criteria.scheduledToSend) {
      clauses.push("(`sched_at` <= now())");
    }
    { // deleted
      clauses.push("(deleted is false)");
    }

    let sql = "select * from fcm03messages";
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`
    }
    if (!sort) {
      sql += " order by `sched_at`"
    } else {
      const sortDir = sort.sortAsc ? 'asc' : 'desc';
      const lookup = new Map<string, string>();
      lookup.set("schedAt", ` order by sched_at ${sortDir}`);
      lookup.set("at"     , ` order by at ${sortDir}`);
      if (lookup.has(sort.sort)) {
        sql += lookup.get(sort.sort);
      }
    }
    sql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => this.toFcmMessage(o));
  }

  private toFcmMessage(row: any): FcmMessage {
    return {
      id: row['id'].toString(),
      userId: row['user_id'],
      title: row['title'],
      body: row['body'],
      go: row['go'],
      at: row['at'],
      scheduledAt: row['sched_at'],
      status: FcmMessageStatusHelper.toFcmMessageStatus(row['status']),
      isRead: row['read'] === 1,
    } as FcmMessage;
  }

  async getAttempts(
    conn: Connection,
    messageId: string,
  ): Promise<number> {
    const sql = "select attempts from fcm03messages where id = ?";
    const params = [messageId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    if (results.length != 1) throw `Message (id=${messageId}) not found!`;
    return +results[0]['attempts']!;
  }

  async setStatus(
    conn: Connection,
    req: FcmMessageStatusSetReq,
  ): Promise<number> {
    const by = req.by;
    const messageId = req.messageId;
    const messageStatus = FcmMessageStatus[req.status];
    const sql = `update fcm03messages set
      status = ?, attempts = attempts + ?,
      fcm_response = ?,
      updated_by = ?
      where id = ? and user_id = ? and status != ?`;
    const params = [
      messageStatus,
      req.incrementAttempt ? 1 : 0,
      req.fcmResponse ?? null,
      by,
      messageId,
      req.userId,
      messageStatus,
    ];

    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated fcmMessageStatus,`
      + ` messageId=${messageId}, status=${messageStatus}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async scheduleMessage(
    conn: Connection,
    req: FcmMessageCreateReq,
  ): Promise<void> {
    const by = req.by;
    const message = req.message;
    const sql = `insert into fcm03messages (
        user_id, title, body, go, at, sched_at, status, created_by
      ) values (
        ?, ?, ?, ?, ?, ?, ?, ?
      )`;
    const params = [
      message.userId,
      message.title!,
      message.body ?? null,
      message.go ?? null,
      message.at,
      message.scheduledAt,
      FcmMessageStatus[message.status!],
      by,
    ];

    /*
    logger.debug(`sql=${sql}`)
    logger.debug(`params=${params}`)
    */
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] scheduled fcmMessage, affectedRows=${affectedRows}`);
  }

  async rescheduleMessage(
    conn: Connection,
    req: FcmMessageRescheduleReq,
  ): Promise<void> {
    const by = req.by;
    const message = req.message;
    const sql = `update fcm03messages
      set sched_at = ?, updated_by = ?
      where id = ?`;
    const params = [
      req.rescheduleTo!,
      by, // updated_by
      message.id!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] rescheduleMessage()`
      + ` rescheduleTo=${req.rescheduleTo!}, affectedRows=${affectedRows}`);
  }

  async markAsRead(conn: Connection, messageId: string, userId: string, by: string): Promise<number> {
    const sql = "update fcm03messages set `read` = 1, read_at = now(), updated_by = ? where id = ? and user_id = ?";
    logger.debug(`[${by}] markAsRead() messageId=${messageId}, userId=${userId}`);
    const params = [by, messageId, userId];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] markAsRead() messageId=${messageId}, userId=${userId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async getUnreadCount(conn: Connection, userId: string): Promise<number> {
    const sql = "select count(*) as count from fcm03messages where user_id = ? and `read` = 0 and deleted is false and sched_at <= now()";
    const params = [userId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return +results[0]['count'];
  }
}
