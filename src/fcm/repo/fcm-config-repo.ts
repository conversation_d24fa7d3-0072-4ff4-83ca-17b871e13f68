import { FcmBlackoutSaveReq, FcmBlackoutTimeSaveReq, FcmConfig, FcmConfigSaveReq } from "@fcm/models/fcm-config";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";

export class FcmConfigRepo {

  async findConfigByUserId(
    conn: Connection,
    userId: string,
  ): Promise<FcmConfig|undefined> {
    const sql = "select * from fcm02config where user_id = ?";
    const params = [userId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.length == 0 ? undefined : this.toFcmConfig(results[0]);
  }

  private toFcmConfig(row: any): FcmConfig {
    return {
      blackoutStart: row["blackout_start"]!,
      blackoutEnd: row["blackout_end"]!,
    } as FcmConfig;
  }

  async createConfig(
    conn: Connection,
    req: FcmConfigSaveReq,
  ): Promise<number> {
    const sql = `insert into fcm02config (
        user_id, blackout_start, blackout_end,
        created_by
      ) values (
        ?, ?, ?, ?
      )`;
    const params = [
      req.userId!,
      req.blackoutStart!,
      req.blackoutEnd!,
      req.by!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${req.by}] createConfig() userId=${req.userId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setBlackoutStart(
    conn: Connection,
    req: FcmBlackoutSaveReq,
  ): Promise<number> {
    const sql = `update fcm02config
      set blackout_start = ?, updated_by = ?
      where user_id = ?`;
    const by = req.by.username;
    const params = [
      req.time!,
      by!,
      req.userId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] setBlackoutStart() userId=${req.userId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setBlackoutEnd(
    conn: Connection,
    req: FcmBlackoutSaveReq,
  ): Promise<number> {
    const sql = `update fcm02config
      set blackout_end = ?, updated_by = ?
      where user_id = ?`;
    const by = req.by.username;
    const params = [
      req.time!,
      by!,
      req.userId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] setBlackoutEnd() userId=${req.userId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setBlackoutTime(
    conn: Connection,
    req: FcmBlackoutTimeSaveReq,
  ): Promise<number> {
    const sql = `update fcm02config
      set blackout_start = ?, blackout_end = ?, updated_by = ?
      where user_id = ?`;
    const by = req.by.username;
    const params = [
      req.start!,
      req.end!,
      by!,
      req.userId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] setBlackoutTime() userId=${req.userId}, numRows=${affectedRows}`);
    return affectedRows;
  }
}
