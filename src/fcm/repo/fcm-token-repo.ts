import { FcmToken, FcmTokenSaveReq } from "@fcm/models/fcm-token";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";

export class FcmTokenRepo {

  async findTokenByUserId(conn: Connection, userId: string): Promise<FcmToken|undefined> {
    const sql = "select * from fcm01tokens where user_id = ?";
    const params = [userId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toFcmToken(results[0]);
  }

  private toFcmToken(row: any): FcmToken {
    return {
      userId: row['user_id'],
      token: row['token'],
    } as FcmToken;
  }

  async createToken(conn: Connection, req: FcmTokenSaveReq): Promise<void> {
    const sql = `insert into fcm01tokens (
        user_id, token,
        created_by
      ) values (
        ?, ?, ?
      )`;
    const by = req.by.username;
    const token = req.token;
    const params = [
      token.userId,
      token.token,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created token, userId=${token.userId}, numRows=${results.affectedRows}`);
  }

  async deleteToken(conn: Connection, req: FcmTokenSaveReq): Promise<number> {
    const sql = "delete from fcm01tokens where user_id = ?";
    const by = req.by.username;
    const userId = req.token.userId;
    const params = [userId];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] deleted token, userId=${userId}, numRows=${affectedRows}`);
    return affectedRows;
  }
}
