import { FcmBlackoutSaveReq, FcmBlackoutTimeSaveReq, FcmConfig, FcmConfigSaveReq } from "@fcm/models/fcm-config";
import {
  FcmMessage,
  FcmMessageCreateReq,
  FcmMessageFindCriteria,
  FcmMessageRescheduleReq,
  FcmMessageStatus,
  FcmMessageStatusSetReq
} from "@fcm/models/fcm-message";
import { Event } from "@studio-cms/models/event";
import { Session } from "@booking-api/models/session";
import { FcmToken, FcmTokenSaveReq } from "@fcm/models/fcm-token";
import { FcmConfigRepo } from "@fcm/repo/fcm-config-repo";
import { FcmMessageRepo } from "@fcm/repo/fcm-message-repo";
import { FcmTokenRepo } from "@fcm/repo/fcm-token-repo";
import { Page, Sort } from "@shared/services";
import logger from "@utils/logger";
import pool from "../../init-pool";
import { addDays, sub, formatDate } from "date-fns";
import { auth } from "firebase-admin";
import DecodedIdToken = auth.DecodedIdToken;
import crypto from "crypto";
import { DateTime } from "luxon";

export class FcmService {

  private tokenRepo = new FcmTokenRepo();
  private messageRepo = new FcmMessageRepo();
  private configRepo = new FcmConfigRepo();

  ////////////
  // Tokens //
  ////////////

  async findTokenByUserId(userId: string): Promise<FcmToken|undefined> {
    return this.tokenRepo.findTokenByUserId(pool, userId);
  }

  async setToken(req: FcmTokenSaveReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();
      await this.tokenRepo.deleteToken(conn, req);
      await this.tokenRepo.createToken(conn, req);
      await conn.commit();
      logger.info(`setToken success, userId=${req.token.userId}`);
    } catch (e) {
      logger.error(`setToken error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async removeToken(req: FcmTokenSaveReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();
      await this.tokenRepo.deleteToken(conn, req);
      await conn.commit();
      logger.info(`removeToken success, userId=${req.token.userId}`);
    } catch (e) {
      logger.error(`removeToken error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  ////////////
  // Config //
  ////////////

  async findFcmConfigByUserId(userId: string): Promise<FcmConfig> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      let config = await this.configRepo.findConfigByUserId(conn, userId);
      if (!config) {
        await this.configRepo.createConfig(conn, {
          userId: userId,
          blackoutStart: "21:00:00",
          blackoutEnd: "07:00:00",
          by: "system",
        } as FcmConfigSaveReq);
        config = await this.configRepo.findConfigByUserId(conn, userId);
      }

      await conn.commit();
      return config!;
    } catch (e) {
      logger.error(`findFcmConfigByUserId() error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async setBlackoutStart(req: FcmBlackoutSaveReq): Promise<number> {
    return this.configRepo.setBlackoutStart(pool, req);
  }

  async setBlackoutEnd(req: FcmBlackoutSaveReq): Promise<number> {
    return this.configRepo.setBlackoutEnd(pool, req);
  }

  async setBlackoutTime(req: FcmBlackoutTimeSaveReq): Promise<number> {
    return this.configRepo.setBlackoutTime(pool, req);
  }

  //////////////
  // Messages //
  //////////////

  async findMessages(
    criteria: FcmMessageFindCriteria,
    page: Page,
    sort?: Sort,
  ): Promise<FcmMessage[]> {
    return this.messageRepo.find(pool, criteria, page, sort);
  }

  async getAttempts(messageId: string): Promise<number> {
    return this.messageRepo.getAttempts(pool, messageId);
  }

  async setStatus(req: FcmMessageStatusSetReq): Promise<number> {
    return this.messageRepo.setStatus(pool, req);
  }

  async scheduleMessage(
    req: FcmMessageCreateReq,
  ): Promise<void> {
    return this.messageRepo.scheduleMessage(pool, req);
  }

  async rescheduleMessage(
    req: FcmMessageRescheduleReq,
  ): Promise<void> {
    return this.messageRepo.rescheduleMessage(pool, req);
  }

  ///////////////////////
  // Messages Template //
  ///////////////////////
  async scheduleTestNotifyFcmMessage(
    idToken: DecodedIdToken,
    uid: string,
    title?: string,
    body?: string,
    go?: string,
    timezone?: string,
  ) {
    const now = DateTime.now().setZone(timezone || 'UTC');
    await this.scheduleMessage({
      message: {
        userId: uid, // (!) send to member
        title: title || "Testing",
        body: body || "Testing Body",
        go: go,
        at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        status: FcmMessageStatus.Scheduled,
      } as FcmMessage,
      by: idToken.email,
    } as FcmMessageCreateReq);
  }

  /**
  *
  * @param by - Created By
  * @param uid - Target Id
  * @param event - Event (Booking) Object
  * @param type - 1 - Member, 2 - Instructor
  */
  async scheduleSessionConfirmationNotifyFcmMessage(
    by: string,
    uid: string,
    bookingId: string,
    type: number
  ) {

    const now = DateTime.now().setZone('UTC');
    let messageReq: FcmMessageCreateReq = {
      message: {
        userId: uid, // (!) send to member
        at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        status: FcmMessageStatus.Scheduled,
      } as FcmMessage,
      by: by,
    };
    switch (type) {
      case 1:
        messageReq.message = {
          ...messageReq.message,
          ...{
            title: "You're in! Booking confirmed.",
            body: "Your Pilates session has been successfully booked. Your door PIN will activate 15 mins before class and can be accessed via your booking page. See you there.",
            go: `/my_booking_detail?id=${bookingId}`,
          }
        }
        break;
      case 2:
        messageReq.message = {
          ...messageReq.message,
          ...{
            title: "New Pilates session booked!",
            body: "A member has booked a Pilates session with you. Check your booking schedule for the session details. ",
            go: `/client_booking_detail?id=${bookingId}`,
          }
        }
          break;
      default:
        break;
    }
    messageReq && await this.scheduleMessage(messageReq);
  }

  /**
    *
    * @param by - Created By
    * @param uid - Target Id
    * @param event - Event (Booking) Object
    * @param type - 1 - 1 day before (Member), 2 - 1 hour before (Member), 3 - 1 day before (Instructor), 4 - 1 hour before (Instructor)
    */
  async scheduleSessionReminderFcmMessage(
    by: string,
    uid: string,
    event: Event,
    type: number
  ) {
    const startDateTime = DateTime.fromFormat(`${event.startDate} ${event.startTime}`, 'yyyy-MM-dd HH:mm:ss', { zone: event.timezone }).setZone('UTC');

    let messageReq: FcmMessageCreateReq;
    switch (type) {
      case 1:
        messageReq = {
          message: {
            userId: uid, // (!) send to member
            at: startDateTime.minus({
              days: 1,
            }).toFormat('yyyy-MM-dd HH:mm:ss'),
            scheduledAt: startDateTime.minus({
              days: 1,
            }).toFormat('yyyy-MM-dd HH:mm:ss'),
            title: "Get ready for your upcoming Pilates session.",
            body: "Your Pilates session is coming up soon! Check your schedule and get ready to move. You can find your door access PIN on the booking page.",
            go: `/my_booking_detail?id=${event.id}`,
            status: FcmMessageStatus.Scheduled,
          } as FcmMessage,
          by: by,
        } as FcmMessageCreateReq
        break;
      case 2:
          messageReq = {
            message: {
              userId: uid, // (!) send to member
              at: startDateTime.minus({
                hours: 1,
              }).toFormat('yyyy-MM-dd HH:mm:ss'),
              scheduledAt: startDateTime.minus({
                hours: 1,
              }).toFormat('yyyy-MM-dd HH:mm:ss'),
              title: "Your Pilates session starts soon.",
              body: "You have got a session starting in an hour. We will see you soon - arrive a little early to settle in.",
              go: `/my_booking_detail?id=${event.id}`,
              status: FcmMessageStatus.Scheduled,
            } as FcmMessage,
            by: by,
          } as FcmMessageCreateReq
          break;
      case 3:
        messageReq = {
          message: {
            userId: uid, // (!) send to member
            at: startDateTime.minus({
              days: 1,
            }).toFormat('yyyy-MM-dd HH:mm:ss'),
            scheduledAt: startDateTime.minus({
              days: 1,
            }).toFormat('yyyy-MM-dd HH:mm:ss'),
            title: "Class scheduled for tomorrow.",
            body: "You're scheduled for a Pilates session tomorrow. Get ready by reviewing your session details. You can find the door access PIN on the booking schedule.",
            go: `/client_booking_detail?id=${event.id}`,
            status: FcmMessageStatus.Scheduled,
          } as FcmMessage,
          by: by,
        } as FcmMessageCreateReq
        break;
      case 4:
          messageReq = {
            message: {
              userId: uid, // (!) send to member
              at: startDateTime.minus({
                hours: 1,
              }).toFormat('yyyy-MM-dd HH:mm:ss'),
              scheduledAt: startDateTime.minus({
                hours: 1,
              }).toFormat('yyyy-MM-dd HH:mm:ss'),
              title: "Class begins in just an hour. Let's get ready!",
              body: "You have got a session starting in an hour. Make sure you're all set to guide your member.",
              go: `/client_booking_detail?id=${event.id}`,
              status: FcmMessageStatus.Scheduled,
            } as FcmMessage,
            by: by,
          } as FcmMessageCreateReq
          break;
      default:
        break;
    }
    messageReq && await this.scheduleMessage(messageReq);
  }

  /**
  *
  * @param by - Created By
  * @param uid - Target Id
  * @param event - Event (Booking) Object
  * @param type - 1 - Member Cancelled (Member), 2 - Member Cancelled (Instructor), 3 - Instructor Confirmed
  */
  async scheduleSessionCancelledFcmMessage(
    by: string,
    uid: string,
    event: Event,
    type: number
  ) {
    const now = DateTime.now().setZone('UTC');
    let messageReq: FcmMessageCreateReq = {
      message: {
        userId: uid, // (!) send to member
        at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        status: FcmMessageStatus.Scheduled,
      } as FcmMessage,
      by: by,
    };
    switch (type) {
      case 1:
        messageReq.message = {
          ...messageReq.message,
          ...event.cancelledWithRefund ? {
            title: "We're sorry, your Pilates session was cancelled.",
            body: "Unfortunately, your upcoming Pilates session has been cancelled. We have refunded the VP to your account. We hope to see you back in the studio soon!",
            go: `/my_booking_detail?id=${event.id}`,
          }: {
            title: "Your session was cancelled. Let's find your next one.",
            body: "You've successfully cancelled your upcoming Pilates session. As this booking isn't eligible for a refund under our cancellation policy, we're here to help you find your next perfect session. Ready when you are!",
            go: `/my_booking_detail?id=${event.id}`,
          }
        }
        break;
      case 2:
          messageReq.message = {
            ...messageReq.message,
            ...{
              title: "We're sorry, your class was cancelled.",
              body: "Your upcoming Pilates session has been cancelled. We look forward to seeing you at your next session.",
              go: `/client_booking_detail?id=${event.id}`,
            }
          }
          break;
      case 3:
        messageReq.message = {
          ...messageReq.message,
          ...{
            title: "Your booking cancellation has been approved.",
            body: "Your booking cancellation has been approved. We look forward to seeing you in your next class!",
            go: `/client_booking_detail?id=${event.id}&tabIndex=2`,
          }
        }
        break;
      default:
        break;
    }
    messageReq && await this.scheduleMessage(messageReq);
  }

  // Member - Instructor give session records and feedback notify
  async scheduleSessionFeedbackNotifyFcmMessage(
    by: string,
    uid: string,
    bookingId: string,
  ) {
    const now = DateTime.now().setZone('UTC');
    await this.scheduleMessage({
      message: {
        userId: uid, // (!) send to member
        title: "You're making progress - your post session feedback is ready",
        body: "Your instructor has left feedback on your recent Pilates session. You can now view your feedback and track your progress in the app.",
        go: `/my_booking_detail?id=${bookingId}&tabIndex=2`,
        at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        status: FcmMessageStatus.Scheduled,
      } as FcmMessage,
      by: by,
    } as FcmMessageCreateReq);
  }

  // Instructor - Instructor give session records and feedback reminder
  async scheduleSessionFeedbackRemindFcmMessage(
    by: string,
    uid: string,
    bookingId: string,
    type: number
  ) {
    const now = DateTime.now().setZone('UTC');
    let messageReq: FcmMessageCreateReq = {
      message: {
        userId: uid, // (!) send to member
        at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        status: FcmMessageStatus.Scheduled,
      } as FcmMessage,
      by: by,
    };
    switch (type) {
      case 1:
        messageReq.message = {
          ...messageReq.message,
          ...{
            title: "Time to leave session record for your members! ",
            body: "A friendly reminder to leave session record for your member and wrap up the session!",
            go: `/client_booking_detail?id=${bookingId}&tabIndex=2`,
          }
        }
        break;
      case 2:
          messageReq.message = {
            ...messageReq.message,
            ...{
              title: "Reminder to complete your session record.",
              body: "Your session record helps members improve and feel supported in their Pilates journey. Take a moment to complete your session record.",
              scheduledAt: now.plus({
                days: 1,
              }).toFormat('yyyy-MM-dd HH:mm:ss'),
              go: `/client_booking_detail?id=${bookingId}&tabIndex=2`,
            }
          }
          break;
      default:
        break;
    }
    messageReq && await this.scheduleMessage(messageReq);
  }

  async scheduleSessionReviewNotifyFcmMessage(
    by: string,
    uid: string,
    bookingId: string,
  ) {
    const now = DateTime.now().setZone('UTC');
    await this.scheduleMessage({
      message: {
        userId: uid, // (!) send to instructor
        title: "You've received a new review.",
        body: "A member has left a review for your recent session. Check out their comments!",
        go: `/client_booking_detail?id=${bookingId}&tabIndex=3`,
        at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        status: FcmMessageStatus.Scheduled,
      } as FcmMessage,
      by: by,
    } as FcmMessageCreateReq);
  }

  /**
  * Member Give Review Reminder
  * @param by - Created By
  * @param uid - Target Member Id
  * @param bookingId - Event (Booking) Id
  * @param type - 1 - After Session Completed, 2 - Member Press Maybe Later (When Ask For Give Review)
  */
  async scheduleSessionReviewReminderFcmMessage(
    by: string,
    uid: string,
    bookingId: string,
    type: number = 1
  ) {
    const now = DateTime.now().setZone('UTC');
    let messageReq: FcmMessageCreateReq = {
      message: {
        userId: uid, // (!) send to member
        at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        status: FcmMessageStatus.Scheduled,
      } as FcmMessage,
      by: by,
    };
    switch (type) {
      case 1:
        messageReq.message = {
          ...messageReq.message,
          ...{
            title: "Rate your Pilates experience",
            body: "We'd love to hear how your Pilates session went. Leave a rating and review for your instructor and studio.",
            go: `/my_booking_detail?id=${bookingId}&tabIndex=1`,
          }
        }
        break;
      case 2:
          messageReq.message = {
            ...messageReq.message,
            ...{
              title: "How did your session go? Let us know!",
              body: "Your review matters! Take a moment to rate and review your instructor and studio to help us improve.",
              scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
              go: `/my_booking_detail?id=${bookingId}&tabIndex=1`,
            }
          }
          break;
      default:
        break;
    }
    messageReq && await this.scheduleMessage(messageReq);
  }

  async scheduleInstructorProfileApprovalNotifyFcmMessage(
    by: string,
    uid: string
  ) {

    const now = DateTime.now().setZone('UTC');
    await this.scheduleMessage({
      message: {
        userId: uid, // (!) send to instructor
        title: "Congrats, your instructor profile has been approved!",
        body: "You can now start accepting bookings and get connected to the Pilates community.",
        at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        status: FcmMessageStatus.Scheduled,
      } as FcmMessage,
      by: by,
    } as FcmMessageCreateReq);
  }

  /**
  * Instructor Reminder
  * @param by - Created By
  * @param uid - Target Instructor Id
  * @param type - 1 - Instructor Press Maybe Later (Remind after 3 days if incomplete yet)
  */
  async scheduleInstructorProfileCompleteReminderFcmMessage(
    by: string,
    uid: string,
    type: number = 1
  ) {
    const now = DateTime.now().setZone('UTC');
    let messageReq: FcmMessageCreateReq = {
      message: {
        userId: uid, // (!) send to member
        at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        status: FcmMessageStatus.Scheduled,
      } as FcmMessage,
      by: by,
    };
    switch (type) {
      case 1:
        messageReq.message = {
          ...messageReq.message,
          ...{
            title: "Almost done! Complete your instructor profile.",
            body: "We noticed you have not finished setting up your instructor profile. Take a moment to fill it out and start connecting with members!",
            go: '/setup_instructor_profile',
          }
        }
        break;
      default:
        break;
    }
    messageReq && await this.scheduleMessage(messageReq);
  }

  async scheduleTopupSuccessfulNotifyFcmMessage(
    by: string,
    uid: string
  ) {
    const now = DateTime.now().setZone('UTC');
    await this.scheduleMessage({
      message: {
        userId: uid, // (!) send to member
        title: "VP top up successful",
        body: "Your top up was successful. Time to schedule your next Pilates session!",
        at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        status: FcmMessageStatus.Scheduled,
      } as FcmMessage,
      by: by,
    } as FcmMessageCreateReq);
  }

  async scheduleCreditExpiredNotifyFcmMessage(by: string, uid: string): Promise<void> {
    const now = DateTime.now().setZone('UTC');
    const message = {
        userId: uid,
        title: "Your VP are expiring soon. Use them before they're gone!",
        body: "You have 1 month left to use your VP, be sure to book your next Pilates class before they expire. Want to keep the momentum going? Top up now and continue enjoying your favorite sessions without interruption.",
        at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
        status: FcmMessageStatus.Scheduled,
    } as FcmMessage;
    await this.scheduleMessage({
        message,
        by
    } as FcmMessageCreateReq);
  }

  async markAsRead(messageId: string, userId: string, by: string): Promise<number> {
    return this.messageRepo.markAsRead(pool, messageId, userId, by);
  }

  async getUnreadCount(userId: string): Promise<number> {
    return this.messageRepo.getUnreadCount(pool, userId);
  }
}
