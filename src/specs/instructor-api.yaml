openapi: 3.0.3
info:
  version: 1.0.0-SNAPSHOT
  title: Instructor API
security:
  - BearerAuth: []
servers:
  - url: /api/instructor
paths:
  # Manage Bank Account
  /bank-accounts/options:
    get:
      summary: Bank account form option
      operationId: bankAccountOption
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts/list/{page}/{pageSize}/{sort}/{sortDir}:
    get:
      summary: List bank accounts
      operationId: bankAccountList
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/sortDir'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts:
    post:
      summary: Create bank account
      operationId: bankAccountCreate
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BankAccount'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts/{id}:
    get:
      summary: Get bank account by ID
      operationId: bankAccountGetById
      parameters:
        - $ref: '#/components/parameters/id'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
    put:
      summary: Update bank account
      operationId: bankAccountUpdate
      parameters:
        - $ref: '#/components/parameters/id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BankAccount'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts/{id}/status:
    patch:
      summary: Update bank account status
      operationId: bankAccountUpdateStatus
      parameters:
        - $ref: '#/components/parameters/id'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum:
                    - "1"
                    - "0"
              required:
                - status
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'

  /exists:
    get:
      summary: exists?
      operationId: exists
      responses:
        "200":
          description: Success
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /register:
    post:
      summary: Register
      operationId: register
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Register'
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /registerV2:
    post:
      summary: Register V2
      operationId: registerV2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterV2'
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /myProfile:
    get:
      summary: My Account
      operationId: myProfile
      responses:
        "200":
          description: Success
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /myProfile/save:
    post:
      summary: My Account / Register (Step 2) > Save
      operationId: myProfileSave
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MyProfile'
      responses:
        "200":
          description: Success
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /myProfile/save/equipments:
    post:
      summary: Register (Step 3) > Save Instructor's Familiar Equipments
      operationId: myProfileEquipmentsSave
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                equipmentTypes:
                  type: array
                  items:
                    type: string
                    maxLength: 5
      responses:
        "200":
          description: Success
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /myProfile/complete:
    post:
      summary: Register (Step 4) > Complete Instructor's Profile (Pictures)
      operationId: myProfileComplete
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                pictures:
                  type: array
                  items:
                    type: string
                    maxLength: 200
      responses:
        "200":
          description: Success
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /myProfile/picture/upload:
    post:
      summary: Upload picture for instructor
      operationId: myProfilePictureUpload
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  nullable: false
              required:
                - file
      responses:
        "200":
          description: Success
          content:
            text/plain:
              schema:
                type: string
                description: Picture ID
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /myProfile/picture/get/{pictureId}:
    get:
      summary: Get picture
      operationId: myProfilePictureGet
      security: [] # (!) NO SECURITY
      parameters:
        - name: pictureId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #########
  # Rates #
  #########
  /rates:
    get:
      summary: Get rates
      operationId: rates
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /rates/save:
    post:
      summary: Save rates
      operationId: ratesSave
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/Rates"
              maxItems: 3
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  ##############
  # Work Hours #
  ##############
  /work-hours:
    get:
      summary: Get work hours
      operationId: workHours
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /work-hours/save:
    post:
      summary: Save work hours
      operationId: workHoursSave
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/WorkHours"
              maxItems: 3
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  ############
  # Mobility #
  ############
  /mobility:
    get:
      summary: Get mobility
      operationId: mobility
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /mobility/save:
    post:
      summary: Save mobility
      operationId: mobilitySave
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/Mobility"
              maxItems: 7
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  #############
  # Blackouts #
  #############
  /blackouts/calendar:
    get:
      summary: Get blackouts calendar
      operationId: blackoutsCalendar
      parameters:
        - name: year
          in: query
          required: true
          schema:
            type: integer
            minLength: 4
            maxLength: 4
        - name: month
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 12
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /blackouts/byDay:
    get:
      summary: Get blackouts by day
      operationId: blackoutsByDay
      parameters:
        - name: day
          in: query
          required: true
          schema:
            type: string
            format: date
            minLength: 10
            maxLength: 10
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /blackouts/save:
    post:
      summary: Save blackout
      operationId: blackoutSave
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Blackout"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /blackouts/delete/{id}:
    get:
      summary: Delete blackout
      operationId: blackoutDelete
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  #####################
  # Preferred Studios #
  #####################
  /prefStudios:
    get:
      summary: Get preferred studios
      operationId: prefStudios
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /prefStudios/has/{studioId}:
    get:
      summary: Has preferred studio
      operationId: prefStudioHas
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /prefStudios/add/{studioId}:
    get:
      summary: Add preferred studio
      operationId: prefStudioAdd
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /prefStudios/remove/{studioId}:
    get:
      summary: Remove preferred studio
      operationId: prefStudioRemove
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
  schemas:
    Register:
      type: object
      properties:
        name:
          type: string
          maxLength: 200
        descr:
          type: string
          maxLength: 10000
        facebook:
          type: string
          maxLength: 200
        instagram:
          type: string
          maxLength: 200
        certifications:
          type: string
          maxLength: 10000
        specialisations:
          type: string
          maxLength: 10000
        pictures:
          type: array
          items:
            type: string
            maxLength: 200
        isCompleted:
          type: boolean
        equipmentTypes:
          type: array
          items:
            type: string
            maxLength: 50
      required:
        - name
    RegisterV2:
      type: object
      properties:
        fullName:
          type: string
          maxLength: 200
        gender:
          type: string
          enum:
            - Female
            - Male
            - RatherNotSay
      required:
        - fullName
        - gender
    MyProfile:
      $ref: "#/components/schemas/Register"
    Rate:
      type: object
      properties:
        start:
          type: string
          minLength: 5
          maxLength: 5
          description: Time in HH:mm (24h) format, eg. 13:30
        end:
          type: string
          minLength: 5
          maxLength: 5
          description: Time in HH:mm (24h) format, eg. 18:30
        price:
          type: string
          pattern: '^\d+(?:\.\d{0,2})?$'
          maxLength: 10
          description: Numeric value, up to 2 decimal places
      required:
        - start
        - end
        - price
    Rates:
      type: object
      properties:
        type:
          type: string
          enum:
            - ALL
            - WD
            - WE
            - PH
        rates:
          type: array
          minItems: 0
          maxItems: 4
          items:
            $ref: "#/components/schemas/Rate"
      required:
        - type
        - rates
    WorkHoursSlot:
      type: object
      properties:
        start:
          type: string
          minLength: 5
          maxLength: 5
          description: Time in HH:mm (24h) format, eg. 13:30
        end:
          type: string
          minLength: 5
          maxLength: 5
          description: Time in HH:mm (24h) format, eg. 18:30
      required:
        - start
        - end
    WorkHours:
      type: object
      properties:
        type:
          type: string
          enum:
            - ALL
            - WD
            - WE
            - PH
        slots:
          type: array
          minItems: 0
          maxItems: 4
          items:
            $ref: "#/components/schemas/WorkHoursSlot"
      required:
        - type
        - slots
    Mobility:
      type: object
      properties:
        dow:
          type: integer
          minimum: 1
          maximum: 7
        locationId:
          type: string
          maxLength: 50
        distance:
          type: integer
          minimum: 0
      required:
        - dow
        - locationId
        - distance
    Blackout:
      type: object
      properties:
        id:
          type: string
          maxLength: 50
          nullable: true
        name:
          type: string
          maxLength: 100
        startDate:
          type: string
          format: date
          minLength: 10
          maxLength: 10
        startTime:
          type: string
          minLength: 5
          maxLength: 5
          nullable: true
        endDate:
          type: string
          format: date
          minLength: 10
          maxLength: 10
        endTime:
          type: string
          minLength: 5
          maxLength: 5
          nullable: true
        allDay:
          type: boolean
          nullable: true
      required:
        - name
        - startDate
        - endDate
    BankAccount:
      type: object
      properties:
        bankId:
          type: string
          format: uuid
        accountNo:
          type: string
          maxLength: 100
        accountName:
          type: string
          maxLength: 100
        swiftCode:
          type: string
          maxLength: 100
        primary:
          type: boolean
      required:
        - bankId
        - accountNo
        - accountName
        - primary
  parameters:
    id:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
    page:
      name: page
      in: path
      required: true
      schema:
        type: integer
        minimum: 1
        default: 1
    pageSize:
      name: pageSize
      in: path
      required: true
      schema:
        type: integer
        minimum: 1
        default: 25
    sort:
      name: sort
      in: path
      required: true
      schema:
        type: string
    sortDir:
      name: sortDir
      in: path
      required: true
      schema:
        type: string
        enum: [asc, desc]
  responses:
    SuccessResponse:
      description: Success
    BadRequestResponse:
      description: Bad Request
    UnauthorizedResponse:
      description: Unauthorized
    InternalServerErrorResponse:
      description: Internal Server Error
