openapi: 3.0.3
info:
  version: 1.0.0-SNAPSHOT
  title: Public API
servers:
  - url: /api/public
paths:
  /health:
    get:
      summary: Health check
      operationId: health
      responses:
        "200":
          description: Success
  /studios/{page}/{pageSize}:
    get:
      summary: Studios
      operationId: studios
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: searchTerm
          in: query
          required: false
          schema:
            type: string
            maxLength: 100
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /studios/get/{studioId}:
    get:
      summary: Studio by ID
      operationId: studioById
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /studios/get/{studioId}/feedback/{page}/{pageSize}:
    get:
      summary: Studio Feedback by ID
      operationId: studioFeedbackById
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /instructors/{page}/{pageSize}:
    get:
      summary: Instructors
      operationId: instructors
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: searchTerm
          in: query
          required: false
          schema:
            type: string
            maxLength: 100
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /instructors/get/{instructorId}:
    get:
      summary: Instructor by ID
      operationId: instructorById
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      parameters:
        - name: instructorId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /instructors/get/{instructorId}/feedback/{page}/{pageSize}:
    get:
      summary: Instructor Feedback by ID
      operationId: instructorFeedbackById
      parameters:
        - name: instructorId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /messageByCode:
    get:
      summary: Message by code
      operationId: messageByCode
      parameters:
        - name: code
          in: query
          required: true
          schema:
            type: string
            maxLength: 100
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  ############
  # Location #
  ############
  /location/autocomplete:
    get:
      summary: Location autocomplete
      operationId: locationAutocomplete
      parameters:
        - name: input
          in: query
          required: true
          allowReserved: true
          schema:
            type: string
            maxLength: 500
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /location/reverseGeocode:
    get:
      summary: Location reverse geocode
      operationId: locationReverseGeocode
      parameters:
        - name: lat
          in: query
          required: true
          schema:
            type: string
            maxLength: 100
        - name: lng
          in: query
          required: true
          schema:
            type: string
            maxLength: 100
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /location/toPlace:
    get:
      summary: Location to place
      operationId: locationToPlace
      parameters:
        - name: placeId
          in: query
          required: true
          schema:
            type: string
            maxLength: 10000
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /tnc:
    get:
      summary: Get latest terms and conditions
      operationId: termsAndCondition
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  version:
                    type: integer
                    description: Version number of the terms
                  content:
                    type: string
                    description: Terms and conditions content
                  createdAt:
                    type: string
                    format: date-time
                    description: When the terms were created
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /tnc/version/latest:
    get:
      summary: Get latest terms and conditions version
      operationId: termsAndConditionVersion
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  version:
                    type: integer
                    description: Version number of the terms
                  createdAt:
                    type: string
                    format: date-time
                    description: When the terms were created
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /tnc/version/{version}:
    get:
      summary: Get terms and conditions by version
      operationId: termsAndConditionByVersion
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      parameters:
        - name: version
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  version:
                    type: integer
                    description: Version number of the terms
                  content:
                    type: string
                    description: Terms and conditions content
                  createdAt:
                    type: string
                    format: date-time
                    description: When the terms were created
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /privacy:
    get:
      summary: Get latest privacy policy
      operationId: privacyPolicy
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  version:
                    type: integer
                    description: Version number of the privacy policy
                  content:
                    type: string
                  createdAt:
                    type: string
                    format: date-time
                    description: When the privacy policy was created
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /privacy/version/latest:
    get:
      summary: Get latest privacy policy version
      operationId: privacyPolicyVersion
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  version:
                    type: integer
                    description: Version number of the privacy policy
                  createdAt:
                    type: string
                    format: date-time
                    description: When the privacy policy was created
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /privacy/version/{version}:
    get:
      summary: Get privacy policy by version
      operationId: privacyPolicyByVersion
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      parameters:
        - name: version
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  version:
                    type: integer
                    description: Version number of the privacy policy
                  content:
                    type: string
                    description: Privacy policy content
                  createdAt:
                    type: string
                    format: date-time
                    description: When the privacy policy was created
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /studio/feedback:
    post:
      summary: Submit studio tutorial feedback
      operationId: studioFeedbackSubmit
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StudioFeedbackRequest'
      responses:
        "200":
          description: Feedback submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudioFeedbackResponse'
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /cometchat/webhook:
    post:
      summary: CometChat webhook callback
      operationId: cometchatWebhook
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              description: |
                The CometChat webhook payload. The structure may vary depending on the trigger/action.
                See CometChat documentation for details.
              example:
                trigger: message_sent
                data:
                  message:
                    id: "123"
                    conversationId: "conv_456"
                    sender: "user1"
                    receiver: "user2"
                    receiverType: "user"
                    category: "message"
                    type: "text"
                    sentAt: 1710000000
                    data:
                      text: "Hello!"
                appId: "your-app-id"
                region: "us"
                webhook: "webhook-url"
      responses:
        "200":
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CometChatWebhookResponse'
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /cometchat/webhook/logs:
    get:
      summary: Get CometChat webhook logs
      operationId: cometchatWebhookLogs
      parameters:
        - name: logId
          in: query
          required: false
          schema:
            type: string
            description: Specific log ID to retrieve
        - name: entityId
          in: query
          required: false
          schema:
            type: string
            description: Filter logs by entity ID
        - name: conversationId
          in: query
          required: false
          schema:
            type: string
            description: Filter logs by conversation ID
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
            description: Maximum number of logs to return
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CometChatWebhookLogsResponse'
        "404":
          description: Log not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
  schemas:
    StudioFeedbackRequest:
      type: object
      required:
        - bookingRef
        - video
        - rating
        - feedback
      properties:
        bookingRef:
          type: string
          description: Booking reference number
        video:
          type: string
          description: Video name
        rating:
          type: integer
          minimum: 1
          maximum: 5
          description: Rating (1-5)
        feedback:
          type: string
          description: Feedback text
    StudioFeedbackResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
    CometChatWebhookPayload:
      type: object
      required:
        - action
        - category
        - entity
        - appId
        - timestamp
      properties:
        action:
          type: string
          description: The webhook action (e.g., message_sent, message_delivered, etc.)
        category:
          type: string
          description: The webhook category
        entity:
          type: object
          required:
            - entityType
            - entityId
          properties:
            entityType:
              type: string
              description: Type of the entity (user, message, conversation, etc.)
            entityId:
              type: string
              description: Unique identifier of the entity
            entityUid:
              type: string
              description: User ID if applicable
            messageId:
              type: string
              description: Message ID if applicable
            senderUid:
              type: string
              description: Sender user ID
            receiverUid:
              type: string
              description: Receiver user ID
            receiverType:
              type: string
              description: Type of receiver (user, group)
            conversationId:
              type: string
              description: Conversation ID
            conversationType:
              type: string
              description: Type of conversation
            message:
              type: string
              description: Message content
            messageType:
              type: string
              description: Type of message
            sentAt:
              type: number
              description: Timestamp when message was sent
            deliveredAt:
              type: number
              description: Timestamp when message was delivered
            readAt:
              type: number
              description: Timestamp when message was read
            metadata:
              type: object
              description: Additional metadata
        appId:
          type: string
          description: CometChat app ID
        clientId:
          type: string
          description: Client ID
        region:
          type: string
          description: Region
        timestamp:
          type: number
          description: Webhook timestamp
    CometChatWebhookResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the webhook was processed successfully
        message:
          type: string
          description: Response message
        processed:
          type: boolean
          description: Whether the webhook was processed
        logId:
          type: string
          description: ID of the webhook log entry
    CometChatWebhookLog:
      type: object
      properties:
        id:
          type: string
          description: Log entry ID
        action:
          type: string
          description: Webhook action
        category:
          type: string
          description: Webhook category
        entityType:
          type: string
          description: Entity type
        entityId:
          type: string
          description: Entity ID
        entityUid:
          type: string
          description: Entity user ID
        messageId:
          type: string
          description: Message ID
        senderUid:
          type: string
          description: Sender user ID
        receiverUid:
          type: string
          description: Receiver user ID
        receiverType:
          type: string
          description: Receiver type
        conversationId:
          type: string
          description: Conversation ID
        conversationType:
          type: string
          description: Conversation type
        payload:
          type: string
          description: Full webhook payload
        processed:
          type: boolean
          description: Whether the webhook was processed
        error:
          type: string
          description: Error message if processing failed
        createdAt:
          type: string
          format: date-time
          description: When the log was created
        processedAt:
          type: string
          format: date-time
          description: When the webhook was processed
    CometChatWebhookLogsResponse:
      type: object
      properties:
        logs:
          type: array
          items:
            $ref: '#/components/schemas/CometChatWebhookLog'
          description: Array of webhook logs
        count:
          type: integer
          description: Number of logs returned
        type:
          type: string
          description: Type of logs returned
        entityId:
          type: string
          description: Entity ID filter used
        conversationId:
          type: string
          description: Conversation ID filter used