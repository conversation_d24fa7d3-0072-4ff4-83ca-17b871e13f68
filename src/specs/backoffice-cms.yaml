openapi: 3.0.3
info:
  version: 1.0.0-SNAPSHOT
  title: Backoffice CMS
security:
  - BearerAuth: []
servers:
  - url: /cms/backoffice
paths:
  ##################
  # Manage Profile #
  ##################
  /myProfile:
    get:
      summary: My user profile
      operationId: myProfile
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Admin"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  # Manage Bank
  /banks/list/{page}/{pageSize}/{sort}/{sortDir}:
    get:
      summary: List banks
      operationId: bankList
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/sortDir'
        - $ref: '#/components/parameters/inclInactive'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  # Manage Bank Account
  /bank-accounts/options:
    get:
      summary: Bank account form option
      operationId: bankAccountOption
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts/list/{page}/{pageSize}/{sort}/{sortDir}:
    get:
      summary: List bank accounts
      operationId: bankAccountList
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/sortDir'
        - $ref: '#/components/parameters/inclInactive'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts:
    post:
      summary: Create bank account
      operationId: bankAccountCreate
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BankAccount'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts/{id}:
    get:
      summary: Get bank account by ID
      operationId: bankAccountGetById
      parameters:
        - $ref: '#/components/parameters/id'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
    put:
      summary: Update bank account
      operationId: bankAccountUpdate
      parameters:
        - $ref: '#/components/parameters/id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BankAccount'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts/{id}/status:
    patch:
      summary: Update bank account status
      operationId: bankAccountUpdateStatus
      parameters:
        - $ref: '#/components/parameters/id'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum:
                    - "1"
                    - "0"
              required:
                - status
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  ##################
  # Manage Studios #
  ##################
  /studios/list/{page}/{pageSize}:
    get:
      summary: List studios
      operationId: studioList
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: sort
          in: query
          required: false
          schema:
            type: string
            enum:
              - name
              - contactNo
              - email
              - mainUser
              - instrless
              - active
              - pendingApproval
        - name: sortDir
          description:
            If sort is specified without sortDir, asc is assumed.
          in: query
          required: false
          schema:
            type: string
            enum: [asc, desc]
        - name: inclDisabled
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageOfStudios"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /studios/get/{studioId}:
    get:
      summary: Get studio by ID
      operationId: studioGetById
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Studio"
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /studios/saveRemarks:
    post:
      summary: Save studio remarks
      operationId: studioSaveRemarks
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SaveRemarks"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /studios/setDisabled/{studioId}/{disabled}:
    get:
      summary: Set studio disabled
      operationId: studioSetDisabled
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
        - name: disabled
          in: path
          required: true
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /studios/setApproved/{studioId}:
    get:
      summary: Set studio approved
      operationId: studioSetApproved
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /studios/setStarred/{studioId}/{starred}:
    get:
      summary: Set studio starred
      operationId: studioSetStarred
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
        - name: starred
          in: path
          required: true
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  ######################
  # Manage Instructors #
  ######################
  /instructors/list/{page}/{pageSize}:
    get:
      summary: List instructors
      operationId: instructorList
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: sort
          in: query
          required: false
          schema:
            type: string
            enum:
              - name
              - facebook
              - instagram
              - active
              - pendingApproval
        - name: sortDir
          description:
            If sort is specified without sortDir, asc is assumed.
          in: query
          required: false
          schema:
            type: string
            enum: [asc, desc]
        - name: inclDisabled
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageOfInstructors"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /instructors/get/{instructorId}:
    get:
      summary: Get instructor by ID
      operationId: instructorGetById
      parameters:
        - name: instructorId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Instructor"
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /instructors/saveRemarks:
    post:
      summary: Save instructor remarks
      operationId: instructorSaveRemarks
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SaveRemarks"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /instructors/setDisabled/{instructorId}/{disabled}:
    get:
      summary: Set instructor disabled
      operationId: instructorSetDisabled
      parameters:
        - name: instructorId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
        - name: disabled
          in: path
          required: true
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /instructors/setApproved/{instructorId}:
    get:
      summary: Set instructor approved
      operationId: instructorSetApproved
      parameters:
        - name: instructorId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /instructors/setStarred/{instructorId}/{starred}:
    get:
      summary: Set instructor starred
      operationId: instructorSetStarred
      parameters:
        - name: instructorId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
        - name: starred
          in: path
          required: true
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  ###################
  # Manage Bookings #
  ###################
  /bookings/list/{page}/{pageSize}:
    get:
      summary: List bookings
      operationId: bookingList
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: sort
          in: query
          required: false
          schema:
            type: string
            enum:
              - status
              - cancelled
        - name: sortDir
          description:
            If sort is specified without sortDir, asc is assumed.
          in: query
          required: false
          schema:
            type: string
            enum: [asc, desc]
        - name: inclCancelled
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageOfBookings"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /bookings/get/{bookingId}:
    get:
      summary: Get booking by ID
      operationId: bookingGetById
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Booking"
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /bookings/saveRemarks:
    post:
      summary: Save booking remarks
      operationId: bookingSaveRemarks
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SaveRemarks"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /bookings/setCancelled/{bookingId}:
    post:
      summary: Set booking cancelled
      operationId: bookingSetCancelled
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  maxLength: 500
                free:
                  type: boolean
                  default: false
                refundCredits:
                  type: integer
                  default: 0
              required:
                - reason
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /bookings/approveCancellReq/{bookingId}:
    get:
      summary: Approve booking cancellation request
      operationId: bookingApproveCancellReq
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /bookings/setStarred/{bookingId}/{starred}:
    get:
      summary: Set booking starred
      operationId: bookingSetStarred
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
        - name: starred
          in: path
          required: true
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /bookings/counts:
    get:
      summary: Get count of bookings with pending cancel requests
      operationId: bookingCount
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  pendingCancelCount:
                    type: integer
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /bookings/searchRescheduleSlots/{bookingId}:
    post:
      summary: Search available slots for rescheduling a cancelled booking
      operationId: bookingSearchRescheduleSlots
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                date:
                  type: string
                  format: date
                  minLength: 10
                  maxLength: 10
              required:
                - date
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "404":
          description: Booking Not Found
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /bookings/confirmReschedule/{bookingId}:
    post:
      summary: Confirm reschedule for a cancelled booking
      operationId: bookingConfirmReschedule
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                date:
                  type: string
                  format: date
                  minLength: 10
                  maxLength: 10
                time:
                  type: string
                  minLength: 5
                  maxLength: 5
              required:
                - date
                - time
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  bookingId:
                    type: string
        "400":
          description: Bad Request
        "404":
          description: Booking Not Found
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #################
  # Manage Admins #
  #################
  /admins/list/{page}/{pageSize}:
    get:
      summary: List admins
      operationId: adminList
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: sort
          in: query
          required: false
          schema:
            type: string
            enum:
              - name
              - firebaseEmail
              - deleted
        - name: sortDir
          description:
            If sort is specified without sortDir, asc is assumed.
          in: query
          required: false
          schema:
            type: string
            enum: [asc, desc]
        - name: inclDisabled
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageOfAdmins"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /admins/get/{adminId}:
    get:
      summary: Get admin by ID
      operationId: adminGetById
      parameters:
        - name: adminId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Admin"
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /admins/save:
    post:
      summary: Save admin
      operationId: adminSave
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Admin"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /admins/saveRemarks:
    post:
      summary: Save admin remarks
      operationId: adminSaveRemarks
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SaveRemarks"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /admins/setDeleted/{adminId}/{deleted}:
    get:
      summary: Set admin deleted
      operationId: adminSetDeleted
      parameters:
        - name: adminId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
        - name: deleted
          in: path
          required: true
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #########################
  # Manage Credit Packages #
  #########################
  /packages/list/{page}/{pageSize}:
    get:
      summary: List credit packages
      operationId: packageList
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: sort
          in: query
          required: false
          schema:
            type: string
            enum:
              - name
              - currency
              - price
              - credits
              - status
              - sequence
              - createdAt
        - name: sortDir
          description:
            If sort is specified without sortDir, asc is assumed.
          in: query
          required: false
          schema:
            type: string
            enum: [asc, desc]
        - name: currency
          in: query
          required: false
          schema:
            type: string
            maxLength: 3
        - name: status
          in: query
          required: false
          description: "Filter by status. If not provided, shows all packages (both active and inactive)"
          schema:
            type: boolean

      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageOfPackages"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /packages/get/{packageId}:
    get:
      summary: Get credit package by ID
      operationId: packageGetById
      parameters:
        - name: packageId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Package"
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /packages/save:
    post:
      summary: Save credit package
      operationId: packageSave
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Package'
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /packages/setStatus:
    post:
      summary: Set credit package status
      operationId: packageSetStatus
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                packageId:
                  type: string
                  maxLength: 50
                status:
                  type: boolean
              required:
                - packageId
                - status
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /packages/moveSequence:
    post:
      summary: Move credit package sequence
      operationId: packageMoveSequence
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                packageId:
                  type: string
                  maxLength: 50
                direction:
                  type: string
                  enum:
                    - up
                    - down
              required:
                - packageId
                - direction
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
  /packages/reorderSequences/{currency}:
    post:
      summary: Reorder sequences for a currency
      operationId: packageReorderSequences
      parameters:
        - name: currency
          in: path
          required: true
          schema:
            type: string
            maxLength: 3
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #########################
  # Common Options #
  #########################
  /options/countries:
    get:
      summary: Get all countries
      operationId: countries
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/Country"
                required:
                  - success
                  - data
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /options/currencies:
    get:
      summary: Get currency options
      operationId: currencies
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/CurrencyOption"
                required:
                  - success
                  - data
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #########################
  # CometChat Integration #
  #########################
  /cometchat/user:
    post:
      summary: Create CometChat user for studio
      operationId: cometchatCreateUser
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
    get:
      summary: Get CometChat user for studio
      operationId: cometchatGetUser
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /cometchat/auth-token:
    post:
      summary: Create CometChat auth token for studio
      operationId: cometchatCreateAuthToken
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #####################
  # Manage Promotions #
  #####################
  /promotions/create:
    post:
      summary: Create a new promotion
      operationId: promotionCreate
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PromotionSaveReq"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /promotions/update/{promotionId}:
    put:
      summary: Update a promotion
      operationId: promotionUpdate
      parameters:
        - name: promotionId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PromotionSaveReq"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /promotions/toggleStatus/{promotionId}:
    put:
      summary: Toggle promotion status
      operationId: promotionToggleStatus
      parameters:
        - name: promotionId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /promotions/promoCode/toggleStatus/{promoCodeId}:
    put:
      summary: Toggle promo code status
      operationId: promoCodeToggleStatus
      parameters:
        - name: promoCodeId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum: [active, inactive]
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /promotions/addCodes/{promotionId}:
    post:
      summary: Add promo codes to a promotion
      operationId: promotionAddCodes
      parameters:
        - name: promotionId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                count:
                  type: integer
                prefix:
                  type: string
                code_limit:
                  type: integer
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                type: object
                properties:
                  codes:
                    type: array
                    items:
                      type: string
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict - Code already exists
        "500":
          description: Internal Server Error
  /promotions/list/{page}/{pageSize}:
    get:
      summary: List promotions
      operationId: promotionList
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageOfPromotions"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /promotions/{promotionId}/codes/{page}/{pageSize}:
    get:
      summary: List promo codes for a promotion
      operationId: promotionCodeList
      parameters:
        - name: promotionId
          in: path
          required: true
          schema:
            type: integer
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageOfPromoCodes"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /members/list/{page}/{pageSize}:
    get:
      summary: List members
      operationId: memberList
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: searchTerm
          in: query
          required: false
          schema:
            type: string
        - name: sort
          in: query
          required: false
          schema:
            type: string
            enum: [fullName, firebaseEmail]
        - name: sortDir
          in: query
          required: false
          schema:
            type: string
            enum: [asc, desc]
        - name: inclDeleted
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageOfMembers"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /promotions/assign-member:
    post:
      summary: Assign member to promo code
      operationId: promoCodeAssignMember
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PromoCodeAssignMemberReq"
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
  schemas:
    Studio:
      type: object
    PageOfStudios:
      type: object
      properties:
        totalItems:
          type: integer
        totalPages:
          type: integer
        items:
          type: array
          minItems: 0
          items:
            $ref: "#/components/schemas/Studio"
      required:
        - totalItems
        - totalPages
        - items
    Instructor:
      type: object
    PageOfInstructors:
      type: object
      properties:
        totalItems:
          type: integer
        totalPages:
          type: integer
        items:
          type: array
          minItems: 0
          items:
            $ref: "#/components/schemas/Instructor"
      required:
        - totalItems
        - totalPages
        - items
    Booking:
      type: object
    PageOfBookings:
      type: object
      properties:
        totalItems:
          type: integer
        totalPages:
          type: integer
        items:
          type: array
          minItems: 0
          items:
            $ref: "#/components/schemas/Booking"
      required:
        - totalItems
        - totalPages
        - items
    Admin:
      type: object
      properties:
        id:
          type: string
          maxLength: 50
          nullable: true
        firebaseEmail:
          type: string
          maxLength: 200
        name:
          type: string
          maxLength: 200
        internalNotes:
          type: string
          maxLength: 5000
          nullable: true
      required:
        - firebaseEmail
        - name
    PageOfAdmins:
      type: object
      properties:
        totalItems:
          type: integer
        totalPages:
          type: integer
        items:
          type: array
          minItems: 0
          items:
            $ref: "#/components/schemas/Admin"
      required:
        - totalItems
        - totalPages
        - items
    Package:
      type: object
      properties:
        id:
          type: string
          maxLength: 50
          nullable: true
        name:
          type: string
          maxLength: 100
        currency:
          type: string
          maxLength: 3
        price:
          type: number
          minimum: 0
        credits:
          type: number
          minimum: 0
        bonusCredits:
          type: number
          minimum: 0
          default: 0
        status:
          type: boolean
          default: true
        firstTimeOnly:
          type: boolean
          default: false
        instructorOnly:
          type: boolean
          default: false
        validFrom:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
          nullable: true
          description: "MySQL datetime format (YYYY-MM-DD HH:mm:ss)"
        validTo:
          type: string
          pattern: '^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
          nullable: true
          description: "MySQL datetime format (YYYY-MM-DD HH:mm:ss)"
        purchaseLimit:
          type: integer
          minimum: 1
          nullable: true
        sequence:
          type: integer
          minimum: 1
          nullable: true
        createdAt:
          type: string
          format: date-time
          nullable: true
        updatedAt:
          type: string
          format: date-time
          nullable: true
        createdBy:
          type: string
          maxLength: 255
          nullable: true
        updatedBy:
          type: string
          maxLength: 255
          nullable: true
      required:
        - name
        - currency
        - price
        - credits
    PageOfPackages:
      type: object
      properties:
        totalItems:
          type: integer
        totalPages:
          type: integer
        items:
          type: array
          minItems: 0
          items:
            $ref: "#/components/schemas/Package"
      required:
        - totalItems
        - totalPages
        - items
    Country:
      type: object
      properties:
        code:
          type: string
          maxLength: 2
          description: "Country code (e.g., SG, US)"
        name:
          type: string
          maxLength: 64
          description: "Country name (e.g., Singapore)"
        timezone:
          type: string
          maxLength: 64
          description: "Timezone (e.g., Asia/Singapore)"
        currency:
          type: string
          maxLength: 3
          description: "Currency code (e.g., SGD, USD)"
        currencyName:
          type: string
          maxLength: 32
          description: "Currency name (e.g., Singapore Dollar)"
        symbol:
          type: string
          maxLength: 8
          description: "Currency symbol (e.g., $)"
      required:
        - code
        - name
        - timezone
        - currency
        - currencyName
        - symbol
    CurrencyOption:
      type: object
      properties:
        code:
          type: string
          maxLength: 3
          description: "Currency code (e.g., SGD, USD)"
        name:
          type: string
          maxLength: 32
          description: "Currency name (e.g., Singapore Dollar)"
        symbol:
          type: string
          maxLength: 8
          description: "Currency symbol (e.g., $)"
        countryCode:
          type: string
          maxLength: 2
          description: "Country code (e.g., SG)"
        countryName:
          type: string
          maxLength: 64
          description: "Country name (e.g., Singapore)"
      required:
        - code
        - name
        - symbol
        - countryCode
        - countryName
    SaveRemarks:
      type: object
      properties:
        id:
          type: string
          maxLength: 50
        value:
          type: string
          maxLength: 10000
      required:
        - id
        - value
    BankAccount:
      type: object
      properties:
        bankId:
          type: string
          format: uuid
        accountNo:
          type: string
          maxLength: 100
        accountName:
          type: string
          maxLength: 100
        swiftCode:
          type: string
          maxLength: 100
        primary:
          type: boolean
      required:
        - bankId
        - accountNo
        - accountName
        - primary
    Promotion:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        type:
          type: string
          enum: [booking, topup]
        value:
          type: number
        start_date:
          type: string
          format: date-time
        end_date:
          type: string
          format: date-time
        per_user_limit:
          type: integer
        global_limit:
          type: integer
        status:
          type: string
          enum: [active, inactive]
    By:
      type: object
      properties:
        uid:
          type: string
        username:
          type: string
    PromotionSaveReq:
      type: object
      properties:
        promotion:
          $ref: "#/components/schemas/Promotion"
    PageOfPromoCodes:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/PromoCode"
        total:
          type: integer
        page:
          type: integer
        pageSize:
          type: integer
    PromoCode:
      type: object
      properties:
        id:
          type: integer
        promotion_id:
          type: integer
        code:
          type: string
        code_limit:
          type: integer
        assigned_user_id:
          type: string
        created_by:
          type: string
        created_at:
          type: string
          format: date-time
        updated_by:
          type: string
        updated_at:
          type: string
          format: date-time
        deleted_at:
          type: string
          format: date-time
        deleted_by:
          type: string
        status:
          type: string
          enum: [active, inactive]
        applied_count:
          type: integer
        assigned_user_fullname:
          type: string
          description: "The full name of the user this code is assigned to. Will be null if the code is not assigned to a specific user."
    PageOfPromotions:
      type: object
      properties:
        totalItems:
          type: integer
        totalPages:
          type: integer
        items:
          type: array
          minItems: 0
          items:
            $ref: "#/components/schemas/Promotion"
      required:
        - totalItems
        - totalPages
        - items
    Member:
      type: object
      properties:
        id:
          type: string
        firebaseUid:
          type: string
        firebaseEmail:
          type: string
        fullName:
          type: string
        displayName:
          type: string
        gender:
          type: string
        mobileNo:
          type: string
        registeredAt:
          type: string
        deleted:
          type: boolean
    PageOfMembers:
      type: object
      properties:
        totalItems:
          type: integer
        totalPages:
          type: integer
        items:
          type: array
          items:
            $ref: "#/components/schemas/Member"
      required:
        - totalItems
        - totalPages
        - items
    PromoCodeAssignMemberReq:
      type: object
      properties:
        promoCodeId:
          type: integer
        memberId:
          type: string
      required:
        - promoCodeId
        - memberId
  parameters:
    id:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
    page:
      name: page
      in: path
      required: true
      schema:
        type: integer
        minimum: 1
    pageSize:
      name: pageSize
      in: path
      required: true
      schema:
        type: integer
        minimum: 1
    sort:
      name: sort
      in: path
      required: true
      schema:
        type: string
    sortDir:
      name: sortDir
      in: path
      required: true
      schema:
        type: string
        enum: [asc, desc]
    inclInactive:
      name: inclInactive
      in: query
      required: false
      schema:
        type: boolean
        default: true
  responses:
    SuccessResponse:
      description: Success
    BadRequestResponse:
      description: Bad Request
    UnauthorizedResponse:
      description: Unauthorized
    InternalServerErrorResponse:
      description: Internal Server Error
