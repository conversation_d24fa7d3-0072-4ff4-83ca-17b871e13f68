openapi: 3.0.3
info:
  version: 1.0.0-SNAPSHOT
  title: Studio CMS
security:
  - BearerAuth: []
servers:
  - url: /cms/studio
paths:
  # Manage Bank Account
  /bank-accounts/options:
    get:
      summary: Bank account form option
      operationId: bankAccountOption
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts/list/{page}/{pageSize}/{sort}/{sortDir}:
    get:
      summary: List bank accounts
      operationId: bankAccountList
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/pageSize'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/sortDir'
        - $ref: '#/components/parameters/inclInactive'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts:
    post:
      summary: Create bank account
      operationId: bankAccountCreate
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BankAccount'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts/{id}:
    get:
      summary: Get bank account by ID
      operationId: bankAccountGetById
      parameters:
        - $ref: '#/components/parameters/id'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
    put:
      summary: Update bank account
      operationId: bankAccountUpdate
      parameters:
        - $ref: '#/components/parameters/id'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BankAccount'
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'
  /bank-accounts/{id}/status:
    patch:
      summary: Update bank account status
      operationId: bankAccountUpdateStatus
      parameters:
        - $ref: '#/components/parameters/id'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum:
                    - "1"
                    - "0"
              required:
                - status
      responses:
        "200":
          $ref: '#/components/responses/SuccessResponse'
        "400":
          $ref: '#/components/responses/BadRequestResponse'
        "401":
          $ref: '#/components/responses/UnauthorizedResponse'
        "500":
          $ref: '#/components/responses/InternalServerErrorResponse'

  ####################
  # Login / Register #
  ####################
  /account/status:
    get:
      summary: Account status
      operationId: accountStatus
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AccountStatus"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /register:
    post:
      summary: Register
      operationId: register
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Register"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  ##################
  # Manage Profile #
  ##################
  /myProfile:
    get:
      summary: My user profile
      operationId: myProfile
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /myProfile/save:
    post:
      summary: My user profile > Save
      operationId: myProfileSave
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/User"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  ##################
  # Get Tawk Token #
  ##################
  /tawkToken:
    get:
      summary: Get Tawk Token
      operationId: tawkToken
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                  email:
                    type: string
                  hash:
                    type: string
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #########################
  # Manage Studio Details #
  #########################
  /myStudio:
    get:
      summary: Studio profile
      operationId: myStudio
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Studio"
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /myStudio/save:
    post:
      summary: Studio profile > Save
      operationId: myStudioSave
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Studio"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /myStudio/picture/upload:
    post:
      summary: Upload picture for studio
      operationId: myStudioPictureUpload
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  nullable: false
              required:
                - file
      responses:
        "200":
          description: Success
          content:
            text/plain:
              schema:
                type: string
                description: Picture ID
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /myStudio/picture/get/{pictureId}:
    get:
      summary: Get picture
      operationId: myStudioPictureGet
      security: [] # (!) NO SECURITY
      parameters:
        - name: pictureId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  ################
  # Manage Users #
  ################
  /users/list/{page}/{pageSize}:
    get:
      summary: List users
      operationId: userList
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: sort
          in: query
          required: false
          schema:
            type: string
            enum:
              - name
              - email
              - jobTitle
              - contactNo
              - active
              - admin
        - name: sortDir
          description:
            If sort is specified without sortDir, asc is assumed.
          in: query
          required: false
          schema:
            type: string
            enum: [asc, desc]
        - name: inclDisabled
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageOfUsers"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /users/get/{userId}:
    get:
      summary: Get user by ID
      operationId: userGetById
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /users/save:
    post:
      summary: Save user
      operationId: userSave
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/User"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /users/setDisabled/{userId}/{disabled}:
    get:
      summary: Set user disabled
      operationId: userSetDisabled
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
        - name: disabled
          in: path
          required: true
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #############
  # Equipment #
  #############
  /equipment/types:
    get:
      summary: Equipment types
      operationId: equipmentTypes
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/EquipmentType"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /equipment/all:
    get:
      summary: All equipment, meant for drop down list
      operationId: equipmentAll
      parameters:
        - name: inclDeleted
          description: Deleted records are not included by default.
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EquipmentSimple"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /equipment/list/{page}/{pageSize}:
    get:
      summary: List equipment
      operationId: equipmentList
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: typeId
          description: Equipment type
          in: query
          required: false
          schema:
            type: string
            maxLength: 50
        - name: availableNow
          description: Equipment available now
          in: query
          required: false
          schema:
            type: boolean
        - name: inclDeleted
          description: Deleted records are not included by default.
          in: query
          required: false
          schema:
            type: boolean
        - name: isPrivacy
          description: Indicate if the space is public or private
          in: query
          required: false
          schema:
            type: boolean
        - name: searchTerm
          in: query
          required: false
          allowReserved: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageOfEquipment"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /equipment/get/{equipmentId}:
    get:
      summary: Get equipment by ID
      operationId: equipmentGetById
      parameters:
        - name: equipmentId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Equipment"
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /equipment/save:
    post:
      summary: Save equipment
      operationId: equipmentSave
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Equipment"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /equipment/set-deleted/{equipmentId}/{deleted}:
    get:
      summary: Set equipment deleted
      operationId: equipmentSetDeleted
      parameters:
        - name: equipmentId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
        - name: deleted
          in: path
          required: true
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /equipment/exists:
    get:
      summary: check Equipment Code Unique
      operationId: equipmentCodeExists
      parameters:
        - name: code
          in: query
          required: true
          schema:
            type: string
        - name: equipmentId
          in: query
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  ################
  # Availability #
  ################
  /equipment/{equipmentId}/getAvailability:
    get:
      summary: Get availability for equipment
      operationId: equipmentAvailabilityGet
      parameters:
        - name: equipmentId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Availability"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /equipment/{equipmentId}/saveAvailability:
    post:
      summary: Save availability for equipment
      operationId: equipmentAvailabilitySave
      parameters:
        - name: equipmentId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: "#/components/schemas/Availability"
              maxItems: 3
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  ############
  # Schedule #
  ############
  /schedule:
    get:
      summary: Schedule, for calendar
      operationId: schedule
      parameters:
        - name: start
          in: query
          required: true
          schema:
            type: string
            format: date
            maxLength: 10
            description: YYYY-MM-DD format
        - name: end
          in: query
          required: true
          schema:
            type: string
            format: date
            maxLength: 10
            description: YYYY-MM-DD format
        - name: equipmentId
          in: query
          required: false
          schema:
            type: string
            maxLength: 50
        - name: inclCancelled
          in: query
          required: false
          schema:
            type: boolean
        - name: searchTerm
          in: query
          required: false
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EventsForSchedule"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /schedule/getEvent/{eventId}:
    get:
      summary: Get event by ID
      operationId: eventGetById
      parameters:
        - name: eventId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Event"
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /schedule/saveEvent:
    post:
      summary: Save event
      operationId: eventSave
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Event"
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /schedule/cancelEvent/{eventId}:
    post:
      summary: Cancel event
      operationId: eventCancel
      parameters:
        - name: eventId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  maxLength: 200
                  nullable: false
              required:
                - reason
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  ###################
  # Rating & Review #
  ###################
  /reviews/{page}/{pageSize}:
    get:
      summary: List review
      operationId: reviews
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PageOfUsers"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /ratings:
    get:
      summary: rating summary
      operationId: ratings
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #########################
  # CometChat Integration #
  #########################
  /cometchat/user:
    post:
      summary: Create CometChat user for studio
      operationId: cometchatCreateUser
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
    get:
      summary: Get CometChat user for studio
      operationId: cometchatGetUser
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /cometchat/auth-token:
    post:
      summary: Create CometChat auth token for studio
      operationId: cometchatCreateAuthToken
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
  schemas:
    AccountStatus:
      type: object
      properties:
        emailExists:
          type: boolean
        studioActive:
          type: boolean
        accountActive:
          type: boolean
      required:
        - emailExists
        - studioActive
        - accountActive
    Register:
      type: object
      properties:
        mainUser:
          $ref: "#/components/schemas/MainUser"
        studio:
          $ref: "#/components/schemas/Studio"
      required:
        - mainUser
        - studio
    MainUser:
      type: object
      properties:
        name:
          type: string
          maxLength: 200
        jobTitle:
          type: string
          maxLength: 100
        contactNo:
          type: string
          maxLength: 100
      required:
        - name
        - jobTitle
        - contactNo
    Studio:
      type: object
      properties:
        name:
          type: string
          maxLength: 200
        descr:
          type: string
          maxLength: 10000
        contactNo:
          type: string
          maxLength: 100
        email:
          type: string
          maxLength: 200
        address:
          type: string
          maxLength: 1000
        placeId:
          type: string
          maxLength: 5000
        usagePolicies:
          type: string
          maxLength: 10000
        instrLess:
          type: boolean
          nullable: true
        pictures:
          type: array
          items:
            type: string
            maxLength: 200
        socialMedia:
          $ref: "#/components/schemas/SocialMedia"
      required:
        - name
        - descr
        - contactNo
        - email
        - address
        - placeId
        - usagePolicies
        - pictures
        - socialMedia
    SocialMedia:
      type: object
      properties:
        facebook:
          type: string
          maxLength: 500
        instagram:
          type: string
          maxLength: 500
        website:
          type: string
          maxLength: 500
    User:
      type: object
      properties:
        id:
          type: string
          maxLength: 50
          nullable: true
        email:
          type: string
          maxLength: 200
        name:
          type: string
          maxLength: 200
        jobTitle:
          type: string
          maxLength: 100
        contactNo:
          type: string
          maxLength: 100
        isAdmin:
          type: boolean
          nullable: true
        isDisabled:
          type: boolean
          nullable: true
        isEmailVerified:
          type: boolean
          nullable: true
      required:
        - email
        - name
        - jobTitle
        - contactNo
    PageOfUsers:
      type: object
      properties:
        totalItems:
          type: integer
        totalPages:
          type: integer
        items:
          type: array
          minItems: 0
          items:
            $ref: "#/components/schemas/User"
      required:
        - totalItems
        - totalPages
        - items
    Review:
      type: object
      properties:
        rating:
          type: number
          maxLength: 10
          nullable: true
        feedback:
          type: string
          maxLength: 500
        feedbackAt:
          type: string
          minLength: 16
          maxLength: 16
        member:
          type: object
    PageOfReviews:
      type: object
      properties:
        totalItems:
          type: integer
        totalPages:
          type: integer
        items:
          type: array
          minItems: 0
          items:
            $ref: "#/components/schemas/Review"
      required:
        - totalItems
        - totalPages
        - items
    AvailabilitySlot:
      type: object
      properties:
        start:
          type: string
          minLength: 5
          maxLength: 5
          description: Time in HH:mm (24h) format, eg. 13:30
        end:
          type: string
          minLength: 5
          maxLength: 5
          description: Time in HH:mm (24h) format, eg. 18:30
        price:
          type: string
          pattern: '^\d+(?:\.\d{0,2})?$'
          maxLength: 10
          description: Numeric value, up to 2 decimal places
      required:
        - start
        - end
        - price
    Availability:
      type: object
      properties:
        type:
          type: string
          enum:
            - ALL
            - WD
            - WE
            - PH
        slots:
          type: array
          minItems: 0
          maxItems: 4
          items:
            $ref: "#/components/schemas/AvailabilitySlot"
      required:
        - type
        - slots
    EquipmentType:
      type: object
      properties:
        id:
          type: string
          maxLength: 50
        name:
          type: string
          maxLength: 200
      required:
        - id
        - name
    EquipmentSimple:
      type: object
      properties:
        type:
          $ref: "#/components/schemas/EquipmentType"
        code:
          type: string
          maxLength: 50
        shortName:
          type: string
          maxLength: 100
        name:
          type: string
          maxLength: 200
        deleted:
          type: boolean
          nullable: true
      required:
        - type
        - code
        - shortName
        - name
    Equipment:
      type: object
      properties:
        id:
          type: string
          maxLength: 50
          nullable: true
        type:
          $ref: "#/components/schemas/EquipmentType"
        code:
          type: string
          maxLength: 50
        privacy:
          type: boolean
        startDate:
          type: string
          format: date
          minLength: 10
          maxLength: 10
          nullable: true
          description: Date in YYYY-MM-DD format, eg. 2024-12-31
        endDate:
          type: string
          format: date
          minLength: 10
          maxLength: 10
          nullable: true
          description: Date in YYYY-MM-DD format, eg. 2026-12-31
        availability:
          type: array
          items:
            $ref: "#/components/schemas/Availability"
          maxItems: 3
          nullable: true
      required:
        - type
        - code
    PageOfEquipment:
      type: object
      properties:
        totalItems:
          type: integer
        totalPages:
          type: integer
        items:
          type: array
          minItems: 0
          items:
            $ref: "#/components/schemas/Equipment"
      required:
        - totalItems
        - totalPages
        - items
    EventType:
      type: string
      enum:
        - Booking
        - EquipmentBlocked
        - StudioBlocked
    EventEquipment:
      type: object
      properties:
        name:
          type: string
          maxLength: 200
      required:
        - name
    EventInstructor:
      type: object
      properties:
        fullName:
          type: string
          maxLength: 100
      required:
        - fullName
    EventMember:
      type: object
      properties:
        fullName:
          type: string
          maxLength: 100
      required:
        - fullName
    Event:
      type: object
      properties:
        id:
          type: string
          maxLength: 50
          nullable: true
        type:
          $ref: "#/components/schemas/EventType"
        bookingRefNo:
          type: string
          maxLength: 50
          nullable: true
          description: Only present for Booking type, eg. \#WTG341
        name:
          type: string
          maxLength: 100
          nullable: true
        internalRemarks:
          type: string
          maxLength: 5000
          nullable: true
        startDate:
          type: string
          format: date
          minLength: 10
          maxLength: 10
          description: Date in YYYY-MM-DD format, eg. 2024-12-31
        endDate:
          type: string
          format: date
          minLength: 10
          maxLength: 10
          description: Date in YYYY-MM-DD format, eg. 2026-12-31
        startTime:
          type: string
          minLength: 5
          maxLength: 5
          description: Time in HH:mm (24h) format, eg. 13:30. Null for full day event.
          nullable: true
        endTime:
          type: string
          minLength: 5
          maxLength: 5
          description: Time in HH:mm (24h) format, eg. 18:30. Null for full day event.
          nullable: true
        isFullDay:
          type: boolean
          nullable: true
        equipmentId:
          type: string
          maxLength: 50
          nullable: true
        equipment:
          $ref: "#/components/schemas/EventEquipment"
        instructor:
          $ref: "#/components/schemas/EventInstructor"
        member:
          $ref: "#/components/schemas/EventMember"
        cancellable:
          type: boolean
          nullable: true
        cancelled:
          type: boolean
          nullable: true
        cancellationReason:
          type: string
          maxLength: 200
          nullable: true
        cancelledBy:
          type: string
          maxLength: 200
          nullable: true
      required:
        - type
        - startDate
        - endDate
    EventsForSchedule:
      type: object
      properties:
        numBookings:
          type: integer
          minimum: 0
        events:
          type: array
          items:
            $ref: "#/components/schemas/Event"
      required:
        - numBookings
        - events
    BankAccount:
      type: object
      properties:
        bankId:
          type: string
          format: uuid
        accountNo:
          type: string
          maxLength: 100
        accountName:
          type: string
          maxLength: 100
        swiftCode:
          type: string
          maxLength: 100
        primary:
          type: boolean
      required:
        - bankId
        - accountNo
        - accountName
        - primary
  parameters:
    id:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
    page:
      name: page
      in: path
      required: true
      schema:
        type: integer
        minimum: 1
    pageSize:
      name: pageSize
      in: path
      required: true
      schema:
        type: integer
        minimum: 1
    sort:
      name: sort
      in: path
      required: true
      schema:
        type: string
    sortDir:
      name: sortDir
      in: path
      required: true
      schema:
        type: string
        enum: [asc, desc]
    inclInactive:
      name: inclInactive
      in: query
      required: false
      schema:
        type: boolean
        default: false
  responses:
    SuccessResponse:
      description: Success
    BadRequestResponse:
      description: Bad Request
    UnauthorizedResponse:
      description: Unauthorized
    InternalServerErrorResponse:
      description: Internal Server Error
