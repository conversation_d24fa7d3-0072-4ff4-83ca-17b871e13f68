openapi: 3.0.3
info:
  version: 1.0.0-SNAPSHOT
  title: Booking API
security:
  - BearerAuth: []
servers:
  - url: /api/booking
paths:
  /make-booking:
    post:
      summary: Make booking
      operationId: bookingMake
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingMake'
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /bookings/{page}/{pageSize}:
    get:
      summary: Booking
      operationId: bookings
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: upcomingOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: pastOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: cancelledOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: feedbackOnly
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /bookings/get/{bookingId}:
    get:
      summary: Booking by ID
      operationId: bookingById
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  ############
  # Schedule #
  ############
  /schedule/calendar:
    get:
      summary: Get schedule calendar
      operationId: scheduleCalendar
      parameters:
        - name: year
          in: query
          required: true
          schema:
            type: integer
            minLength: 4
            maxLength: 4
        - name: month
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maximum: 12
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /schedule/byDay:
    get:
      summary: Get schedule by day
      operationId: scheduleByDay
      parameters:
        - name: day
          in: query
          required: true
          schema:
            type: string
            format: date
            minLength: 10
            maxLength: 10
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  ##########
  # Search #
  ##########
  /search:
    get:
      summary: Search slots
      operationId: search
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      parameters:
        - name: lat
          in: query
          required: true
          schema:
            type: number
            minLength: 1
            maxLength: 80
        - name: lng
          in: query
          required: true
          schema:
            type: number
            minLength: 1
            maxLength: 80
        - name: distance
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maxLength: 3
        - name: date
          in: query
          required: true
          schema:
            type: string
            format: date
            minLength: 10
            maxLength: 10
        - name: time
          in: query
          required: true
          allowReserved: true
          schema:
            type: string
            minLength: 5
            maxLength: 5
        - name: studioId
          in: query
          required: false
          schema:
            type: string
            maxLength: 50
        - name: instrId
          in: query
          required: false
          schema:
            type: string
            maxLength: 50
        - name: equipTypes
          in: query
          required: false
          allowReserved: true
          schema:
            type: string
            maxLength: 500
        - name: instrLessOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: faveStudioOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: faveInstrOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: extend
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /search/equipment:
    get:
      summary: Search > equipment
      operationId: searchEquipment
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      parameters:
        - name: lat
          in: query
          required: true
          schema:
            type: number
            minLength: 1
            maxLength: 80
        - name: lng
          in: query
          required: true
          schema:
            type: number
            minLength: 1
            maxLength: 80
        - name: distance
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maxLength: 3
        - name: date
          in: query
          required: true
          schema:
            type: string
            format: date
            minLength: 10
            maxLength: 10
        - name: time
          in: query
          required: true
          allowReserved: true
          schema:
            type: string
            minLength: 5
            maxLength: 5
        - name: studioId
          in: query
          required: true
          schema:
            type: string
            maxLength: 50
        - name: instrId
          in: query
          required: false
          schema:
            type: string
            maxLength: 50
        - name: equipTypes
          in: query
          required: false
          allowReserved: true
          schema:
            type: string
            maxLength: 500
        - name: instrLessOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: faveStudioOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: faveInstrOnly
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /search/equipment/choose:
    get:
      summary: Search > equipment > choose
      operationId: searchEquipmentChoose
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      parameters:
        - name: lat
          in: query
          required: true
          schema:
            type: number
            minLength: 1
            maxLength: 80
        - name: lng
          in: query
          required: true
          schema:
            type: number
            minLength: 1
            maxLength: 80
        - name: distance
          in: query
          required: true
          schema:
            type: integer
            minimum: 1
            maxLength: 3
        - name: date
          in: query
          required: true
          schema:
            type: string
            format: date
            minLength: 10
            maxLength: 10
        - name: time
          in: query
          required: true
          allowReserved: true
          schema:
            type: string
            minLength: 5
            maxLength: 5
        - name: studioId
          in: query
          required: true
          schema:
            type: string
            maxLength: 50
        - name: instrId
          in: query
          required: false
          schema:
            type: string
            maxLength: 50
        - name: equipId
          in: query
          required: true
          schema:
            type: string
            maxLength: 50
        - name: equipTypes
          in: query
          required: false
          allowReserved: true
          schema:
            type: string
            maxLength: 500
        - name: instrLessOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: faveStudioOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: faveInstrOnly
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #############
  # Exercises #
  #############
  /exercises/categories:
    get:
      summary: Exercise categories
      operationId: exerciseCategories
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /exercises/byCategoryId/{categoryId}:
    get:
      summary: Exercise by categoryId
      operationId: exercisesByCategoryId
      parameters:
        - name: categoryId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /exercises/modsByExerciseId/{exerciseId}:
    get:
      summary: Exercise modifications by exerciseId
      operationId: exerciseModsByExerciseId
      parameters:
        - name: exerciseId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  ############
  # Sessions #
  ############
  /sessions/{page}/{pageSize}:
    get:
      summary: Sessions
      operationId: sessions
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: upcomingOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: pastOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: cancelledOnly
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /sessions/get/{bookingId}:
    get:
      summary: Session by ID
      operationId: sessionById
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /sessions/saveNotes/{bookingId}:
    post:
      summary: Save session notes
      operationId: sessionNotesSave
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionNotesSave'
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /sessions/saveRecord/{bookingId}:
    post:
      summary: Save session record
      operationId: sessionRecordSave
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      requestBody:
        content:
          application/json:
            schema:
              type: object
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /sessions/complete/{bookingId}:
    post:
      summary: Complete session record
      operationId: sessionRecordComplete
      parameters:
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      requestBody:
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/SessionRecordSave'
                - $ref: '#/components/schemas/SessionRecordFlexiSave'
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  ##########
  # Cancel #
  ##########
  /cancel-booking/request/{eventId}:
    get:
      summary: Cancel booking request
      operationId: bookingCancelRequest
      parameters:
        - name: eventId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /cancel-booking/{eventId}:
    post:
      summary: Cancel booking
      operationId: bookingCancel
      parameters:
        - name: eventId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingCancel'
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /cancel-session/{eventId}:
    post:
      summary: Cancel session
      operationId: sessionCancelRequest
      parameters:
        - name: eventId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionCancel'
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  ##########
  # NoShow #
  ##########
  /noshow-booking/{eventId}:
    post:
      summary: No show booking
      operationId: bookingNoShow
      parameters:
        - name: eventId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingNoShow'
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  ############
  # Feedback #
  ############
  /feedback-booking/{eventId}:
    post:
      summary: Feedback booking
      operationId: bookingFeedback
      parameters:
        - name: eventId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingFeedback'
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /feedback-booking/later/{eventId}:
    get:
      summary: Feedback booking
      operationId: bookingFeedbackLater
      parameters:
        - name: eventId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /feedback-booking/no-feedback/{eventId}:
    get:
      summary: No feedback for booking
      operationId: bookingNoFeedback
      parameters:
        - name: eventId
          in: path
          required: true
          schema:
            type: string
            minimum: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  ##########
  # HitPay #
  ##########
  /hitpay-notify:
    post:
      summary: HitPay notify
      description: For HitPay to notify payment
      operationId: hitpayNotify
      security: [] # (!) NO SECURITY
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  ###########
  # Support #
  ###########
  /equipment-types:
    get:
      summary: Equipment types
      operationId: equipmentTypes
      security: [] # (!) NO SECURITY
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  ###########
  # Credit  #
  ###########
  /credit/packages:
    get:
      summary: Credit Packages
      operationId: creditPackages
      security: # (!) OPTIONAL SECURITY
        - {}
        - BearerAuth: []
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /credit/topup:
    post:
      summary: Topup Credit
      operationId: creditTopup
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreditTopup'
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /credit/payment-notify:
    post:
      summary: Credit Purchase (Payment Notify)
      operationId: creditTopupNotify
      security: [] # (!) NO SECURITY
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /credit/make-booking:
    post:
      summary: Make booking by credit
      operationId: creditBooking
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingMakeCredit'
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /credit/transactions/{page}/{pageSize}:
    get:
      summary: List credit transactions
      operationId: creditTransactions
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: type
          in: query
          required: false
          schema:
            type: string
            enum:
              - Topup
              - Booking
              - Refund
              - Expiry
              - Renewal
        - name: startDate
          in: query
          schema:
            type: string
            format: date
            minLength: 10
            maxLength: 10
        - name: endDate
          in: query
          schema:
            type: string
            format: date
            minLength: 10
            maxLength: 10
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Transaction'
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /credit/extend-expiry:
    post:
      summary: Extend credit expiry by deducting credits
      operationId: creditExtendExpiry
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreditExtendExpiry'
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transaction'
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /bookings/counts:
    get:
      summary: Get booking and session counts
      operationId: bookingCounts
      parameters:
        - name: upcomingOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: pastOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: cancelledOnly
          in: query
          required: false
          schema:
            type: boolean
        - name: feedbackOnly
          in: query
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookingCounts'
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /promo-code/apply:
    post:
      tags:
        - Promo Code
      summary: Apply a promo code for top-up
      operationId: promoCodeApply
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                promoCode:
                  type: string
              required:
                - promoCode
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                properties:
                  message:
                    type: string
                  data:
                    type: object
        '400':
          description: Bad request
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
  schemas:
    BookingMake:
      type: object
      properties:
        date:
          type: string
          format: date
          minLength: 10
          maxLength: 10
        time:
          type: string
          minLength: 5
          maxLength: 5
        instructorId:
          type: string
          maxLength: 50
          nullable: true
        equipmentId:
          type: string
          maxLength: 50
      required:
        - date
        - time
        - equipmentId
    BookingMakeCredit:
      type: object
      properties:
        date:
          type: string
          format: date
          minLength: 10
          maxLength: 10
        time:
          type: string
          minLength: 5
          maxLength: 5
        instructorId:
          type: string
          maxLength: 50
          nullable: true
        equipmentId:
          type: string
          maxLength: 50
        lat:
          type: number
          minimum: -90
          maximum: 90
          description: Latitude coordinate
        lng:
          type: number
          minimum: -180
          maximum: 180
          description: Longitude coordinate
        distance:
          type: integer
          minimum: 1
          maximum: 100
          description: Search radius in kilometers
      required:
        - date
        - time
        - equipmentId
        - lat
        - lng
        - distance
    BookingCancel:
      type: object
      properties:
        token:
          type: string
          maxLength: 50
        reason:
          type: string
          maxLength: 200
      required:
        - token
        - reason
    SessionCancel:
      type: object
      properties:
        reason:
          type: string
          maxLength: 200
      required:
        - reason
    BookingNoShow:
      type: object
      properties:
        reason:
          type: string
          maxLength: 5000
      required:
        - remarks
    BookingFeedback:
      type: object
      properties:
        instructorFeedback:
          type: string
          maxLength: 5000
          description: Feedback from member to instructor
        studioFeedback:
          type: string
          maxLength: 5000
          description: Feedback from member to studio
        isPrivate:
          type: boolean
          description: Flag to indicate if feedback is private
        instructorRating:
          type: integer
          minimum: 1
          maximum: 5
          description: Rating given to the instructor
        studioRating:
          type: integer
          minimum: 1
          maximum: 5
          description: Rating given to the studio
    SessionNotesSave:
      type: object
      properties:
        bookingId:
          type: string
          maxLength: 50
        key:
          type: string
          enum:
            - SessionState
            - SessionTarget
        value:
          type: string
          maxLength: 500
      required:
        - bookingId
        - key
        - value
    SessionRecordSave:
      type: object
      properties:
        bookingId:
          type: string
          maxLength: 50
        focus:
          type: string
          maxLength: 500
        exercises:
          type: array
          minItems: 1
          items:
            $ref: "#/components/schemas/ExerciseRecord"
        feedback:
          type: string
          maxLength: 10000
      required:
        - bookingId
        - focus
        - exercises
        - feedback
    SessionRecordFlexiSave:
      type: object
      properties:
        bookingId:
          type: string
          maxLength: 50
        state:
          type: string
          maxLength: 1000
        segments:
          type: array
          minItems: 1
          items:
            type: string
        feedback:
          type: string
          maxLength: 10000
        overallRating:
          type: integer
          minimum: 0
          maximum: 5
      required:
        - bookingId
        - segments
        - feedback
        - overallRating
    ExerciseRecord:
      type: object
      properties:
        category:
          $ref: "#/components/schemas/ExerciseRecord_Category"
        exercise:
          $ref: "#/components/schemas/ExerciseRecord_Exercise"
        modification:
          nullable: true
          type: object
          properties:
            id:
              type: string
              maxLength: 50
            name:
              type: string
              maxLength: 500
          required:
            - id
            - name
        spring:
          type: string
          maxLength: 500
        reps:
          type: string
          minLength: 1
          maxLength: 4
          pattern: ^\d{1,4}$
        duration:
          type: string
          minLength: 5
          maxLength: 5
          pattern: ^\d{2}.[0-5]\d?$
        stabilityRating:
          type: integer
          minimum: 0
          maximum: 5
        formRating:
          type: integer
          minimum: 0
          maximum: 5
      required:
        - category
        - exercise
        - spring
        - reps
        - duration
        - stabilityRating
        - formRating
    ExerciseRecord_Category:
      type: object
      properties:
        id:
          type: string
          maxLength: 50
        name:
          type: string
          maxLength: 500
      required:
        - id
        - name
    ExerciseRecord_Exercise:
      type: object
      properties:
        id:
          type: string
          maxLength: 50
        name:
          type: string
          maxLength: 500
      required:
        - id
        - name
    CreditTopup:
      type: object
      properties:
        packageId:
          type: string
          maxLength: 50
      required:
        - packageId
    Transaction:
      type: object
      properties:
        id:
          type: string
        amount:
          type: number
        date:
          type: string
          format: date
        type:
          type: string
        status:
          type: string
      required:
        - id
        - amount
        - date
        - type
        - status
    CreditExtendExpiry:
      type: object
      properties:
        months:
          type: integer
          minimum: 1
          maximum: 12
          description: Number of months to extend expiry by
      required:
        - months
    BookingCounts:
      type: object
      properties:
        bookings:
          type: integer
          description: Total number of bookings matching the criteria
        sessions:
          type: integer
          description: Total number of sessions matching the criteria
      required:
        - bookings
        - sessions