openapi: 3.0.3
info:
  version: 1.0.0-SNAPSHOT
  title: Member API
security:
  - BearerAuth: []
servers:
  - url: /api/member
paths:
  /exists:
    get:
      summary: exists?
      operationId: exists
      responses:
        "200":
          description: Success
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /otp/send:
    post:
      summary: Send OTP
      operationId: sendOtp
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                phoneNumber:
                  type: string
                  maxLength: 20
              required:
                - phoneNumber
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
  /otp/verify:
    post:
      summary: Verify OTP
      operationId: verifyOtp
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                phoneNumber:
                  type: string
                  maxLength: 20
                code:
                  type: string
                  maxLength: 6
              required:
                - phoneNumber
                - code
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
  /register:
    post:
      summary: Register
      description: |
        Register a new member. On successful registration, a corresponding user will also be created in CometChat for chat integration (no extra action required by client).
      operationId: register
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Register'
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "409":
          description: Conflict
        "500":
          description: Internal Server Error
  /myAccount:
    get:
      summary: My Account
      operationId: myAccount
      responses:
        "200":
          description: Success
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /myAccount/save:
    post:
      summary: My Account > Save
      operationId: myAccountSave
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MyAccount'
      responses:
        "200":
          description: Success
        "204":
          description: No Content
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #########
  # Faves #
  #########
  /faveStudios:
    get:
      summary: Get fave studios
      operationId: faveStudios
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /faveStudios/has/{studioId}:
    get:
      summary: Has fave studio
      operationId: faveStudioHas
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /faveStudios/add/{studioId}:
    get:
      summary: Add fave studio
      operationId: faveStudioAdd
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /faveStudios/remove/{studioId}:
    get:
      summary: Remove fave studio
      operationId: faveStudioRemove
      parameters:
        - name: studioId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /faveInstructors:
    get:
      summary: Get fave instructors
      operationId: faveInstructors
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /faveInstructors/has/{instructorId}:
    get:
      summary: Has fave instructor
      operationId: faveInstructorHas
      parameters:
        - name: instructorId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /faveInstructors/add/{instructorId}:
    get:
      summary: Add fave instructors
      operationId: faveInstructorAdd
      parameters:
        - name: instructorId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /faveInstructors/remove/{instructorId}:
    get:
      summary: Remove fave instructors
      operationId: faveInstructorRemove
      parameters:
        - name: instructorId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  #######
  # FCM #
  #######
  /fcmToken/set:
    post:
      summary: Set FCM token
      operationId: fcmTokenSet
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetFcmToken'
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /fcmToken/unset:
    post:
      summary: Unset FCM token
      operationId: fcmTokenRemove
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /fcmConfig:
    get:
      summary: FCM Config
      operationId: fcmConfig
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /fcmBlackout/setTime:
    get:
      summary: Set FCM blackout time
      operationId: fcmBlackoutSetTime
      parameters:
        - name: start
          in: query
          required: true
          schema:
            type: string
            minLength: 4
            maxLength: 4
        - name: end
          in: query
          required: true
          schema:
            type: string
            minLength: 4
            maxLength: 4
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /fcmBlackout/setStart:
    get:
      summary: Set FCM blackout start
      operationId: fcmBlackoutSetStart
      parameters:
        - name: v
          in: query
          required: true
          schema:
            type: string
            minLength: 4
            maxLength: 4
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /fcmBlackout/setEnd:
    get:
      summary: Set FCM blackout end
      operationId: fcmBlackoutSetEnd
      parameters:
        - name: v
          in: query
          required: true
          schema:
            type: string
            minLength: 4
            maxLength: 4
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /fcmTest:
    post:
      summary: Test FCM
      operationId: fcmTest
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  maxLength: 200
                body:
                  type: string
                  maxLength: 1000
                go:
                  type: string
                  maxLength: 200
              required:
                - title
                - body
      responses:
        "200":
          description: Success
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /inbox/{page}/{pageSize}:
    get:
      summary: Inbox
      operationId: inbox
      parameters:
        - name: page
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: pageSize
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /inbox/{messageId}/read:
    post:
      summary: Mark message as read
      operationId: fmcMarkAsRead
      parameters:
        - name: messageId
          in: path
          required: true
          schema:
            type: string
            maxLength: 50
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
        "401":
          description: Unauthorized
        "404":
          description: Message not found
        "500":
          description: Internal Server Error
  #######################
  # Request delete data #
  #######################
  /request-delete:
    get:
      summary: Request delete
      operationId: requestDelete
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /delete:
    get:
      summary: Delete and set request delete completed
      operationId: deleteAcc
      responses:
        "200":
          description: Success
        "401":
          description: Unauthorized
        "500":
          description: Internal Server Error
  /cometchat/user:
    post:
      summary: Create CometChat user
      description: Create a CometChat user for the authenticated member (no request body needed).
      operationId: cometchatCreateUser
      responses:
        "200":
          description: Success
        "404":
          description: Member not found
        "500":
          description: Internal Server Error
    get:
      summary: Get CometChat user
      description: Get the CometChat user for the authenticated member.
      operationId: cometchatGetUser
      responses:
        "200":
          description: Success
        "404":
          description: User not found
        "500":
          description: Internal Server Error
  /cometchat/auth-token:
    post:
      summary: Create CometChat auth token
      description: Get or create the CometChat user, then create an auth token for the authenticated member.
      operationId: cometchatCreateAuthToken
      responses:
        "200":
          description: Success
        "404":
          description: Member not found
        "500":
          description: Internal Server Error
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
  schemas:
    Register:
      type: object
      properties:
        fullName:
          type: string
          maxLength: 200
        displayName:
          type: string
          maxLength: 50
        gender:
          type: string
          enum:
            - Female
            - Male
            - RatherNotSay
        mobileNo:
          $ref: "#/components/schemas/ContactNo"
        emergencyContact:
          $ref: "#/components/schemas/Contact"
      required:
        - fullName
        - displayName
        - gender
        - mobileNo
    ContactNo:
      type: object
      properties:
        countryCode:
          type: string
          maxLength: 5
        number:
          type: string
          maxLength: 50
      required:
        - countryCode
        - number
    Contact:
      type: object
      properties:
        name:
          type: string
          maxLength: 200
        contactNo:
          $ref: "#/components/schemas/ContactNo"
      required:
        - name
        - contactNo
    MyAccount:
      $ref: "#/components/schemas/Register"
    SetFcmToken:
      type: object
      properties:
        token:
          type: string
          maxLength: 1000
          nullable: true
