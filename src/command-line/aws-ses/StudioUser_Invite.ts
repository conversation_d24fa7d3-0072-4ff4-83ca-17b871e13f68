import { ListTemplatesCommand } from "@aws-sdk/client-ses";
import { Template } from "@aws-sdk/client-ses/dist-types/models/models_0";
import { createTemplate, listTemplates, updateTemplate } from "@utils/aws-ses";
import logger from "@utils/logger";

class Main {
  async execute() {
    const template = {
      TemplateName: "StudioUser_Invite",
      SubjectPart: "Welcome to ViFit Studio Management – Let’s get your studio set up!",
      HtmlPart: "Hi {{name}},<br><br>Welcome to ViFit Studio Management! We are thrilled to have your studio join our growing Pilates community.<br><br>Your account has been successfully created. To get started, simply log in and complete your studio setup so you can begin managing classes and accepting bookings.<br><br><a href='{{signupURL}}'>{{signupURL}}</a><br><br>If you have any questions or need help getting started, our support team is here for you.<br><br>Let’s build something amazing together.<br><br>Best regards,<br>The ViFit Team",
      TextPart: "Hi {{name}},\n\nWelcome to ViFit Studio Management! We are thrilled to have your studio join our growing Pilates community.\n\nYour account has been successfully created. To get started, simply log in and complete your studio setup so you can begin managing classes and accepting bookings.\n\n{{signupURL}}\n\nIf you have any questions or need help getting started, our support team is here for you.\n\nLet’s build something amazing together.\n\nBest regards,\nThe ViFit Team",
    } as Template;
    const listTemplatesResponse = await listTemplates(new ListTemplatesCommand({MaxItems: 10}));
    const found = listTemplatesResponse.TemplatesMetadata!.find(o => o.Name === template.TemplateName);
    if (found) {
      await updateTemplate(template);
    } else {
      await createTemplate(template);
    }
  }
}

new Main().execute()
  .then(value => logger.debug("Main.execute()"))
  .catch(error => logger.error(`Main() error! ${JSON.stringify(error)}`));
