import { BookingFees } from "@booking-api/models/search";
import { Session } from "@booking-api/models/session";
import { By } from "@shared/models";
import { BookingCancel, SessionStatus } from "@booking-api/models/booking";

export interface Event {
  id?: string,
  studioId: string,
  studio?: EventStudio,
  type: string,
  bookingRefNo?: string,
  bookingStatus?: string,
  paymentReqId?: string,
  bookingFees?: BookingFees,
  promoCode?: string,
  name?: string,
  internalRemarks?: string,
  countryCode?: string,
  timezone: string,
  startDate: string,
  startTime?: string,
  endDate: string,
  endTime?: string,
  isFullDay?: boolean,
  equipmentId?: string,
  equipment?: EventEquipment,
  instructorId?: string,
  instructor?: EventInstructor,
  memberId?: string,
  member?: EventMember,
  holdAt?: Date,
  holdEnds?: Date,
  cancellable?: boolean,
  //
  cancelled?: boolean,
  cancelledAt?: string,
  cancelledById?: string,
  cancelledBy?: EventCancelBy,
  cancelledWithRefund?: boolean,
  cancelReason?: string,
  cancelReq?: BookingCancel,
  //
  instrNoShow?: boolean,
  instrNoShowAt?: string,
  instrNoShowById?: string,
  instrNoShowRemarks?: string,
  //
  doorPin?: string,
  //
  session: Session;
  //
  status: SessionStatus;
  //
  bookedByAdmin?: boolean;
}

export interface EventEquipment {
  name: string,
  code: string,
  privacy: boolean,
  type?: EventEquipmentType,
}

export interface EventEquipmentType {
  id: string,
  name: string,
}

export interface EventStudio {
  name: string,
  address: string,
  placeId: string,
  lat: string,
  lng: string,
}

export interface EventInstructor {
  fullName: string,
}

export interface EventMember {
  fullName: string,
}

export interface EventFindCriteria {
  studioId: string,
  startDateFrom?: string,
  endDateTo?: string,
  equipmentId?: string,
  inclCancelled?: boolean,
  searchTerm?: string,
}

export interface EventSaveReq {
  event: Event,
  by: By,
}

export enum EventCancelBy {
  Studio,
  Instructor,
  Member,
  Backoffice,
}

// Determine the event is member's booking or instructor's session
export enum EventType {
  Booking = "Booking",
  Session = "Session",
}

export interface EventCancelReq {
  eventId: string,
  studioId?: string,
  instrId?: string,
  memberId?: string,
  cancelBy: EventCancelBy,
  cancelFree?: boolean,
  cancelReason: string,
  by: By,
}

export interface EventNoShowReq {
  eventId: string,
  memberId?: string,
  remarks: string,
  by: By,
}
