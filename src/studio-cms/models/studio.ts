import { Geocode<PERSON><PERSON>ult } from "@googlemaps/google-maps-services-js";
import { By } from "@shared/models";
import { LatLng } from "@booking-api/models/search";
import { SocialMedia } from "./shared";

export interface Studio {
  id: string,
  name: string,
  descr: string,
  contactNo: string,
  email: string,
  address: string,
  placeId: string,
  geocode?: GeocodeResult,
  latlng?: LatLng,
  distance?: number,
  socialMedia: SocialMedia,
  usagePolicies: string,
  instrLess?: boolean,
  pictures: string[];
  videos: string[];
  active?: boolean,
  pendingApproval?: boolean,
  hasDoorPin?: boolean,
  deleted?: boolean,
  avgRating?: number,
  totalRatingCount?: number,
  equipments?: StudioEquipment[],
  fav?: boolean,
}

export interface StudioEquipment {
  studioId: string,
  typeId: string,
  typeName: string,
}

export interface StudioSaveReq {
  studio: Studio,
  by: By,
}

export interface MainUserUpdateReq {
  studioId: string,
  mainUserId: string,
  by: By,
}
