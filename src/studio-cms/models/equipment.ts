import { By } from "@shared/models";

export interface EquipmentType {
  id: string,
  name: string,
}

export interface EquipmentSimple {
  id?: string,
  studioId: string,
  type: EquipmentType,
  code: string,
  privacy: boolean,
  deleted?: boolean,
}

export interface Equipment extends EquipmentSimple {
  privacy: boolean,
  startDate?: string,
  endDate?: string,
  availability: Availability[],
}

export interface EquipmentFindCriteria {
  studioId: string,
  typeId?: string,
  availableNow?: boolean,
  inclDeleted?: boolean,
  isPrivacy?: boolean,
  searchTerm?: string,
}

export interface EquipmentSaveReq {
  equipment: Equipment,
  by: By,
}

export interface EquipmentSetDeletedReq {
  equipmentId: string,
  studioId: string,
  deleted: boolean,
  by: By,
}

//////////////////
// Availability //
//////////////////

export interface Availability {
  type: string,
  slots: AvailabilitySlot[],
}

export interface AvailabilitySlot {
  start: string,
  end: string,
  price: string,
}

export interface AvailabilitySaveReq {
  equipmentId: string,
  availability: Availability[],
  by: By,
}
