import { By } from "@shared/models";

export interface User {
  id?: string,
  firebaseUid: string,
  firebaseEmail: string,
  studioId: string,
  mainUser?: boolean,
  name: string,
  jobTitle: string,
  contactNo: string,
  admin?: boolean,
  active?: boolean,
  emailVerified?: boolean,
}

export interface UserFindCriteria {
  studioId: string,
  isAdmin?: boolean,
  isActive?: boolean,
  searchTerm?: string,
}

export interface UserSaveReq {
  user: User,
  by: By,
}

export interface UserSetActiveReq {
  userId: string,
  studioId: string,
  active: boolean,
  by: By,
}

export interface FirebaseUidSetReq {
  userId: string,
  firebaseUid: string,
  firebaseEmail: string,
  by: By,
}
