import { Response, NextFunction, RequestHandler } from "express";

import { getIdToken } from "../../init-openapi";
import { ApiRequest } from "@shared/constants";
import { ServiceError } from "@shared/services";
import { UserService } from "@studio-cms/services/user-service";

const userService = new UserService();

export const requireStudioAccess = (handler: RequestHand<PERSON>): RequestHandler => {
  return async (req: ApiRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const idToken = getIdToken(req);

      if (!idToken.email_verified) {
        throw new ServiceError("401", `email not verified`);
      }
      const user = await userService.findByFirebaseEmail(idToken.email!);
      if (!user || !user.active || !user.admin) {
        throw new ServiceError("401", `not authorised to access studio`);
      }

      req.idToken = idToken;
      req.auth = { ...user, countryCode: 'SG' }; // TODO: get country code from stu01studios table

      await handler(req, res, next);
    } catch (err) {
      res.status(err.code || 500).json({ success: false, message: err.message });
      throw err;
    }
  };
}
