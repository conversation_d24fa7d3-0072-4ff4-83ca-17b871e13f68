import { Connection, RowDataPacket } from "mysql2/promise";
import { EventEquipment, EventInstructor, EventMember, EventStudio } from "../models/event";

export class EventSupportRepo {

  async findEventStudioById(conn: Connection, id: string): Promise<EventStudio|undefined> {
    const sql = "select * from stu01studios where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toEventStudio(results[0]);
  }

  private toEventStudio(row: any): EventStudio {
    return {
      name: row['name'],
      address: row['address']!,
      placeId: row['place_id']!,
      lat: row['lat']!,
      lng: row['lng']!,
    } as EventStudio;
  }

  async findEventEquipmentById(conn: Connection, id: string): Promise<EventEquipment|undefined> {
    const sql = "select * from vw_stu04equipment where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toEventEquipment(results[0]);
  }

  private toEventEquipment(row: any): EventEquipment {
    return {
      code: row['code'],
      name: row['type_name'],
      privacy: !!row['privacy'],
    } as EventEquipment;
  }

  async findEventInstructorById(conn: Connection, id: string): Promise<EventInstructor|undefined> {
    const sql = "select * from instr01instructors where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toEventInstructor(results[0]);
  }

  private toEventInstructor(row: any): EventInstructor {
    return {
      fullName: row['name'],
    } as EventMember;
  }

  async findEventMemberById(conn: Connection, id: string): Promise<EventMember|undefined> {
    const sql = "select * from usr01members where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toEventMember(results[0]);
  }

  private toEventMember(row: any): EventMember {
    return {
      fullName: row['full_name'],
    } as EventMember;
  }
}
