import { Page, PageOfData, Sort } from "@shared/services";
import { BookingFees } from "@booking-api/models/search";
import { convertMySQLDateToTimezoneString, convertMySQLTimeToTimezoneString } from "@shared/utils/timezone-formatter";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import { Event, EventCancelBy, EventCancelReq, EventFindCriteria, EventNoShowReq, EventSaveReq } from "../models/event";
import { SessionFeedbackFindCriteria, SessionFeedback, GroupRating } from "@public-api/models/public";
import { StudioRepo } from "@studio-cms/repo/studio-repo";
import { EquipmentRepo } from "./equipment-repo";
import { EventSupportRepo } from "./event-support-repo";
import { DateTime } from "luxon";

export class EventRepo {

  private readonly equipRepo = new EquipmentRepo();
  private readonly supportRepo = new EventSupportRepo();

  async findById(conn: Connection, id: string): Promise<Event|undefined> {
    const sql = "select * from evt01events where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    const ret = EventRepo.toEvent(results[0]);
    await this.addAssociatedItems(conn, ret);
    return ret;
  }

  private async addAssociatedItems(conn: Connection, event: Event) {
    if (event.studioId) {
      event.studio = await this.supportRepo.findEventStudioById(conn, event.studioId);
    }
    if (event.equipmentId) {
      event.equipment = await this.supportRepo.findEventEquipmentById(conn, event.equipmentId);
    }
    if (event.instructorId) {
      event.instructor = await this.supportRepo.findEventInstructorById(conn, event.instructorId);
    }
    if (event.memberId) {
      event.member = await this.supportRepo.findEventMemberById(conn, event.memberId);
    }
  }

  async findAll(
    conn: Connection,
    criteria: EventFindCriteria,
  ): Promise<Event[]> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    if (criteria.studioId) {
      clauses.push("(studio_id = ?)");
      params.push(criteria.studioId);
    }
    if (criteria.startDateFrom) {
      clauses.push("(start_date >= ?)");
      params.push(criteria.startDateFrom);
    }
    if (criteria.endDateTo) {
      clauses.push("(end_date <= ?)");
      params.push(criteria.endDateTo);
    }
    if (criteria.equipmentId) {
      clauses.push("(equip_id = ?)");
      params.push(criteria.equipmentId);
    }
    if (!criteria.inclCancelled) {
      clauses.push("(cancelled is false)");
    }
    if (criteria.searchTerm) {
      clauses.push("("
        + "(booking_ref = ?)" // (!) exact
        + ")");
      params.push(criteria.searchTerm);
    }
    clauses.push("(deleted is false)"); // (!) always deleted=false
    // logger.debug(`clauses=${clauses}`);

    // select items
    let selectSql = "select * from evt01events";
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }
    /*
    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    */
    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = [];
    for (const result of results) {
      const ret = EventRepo.toEvent(result);
      await this.addAssociatedItems(conn, ret);
      items.push(ret);
    }
    return items;

    /*
    // select total
    let totalSql = "select count(*) as `count` from evt01events";
    if (clauses.length !== 0) {
      totalSql += ` where ${clauses.join(" and ")}`
    }
    const [results2] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = results2[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;

    // return page of data
    return {
      totalItems: totalItems,
      totalPages: totalPages,
      items: items,
    } as PageOfData<Event>;
    */
  }

  static toEvent(row: any): Event {
    const bookingFees = row['booking_fees'];
    const timezone = row['timezone'] ?? 'Asia/Singapore';

    return {
      id: row['id'],
      studioId: row['studio_id'],
      type: row['type'],
      bookingRefNo: row['booking_ref'],
      bookingStatus: row['booking_status'],
      paymentReqId: row['payment_req_id'],
      bookingFees: bookingFees ? JSON.parse(bookingFees) as BookingFees : undefined,
      promoCode: row['promo_code'] ?? null,
      name: row['name'],
      internalRemarks: row['int_remarks'],
      countryCode: row['country_code'] ?? 'SG',
      timezone: timezone,
      startDate: convertMySQLDateToTimezoneString(row['start_date']),
      startTime: convertMySQLTimeToTimezoneString(row['start_time']),
      endDate: convertMySQLDateToTimezoneString(row['end_date']),
      endTime: convertMySQLTimeToTimezoneString(row['end_time']),
      isFullDay: row['full_day'] === 1,
      equipmentId: row['equip_id'],
      instructorId: row['instr_id'],
      memberId: row['member_id'],
      //
      cancelled: row['cancelled'] === 1,
      cancelledAt: row['cancelled_at'],
      cancelledById: row['cancelled_by_id'],
      cancelReason: row['cancel_reason'],
      cancelledWithRefund: row['cancelled_with_refund'] === 1,
      bookedByAdmin: row['booked_by_admin'] === 1,
      //
      instrNoShow: row['i_noshow'] === 1,
      instrNoShowAt: row['i_noshow_at'],
      instrNoShowById: row['i_noshow_by_id'],
      instrNoShowRemarks: row['i_noshow_remarks'],
    } as Event;
  }

  async create(
    conn: Connection,
    req: EventSaveReq,
    fees?: BookingFees,
  ): Promise<string> {
    const sql = `insert into evt01events (
        id, studio_id, type, name, int_remarks,
        start_date, start_time, end_date, end_time, full_day,
        booking_ref, booking_status, payment_req_id, booking_fees,
        equip_id,
        member_id, instr_id,
        hold_at, hold_ends,
        created_by, booked_by_admin, timezone, country_code, promo_code
      ) values (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?
      )`;
    const by = req.by.username;
    const event = req.event;
    const uuid = crypto.randomUUID();
    logger.debug(`[${by}] create() event=${JSON.stringify(event)}`)
    const params = [
      uuid,
      event.studioId,
      event.type,
      event.name ?? '',
      event.internalRemarks ?? '',
      event.startDate,
      event.startTime ?? null,
      event.endDate,
      event.endTime ?? null,
      event.isFullDay ?? false,
      event.bookingRefNo ?? null,
      event.bookingStatus ?? null,
      event.paymentReqId ?? null,
      fees ? JSON.stringify(fees) : null,
      event.equipmentId ?? null,
      event.memberId ?? null,
      event.instructorId ?? null,
      event.holdAt ? DateTime.fromJSDate(event.holdAt).toISO() : null,
      event.holdEnds ? DateTime.fromJSDate(event.holdEnds).toISO() : null,
      by,
      event.bookedByAdmin ?? false,
      event.timezone ?? 'Asia/Singapore',
      event.countryCode ?? 'SG',
      event.promoCode ?? null,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created event, id=${uuid}, numRows=${results.affectedRows}`);
    return uuid;
  }

  async update(conn: Connection, req: EventSaveReq): Promise<number> {
    const sql = `update evt01events set
      type = ?, name = ?, int_remarks = ?,
      start_date = ?, start_time = ?, end_date = ?, end_time = ?, full_day = ?,
      equip_id = ?,
      updated_by = ?
      where id = ? and studio_id = ?`;
    const by = req.by.username;
    const event = req.event;
    logger.debug(`[${by}] update() event=${JSON.stringify(event)}`)
    const params = [
      event.type,
      event.name ?? '',
      event.internalRemarks ?? '',
      event.startDate,
      event.startTime ?? null,
      event.endDate,
      event.endTime ?? null,
      event.isFullDay ?? false,
      event.equipmentId ?? null,
      by,
      event.id,
      event.studioId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated event, id=${event.id}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async cancel(conn: Connection, req: EventCancelReq): Promise<number> {
    let whereClause: string;
    let whereParams: string;
    const cancelBy = req.cancelBy!;
    if (cancelBy === EventCancelBy.Studio) {
      whereClause = "studio_id = ?";
      whereParams = req.studioId!;
    } else if (cancelBy === EventCancelBy.Instructor) {
      whereClause = "instr_id = ?";
      whereParams = req.instrId!;
    } else if (cancelBy === EventCancelBy.Member) {
      whereClause = "member_id = ?";
      whereParams = req.memberId!;
    } else {
      throw `Unexpected req.cancelBy! value=${EventCancelBy[req.cancelBy]}`;
    }

    const sql = `update evt01events set
      cancelled = ?, cancelled_at = now(),
      cancelled_by = ?, cancelled_by_id = ?, cancel_reason = ?,
      cancel_free = ?,
      updated_by = ?
      where id = ? and ${whereClause}
      and cancelled is not true`;
    const by = req.by.username;
    logger.debug(`[${by}] cancel() req=${JSON.stringify(req)}`)
    const params = [
      true,              // cancelled
      EventCancelBy[req.cancelBy], // cancelled_by (Studio, Instructor, Member)
      req.by.uid,        // cancelled_by_id
      req.cancelReason,  // cancel_reason
      req.cancelFree ?? null, // cancel_free, default null
      by,                // updated_by
      req.eventId,
      whereParams,       // (!) important
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] cancelled event, id=${req.eventId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async noShow(conn: Connection, req: EventNoShowReq): Promise<number> {
    const sql = `update evt01events set
      i_noshow = ?, i_noshow_at = now(),
      i_noshow_by_id = ?, i_noshow_remarks = ?,
      updated_by = ?
      where id = ? and member_id = ?
      and i_noshow is not true`;
    const by = req.by.username;
    logger.debug(`[${by}] noShow() req=${JSON.stringify(req)}`)
    const params = [
      true,              // i_noshow
      req.by.uid,        // i_noshow_by_id
      req.remarks,       // i_noshow_remarks
      by,                // updated_by
      req.eventId,
      req.memberId!,     // member_id
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] noShowed event, id=${req.eventId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async findReviews(
    conn: Connection,
    criteria: SessionFeedbackFindCriteria,
    sort?: Sort,
    page?: Page,
  ): Promise<PageOfData<SessionFeedback>> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    let select = "select s.event_id, e.member_id, m_has_feedback, m_studio_rating, m_studio_feedback, m_feedback_at";
    // select items
    let sql = " from evt02sessions s INNER JOIN evt01events e ON e.id = s.event_id";
    { // member given feedback and have rating and is not private for ViFit
      clauses.push("(s.m_has_feedback is true)");
      clauses.push("(s.is_private is false)");
      clauses.push("(s.m_studio_feedback <> '')");
    }

    if (criteria.studioId) {
      clauses.push("(e.studio_id = ?)");
      params.push(criteria.studioId);
    }

    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`
    }

    let paginationSQL = select + sql;
    if (sort) {
      const sortDir = sort.sortAsc ? 'asc' : 'desc';
      const lookup = new Map<string, string>();
      lookup.set("feedbackAt"    , ` order by m_feedback_at ${sortDir}`);
      if (lookup.has(sort.sort)) {
        paginationSQL += lookup.get(sort.sort);
      }
    }
    if (page) {
      paginationSQL += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }

    const [results] = await conn.query<RowDataPacket[]>(paginationSQL, params);
    const items = [];
    for (const result of results) {
      const ret = StudioRepo.toSessionFeedback(result);
      if (ret.memberId) {
        ret.member = await this.supportRepo.findEventMemberById(conn, ret.memberId);
      }
      items.push(ret);
    }

    // select total
    let totalSql = "select count(*) as `count` " + sql;
    const [resultsTotal] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = resultsTotal[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;

    // return page of data
    return {
      totalItems: totalItems,
      totalPages: totalPages,
      items: items,
    } as PageOfData<SessionFeedback>;
  }

  /**
  * @returns cancellation penalty fee (%) percent
  */
  async findCancellationFee(conn: Connection): Promise<number> {
    const sql = "select * from app05fees where code = ?";
    const params = [
      "booking_cancel"
    ];

    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    const price = results?.[0]?.['price'] ?? 0;
    return +price;
  }

  async updateCancelledWithRefund(conn: Connection, eventId: string, value: boolean, by: string): Promise<number> {
    const sql = `update evt01events set
      cancelled_with_refund = ?,
      updated_by = ?
      where id = ?`;
    logger.debug(`[${by}] updateCancelledWithRefund() eventId=${eventId}, value=${value}`)
    const params = [
      value,
      by,
      eventId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated cancelled_with_refund, eventId=${eventId}, numRows=${affectedRows}`);
    return affectedRows;
  }
}
