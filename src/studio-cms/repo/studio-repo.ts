import { LatLng, SearchStudio } from "@booking-api/models/search";
import { GeocodeResult } from "@googlemaps/google-maps-services-js";

import { Picture, PictureSaveReq } from "@shared/models";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import { SocialMedia } from "../models/shared";
import { MainUserUpdateReq, Studio, StudioSaveReq, StudioEquipment } from "../models/studio";
import { SessionFeedback } from "@public-api/models/public";
import { StudioConfigSaveReq, StudioConfig } from "@studio-cms/models/studio-config";

export class StudioRepo {

  async findById(conn: Connection, id: string): Promise<Studio|undefined> {
    const sql = "select * from stu01studios where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return StudioRepo.toStudio(results[0]);
  }

  static toStudio(row: any): Studio {
    const geocode = row['geocode'];
    return {
      id: row['id'],
      name: row['name'],
      descr: row['descr'],
      contactNo: row['contact_no'],
      email: row['email'],
      address: row['address'],
      placeId: row['place_id'],
      geocode: geocode == null ? null : JSON.parse(geocode) as GeocodeResult,
      latlng: row['lat'] && row['lng']? new LatLng(
        +row['lat'],
        +row['lng'],
      ): null,
      distance: row['distance'] ?? null,
      socialMedia: {
        facebook: row['facebook'],
        instagram: row['instagram'],
        website: row['website'],
      } as SocialMedia,
      usagePolicies: row['usage'],
      instrLess: row['instrless'] === 1,
      pictures: JSON.parse(row['pictures'] ?? '[]'),
      hasDoorPin: row['has_door_pin'] == 1,
      active: row['active'] === 1,
      pendingApproval: row['pend_approval'] == 1,
      deleted: row['deleted'] === 1,
    } as Studio;
  }

  static toSessionFeedback(row: any): SessionFeedback {
    return {
      rating: row['m_studio_rating'],
      feedback: row['m_studio_feedback'],
      feedbackAt: row['m_feedback_at'],
      memberId: row['member_id'] ?? ""
    }
  }

  static toEquipment(row: any): StudioEquipment {
    return {
      studioId: row['studio_id'],
      typeId: row['type_id'],
      typeName: row['type_name'],
    }
  }


  async create(conn: Connection, req: StudioSaveReq): Promise<string> {
    const sql = `insert into stu01studios (
        id, name, descr, contact_no, email, address, place_id,
        geocode, lat, lng, latlng,
        facebook, instagram, website, \`usage\`,
        instrless,
        pictures, videos,
        pend_approval,
        created_by
      ) values (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
      )`;
    const by = req.by.username;
    const studio = req.studio;
    const lat = studio.geocode?.geometry.location.lat ?? null;
    const lng = studio.geocode?.geometry.location.lng ?? null;
    const latLng = (lat && lng) ? new LatLng(lat, lng) : null;
    const uuid = crypto.randomUUID();
    logger.debug(`[${by}] create() studio=${JSON.stringify(studio)}`)
    const params = [
      uuid,
      studio.name,
      studio.descr ?? '',
      studio.contactNo,
      studio.email,
      studio.address,
      studio.placeId,
      JSON.stringify(studio.geocode) ?? null,
      lat,
      lng,
      latLng?.toCacheString() ?? null,
      studio.socialMedia?.facebook ?? '',
      studio.socialMedia?.instagram ?? '',
      studio.socialMedia?.website ?? '',
      studio.usagePolicies ?? '',
      studio.instrLess ?? false,
      studio.pictures ?? '[]',
      studio.videos ?? '[]',
      true, // (!) pending approval
      by,   // created_by
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created studio, id=${uuid}, numRows=${results.affectedRows}`);
    return uuid;
  }

  async update(conn: Connection, req: StudioSaveReq): Promise<number> {
    const sql = `update stu01studios set
      name = ?, descr = ?, contact_no = ?, email = ?, address = ?, place_id = ?,
      geocode = ?, lat = ?, lng = ?, latlng = ?,
      facebook = ?, instagram = ?, website = ?, \`usage\` = ?,
      instrless = ?,
      pictures = ?, videos = ?,
      pend_approval = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const studio = req.studio;
    const lat = studio.geocode?.geometry.location.lat ?? null;
    const lng = studio.geocode?.geometry.location.lng ?? null;
    const latLng = (lat && lng) ? new LatLng(lat, lng) : null;
    logger.debug(`[${by}] update() studio=${JSON.stringify(studio)}`)
    const params = [
      studio.name,
      studio.descr ?? '',
      studio.contactNo,
      studio.email,
      studio.address,
      studio.placeId,
      JSON.stringify(studio.geocode) ?? null,
      lat,
      lng,
      latLng?.toCacheString() ?? null,
      studio.socialMedia?.facebook ?? '',
      studio.socialMedia?.instagram ?? '',
      studio.socialMedia?.website ?? '',
      studio.usagePolicies ?? '',
      studio.instrLess ?? false,
      studio.pictures ?? '[]',
      studio.videos ?? '[]',
      true, // (!) pending approval
      by,   // updated_by
      studio.id,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated studio, id=${studio.id}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async updateMainUserId(conn: Connection, req: MainUserUpdateReq): Promise<number> {
    const sql = `update stu01studios
        set main_user_id = ?,
        updated_by = ?
        where id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] updateMainUserId() req=${JSON.stringify(req)}`)
    const params = [
      req.mainUserId!,
      by, // updated_by
      req.studioId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);

    const affectedRows = results.affectedRows;
    if (affectedRows !== 1) {
      throw `updateMainUserId(): expected exactly 1 row affected, actual=${affectedRows}`;
    }

    logger.info(`[${by}] updated main user ID:`
      + ` studioId=${req.studioId}, userId=${req.mainUserId}, numRows=${results.affectedRows}`);
    return affectedRows;
  }

  async insertPicture(conn: Connection, req: PictureSaveReq): Promise<string> {
    const sql = `insert into stu06pictures (
        id, user_id, path, created_by
      ) values (
        ?, ?, ?, ?
      )`;
    const by = req.by.username;
    const uuid = crypto.randomUUID();
    logger.debug(`[${by}] insertPicture() req=${JSON.stringify(req)}`)
    const params = [
      uuid,
      req.userId,
      req.path,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] inserted picture, id=${uuid}, numRows=${results.affectedRows}`);
    return uuid;
  }

  async findPictureById(conn: Connection, id: string): Promise<Picture|undefined> {
    const sql = "select *  from stu06pictures where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toPicture(results[0]);
  }

  async findPictureByIds(conn: Connection, ids: string[] ): Promise<Picture[]> {
    const placeholders = ids.map(() => '?').join(',');
    const sql = `select *  from stu06pictures where id in (${placeholders})`;
    const params = ids;
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return [];
    }
    return results.map(this.toPicture);
  }

  private toPicture(row: any): Picture {
    return {
      id: row['id'],
      userId: row['userId'],
      path: row['path'],
    } as Picture;
  }

  async prefixStudioPictures(conn: Connection, pictureIds: string[]): Promise<string[]> {
    if (!pictureIds || pictureIds.length === 0) {
      return [];
    }
    const pictures = await this.findPictureByIds(conn, pictureIds);
    return pictures.map(picture =>
      `${process.env.PIC_URL_PREFIX!}/studio-pics/${picture.path}`
    );
  }

  /**
   * Get all config as an array of StudioConfig for a studio
   */
  async getStudioConfig(conn: Connection, studioId: string): Promise<StudioConfig[]> {
    const sql = `SELECT studio_id, config_key, config_value, created_by, updated_by, created_at, updated_at FROM stu07studio_config WHERE studio_id = ?`;
    const [results] = await conn.query<RowDataPacket[]>(sql, [studioId]);
    return results.map(row => ({
      studioId: row.studio_id,
      key: row.config_key,
      value: row.config_value,
      createdBy: row.created_by,
      updatedBy: row.updated_by,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    }));
  }

  /**
   * Set a config value for a studio (insert or update), with by tracking
   */
  async setStudioConfig(conn: Connection, req: StudioConfigSaveReq): Promise<number> {
    const sql = `INSERT INTO stu07studio_config (studio_id, config_key, config_value, created_by, updated_by)
      VALUES (?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), updated_by = VALUES(updated_by), updated_at = CURRENT_TIMESTAMP`;
    const by = req.by.username;
    const params = [req.studioId, req.key, req.value, by, by];
    logger.info(`[${by}] setStudioConfig() studioId=${req.studioId}, key=${req.key}, value=${req.value}`);
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    return results.affectedRows;
  }

  /*
  async deletePicture(conn: Connection, req: PictureDeleteReq): Promise<number> {
    const sql =
// sql
`delete from stu06pictures
  where id = ? and user_id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] deletePicture() req=${JSON.stringify(req)}`)
    const params = [
      req.pictureId,
      req.userId,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] deleted picture, id=${req.pictureId}, numRows=${affectedRows}`);
    return affectedRows;
  }
  */
}
