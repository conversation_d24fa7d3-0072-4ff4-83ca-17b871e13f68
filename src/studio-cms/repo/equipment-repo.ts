import { toMySQLDate } from "@shared/date-time";
import { Page, PageOfData } from "@shared/services";
import { convertMySQLDateToTimezoneString } from "@shared/utils/timezone-formatter";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import {
  Availability,
  AvailabilitySaveReq,
  AvailabilitySlot,
  Equipment,
  EquipmentFindCriteria,
  EquipmentSaveReq,
  EquipmentSetDeletedReq,
  EquipmentSimple,
  EquipmentType
} from "../models/equipment";
import { DateTime } from "luxon";

export class EquipmentRepo {

  async findTypes(conn: Connection): Promise<EquipmentType[]> {
    const sql = "select * from stu03equip_types where deleted is false order by name";
    const [results] = await conn.query<RowDataPacket[]>(sql);
    return results.map(o => this.toEquipmentType(o));
  }

  private toEquipmentType(row: any): EquipmentType {
    return {
      id: row['id'],
      name: row['name'],
    } as EquipmentType;
  }

  async findById(conn: Connection, id: string): Promise<Equipment|undefined> {
    const sql = "select * from vw_stu04equipment where id = ?";
    const params = [id];
    const [results, fields] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    const ret = this.toEquipment(results[0]);
    ret.availability = await this.findAvailability(conn, id);
    return ret;
  }

  // (!) simple
  async findAllSimple(
    conn: Connection, studioId: string, inclDeleted: boolean
  ): Promise<EquipmentSimple[]> {
    let selectSql = "select * from vw_stu04equipment";
    const clauses = [];
    const params = [];
    if (!inclDeleted) {
      clauses.push("(deleted is false)");
    }
    {
      clauses.push("(studio_id = ?)");
      params.push(studioId);
    }

    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }
    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    return results.map(result => this.toEquipmentSimple(result));
  }

  async findAll(
    conn: Connection,
    criteria: EquipmentFindCriteria,
    page?: Page,
  ): Promise<PageOfData<Equipment>> {
    // prepare clauses and params
    const now = new Date();
    const clauses = [];
    const params = [];
    if (criteria.studioId) {
      clauses.push("(studio_id = ?)");
      params.push(criteria.studioId);
    }
    if (criteria.typeId) {
      clauses.push("(type_id = ?)");
      params.push(criteria.typeId);
    }
    if (criteria.availableNow) {
      clauses.push("("
        + "(start_date is null or start_date <= ?)"
        + " and (end_date is null or end_date >= ?)"
        + ")"
      );
      params.push(toMySQLDate(now));
      params.push(toMySQLDate(now));
    }
    if (!criteria.inclDeleted) {
      clauses.push("(deleted is false)");
    }

    if (criteria.isPrivacy !== undefined) {
      clauses.push("(privacy is ?)");
      params.push(!!criteria.isPrivacy);
    }
    if (criteria.searchTerm) {
      clauses.push("("
        + "(code like ?)"
        + " or (short_name like ?)"
        + " or (name like ?)"
        + " or (descr like ?)"
        + " or (privacy like ?)"
        + " or (type_name like ?)"
        + ")");
      for (let i = 0; i < 6; i++) {
        params.push(`%${criteria.searchTerm}%`);
      }
    }

    // select items
    let selectSql = "select * from vw_stu04equipment";
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }
    selectSql += ` order by type_name, name`;
    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    const [results1] = await conn.query<RowDataPacket[]>(selectSql, params);
    const items = [];
    for (const result of results1) {
      const ret = this.toEquipment(result);
      ret.availability = await this.findAvailability(conn, ret.id!);
      items.push(ret);
    }

    // select total
    let totalSql = "select count(*) as `count` from vw_stu04equipment";
    if (clauses.length !== 0) {
      totalSql += ` where ${clauses.join(" and ")}`
    }
    const [results2] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = results2[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;

    // return page of data
    return {
      totalItems: totalItems,
      totalPages: totalPages,
      items: items,
    } as PageOfData<Equipment>;
  }

  // (!) simple
  private toEquipmentSimple(row: any): EquipmentSimple {
    return {
      id: row['id'],
      studioId: row['studio_id'],
      type: {
        id: row['type_id'],
        name: row['type_name'],
      } as EquipmentType,
      code: row['code'],
      privacy: row['privacy'],
      deleted: row['deleted'] === 1,
    } as EquipmentSimple;
  }

  private toEquipment(row: any): Equipment {
    return {
      id: row['id'],
      studioId: row['studio_id'],
      type: {
        id: row['type_id'],
        name: row['type_name'],
      } as EquipmentType,
      code: row['code'],
      deleted: row['deleted'] === 1,
      // ...
      privacy: row['privacy'],
      startDate: convertMySQLDateToTimezoneString(row['start_date']),
      endDate: convertMySQLDateToTimezoneString(row['end_date']),
      // ... availability
    } as Equipment;
  }

  async create(conn: Connection, req: EquipmentSaveReq): Promise<string> {
    const sql = `insert into stu04equipment (
        id, studio_id, type_id, code, privacy, start_date, end_date, created_by
      ) values (
        ?, ?, ?, ?, ?, ?, ?, ?
      )`;
    const by = req.by.username;
    const equipment = req.equipment;
    const uuid = crypto.randomUUID();
    logger.debug(`[${by}] create() equipment=${JSON.stringify(equipment)}`)
    const params = [
      uuid,
      equipment.studioId,
      equipment.type.id,
      equipment.code,
      equipment.privacy ?? 0,
      equipment.startDate ?? null,
      equipment.endDate ?? null,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created equipment, id=${uuid}, numRows=${results.affectedRows}`);
    return uuid;
  }

  async update(conn: Connection, req: EquipmentSaveReq): Promise<number> {
    const sql = `update stu04equipment set
      code = ?, privacy = ?, start_date = ?, end_date = ?, updated_by = ?
      where id = ? and studio_id = ?`;
    const by = req.by.username;
    const equipment = req.equipment;
    logger.debug(`[${by}] update() equipment=${JSON.stringify(equipment)}`)
    const params = [
      equipment.code,
      equipment.privacy ?? 0,
      equipment.startDate ?? null,
      equipment.endDate ?? null,
      by,
      equipment.id!,
      equipment.studioId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated equipment, id=${equipment.id}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setDeleted(conn: Connection, req: EquipmentSetDeletedReq): Promise<number> {
    const sql = `update stu04equipment set
      deleted = ?, deleted_by = ?, deleted_at = ?,
      updated_by = ?
      where id = ? and studio_id = ?`;
    const by = req.by.username;
    const params = req.deleted ? [
      true,              // deleted
      by,                // deleted_by
      DateTime.now().toISO(),
      by,                // updated_by
      req.equipmentId,
      req.studioId, // (!) important
    ] : [
      false, // deleted
      null,  // deleted_by
      null,  // deleted_at
      by,    // updated_by
      req.equipmentId,
      req.studioId, // (!) important
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] set equipment deleted=${req.deleted}, id=${req.equipmentId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  //////////////////
  // Availability //
  //////////////////

  private async findAvailability(
    conn: Connection, equipmentId: string,
  ): Promise<Availability[]> {
    const sql =
      `select * from stu05equip_avail
      where equip_id = ?
      order by type, start`;
    const params = [equipmentId];
    const [results, fields] = await conn.query<RowDataPacket[]>(sql, params);
    return this.toAvailability(results);
  }

  private toAvailability(rows: any[]): Availability[] {
    const map: any = {};
    for (const row of rows) {
      const type: string = row['type'];
      const slot = {
        start: row['start'],
        end: row['end'],
        price: row['price'],
      } as AvailabilitySlot;

      const val = map[type];
      if (val) {
        val.push(slot);
      } else {
        map[type] = [slot];
      }
    }

    const ret: Availability[] = [];
    Object.keys(map).forEach((key: string) => {
      ret.push({
        type: key,
        slots: map[key] as AvailabilitySlot[],
      } as Availability);
    });
    return ret;
  }

  async deleteAvailability(
    conn: Connection, req: AvailabilitySaveReq
  ): Promise<number> {
    const sql = "delete from stu05equip_avail where equip_id = ?";
    const by = req.by.username;
    const params = [req.equipmentId];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] deleted availability, equipmentId=${req.equipmentId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async insertAvailability(
    conn: Connection, req: AvailabilitySaveReq
  ): Promise<void> {
    const sql =
`insert into stu05equip_avail (
  id, equip_id, type, start, end, price, created_by
) values (
  ?, ?, ?, ?, ?, ?, ?
)`;
    const by = req.by.username;
    const avails = req.availability;
    for (const avail of avails) {
      const type = avail.type;
      for (const slot of avail.slots) {
        const params = [
          crypto.randomUUID(),
          req.equipmentId,
          type,
          slot.start,
          slot.end,
          slot.price,
          by,
        ];
        await conn.execute<ResultSetHeader>(sql, params);
      }
      logger.info(`[${by}] inserted availability, `
        + `type=${type}, equipmentId=${req.equipmentId}, numSlots=${avail.slots.length}`);
    }
  }

  async checkCodeExists(
    conn: Connection, studioId: string, code: string, equipmentId?: string
  ): Promise<boolean> {
    let sql = `select count(code) as \`count\` from stu04equipment`;
    const clauses = [];
    const params = [];

    {
      clauses.push("(studio_id = ?)");
      params.push(studioId);

      clauses.push("(code = ?)");
      params.push(code);
    }

    if(equipmentId) {
      clauses.push("(id <> ?)");
      params.push(equipmentId);
    }
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`
    }
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results[0]['count'] !== 0;
  }
}
