import { toBy } from "@shared/request-parsers";
import { Request<PERSON>and<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EquipmentSetDeletedReq } from "../models/equipment";
import { EquipmentService } from "../services/equipment-service";
import { UserService } from "../services/user-service";

const userService = new UserService();
const equipService = new EquipmentService();

const equipmentSetDeleted: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active || !user.admin) {
    res.status(401).end(); // unauthorized
    return;
  }

  const deleted = req.params.deleted + "";
  await equipService.setDeleted({
    equipmentId: req.params.equipmentId as string,
    studioId: user.studioId, // (!) important
    deleted: (deleted === "true"),
    by: toBy(idToken),
  } as EquipmentSetDeletedReq);
  res.status(200).end();
};

export default equipmentSetDeleted;
