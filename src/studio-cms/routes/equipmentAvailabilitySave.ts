import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EquipmentService } from "../services/equipment-service";
import { UserService } from "../services/user-service";
import { toAvailabilitySaveReq } from "./shared/request-parsers";

const userService = new UserService();
const equipService = new EquipmentService();

const equipmentAvailabilitySave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const body = req.body;

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active || !user.admin) {
    res.status(401).end(); // unauthorized
    return;
  }

  const equipmentId = req.params.equipmentId;
  const ret = await equipService.findById(equipmentId);
  if (!ret || ret.studioId !== user.studioId) {
    res.status(204).end(); // no content
    return;
  }

  const toSave = toAvailabilitySaveReq(body, idToken);
  toSave.equipmentId = equipmentId; // (!) important
  await equipService.saveAvailability(toSave);
  res.status(200).end();
};

export default equipmentAvailabilitySave;
