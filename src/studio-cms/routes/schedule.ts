import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EventService } from "../services/event-service";
import { UserService } from "../services/user-service";
import { toScheduleJson } from "./shared/response-output";

const userService = new UserService();
const eventService = new EventService();

const schedule: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active) {
    res.status(401).end(); // unauthorized
    return;
  }

  const start = req.query['start'];
  const end = req.query['end'];
  const equipmentId = req.query['equipmentId'];
  const inclCancelled = req.query['inclCancelled'] + "";
  const searchTerm = req.query['searchTerm'];
  const ret = await eventService.findAll({
    studioId: user.studioId, // (!) important
    startDateFrom: start! + "",
    endDateTo: end! + "",
    ...equipmentId && { equipmentId: equipmentId + "" },
    inclCancelled: (inclCancelled === "true"),
    ...searchTerm && { searchTerm: searchTerm + "" },
  });
  res.status(200).json(toScheduleJson(ret));
};

export default schedule;
