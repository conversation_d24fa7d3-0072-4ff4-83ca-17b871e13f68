import { toBy } from "@shared/request-parsers";
import { <PERSON>quest<PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EventCancelBy, EventCancelReq } from "../models/event";
import { EventService } from "../services/event-service";
import { UserService } from "../services/user-service";

const userService = new UserService();
const eventService = new EventService();

const eventCancel: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const body = req.body;

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active) {
    res.status(401).end(); // unauthorized
    return;
  }

  try {
    await eventService.cancel({
      eventId: req.params.eventId!,
      studioId: user.studioId, // (!) important
      cancelBy: EventCancelBy.Studio, // (!) important
      cancelReason: body.reason,
      by: toBy(idToken),
    } as EventCancelReq);
    res.status(200).json();
  } catch (error) {
    res.status(400).send(error)
  }
};

export default eventCancel;
