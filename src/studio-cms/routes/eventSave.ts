import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EventService } from "../services/event-service";
import { UserService } from "../services/user-service";
import { toEventSaveReq } from "./shared/request-parsers";

const userService = new UserService();
const eventService = new EventService();

const eventSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const body = req.body;

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active) {
    res.status(401).end(); // unauthorized
    return;
  }

  const toSave = toEventSaveReq(body, idToken);
  toSave.event.studioId = user.studioId; // (!) user's studioId
  if (!toSave.event.id?.trim()) {
    // create
    await eventService.create(toSave);
  } else {
    // update
    await eventService.update(toSave);
  }
  res.status(200).end();
};

export default eventSave;
