import { Response } from "express";

import { ApiRequest } from "@shared/constants";
import { BankService } from "@backoffice-cms/services/bank-account-service";
import { requireStudioAccess } from "@studio-cms/middlewares/studio-access";

const bankService = new BankService();

export default requireStudioAccess(async (req: ApiRequest, res: Response) => {
  const result = await bankService.getFormOptions(req);
  res.status(200).json({
    success: true,
    data: result
  });
});
