import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { UserService } from "../services/user-service";
import { toSelfSaveReq } from "./shared/request-parsers";
import { toUser<PERSON>son } from "./shared/response-output";

const userService = new UserService();

const myProfileSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const email = idToken.email!;
  const body = req.body;

  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }

  const user = await userService.findByFirebaseEmail(email);
  if (!user || !user.active) {
    res.status(401).end(); // unauthorized
    return;
  }

  // Check if this is an invited user (has placeholder Firebase UID)
  const isInvitedUser = user.firebaseUid && user.firebaseUid.startsWith('@');
  
  if (isInvitedUser) {
    // For invited users, we need to update the record using the placeholder ID
    // but set the Firebase UID to the real one
    const toSave = toSelfSaveReq(body, idToken);
    toSave.user.id = user.id.toString(); // Use the placeholder ID from database
    toSave.user.firebaseUid = idToken.uid; // Set to real Firebase UID
    await userService.updateSelfWithFirebaseUid(toSave);
  } else {
    // For regular users, just update profile
    const toSave = toSelfSaveReq(body, idToken);
    toSave.user.id = user.id.toString();
    await userService.updateSelf(toSave);
  }
  
  // Fetch and return the updated user data
  const updatedUser = await userService.findByFirebaseEmail(email);
  res.status(200).json(toUserJson(updatedUser));
};

export default myProfileSave;
