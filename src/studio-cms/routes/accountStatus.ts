import { toBy } from "@shared/request-parsers";
import { FirebaseUidSetReq } from "@studio-cms/models/user";
import logger from "@utils/logger";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { StudioService } from "../services/studio-service";
import { UserService } from "../services/user-service";

const userService = new UserService();
const studioService = new StudioService();

const accountStatus: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const email = idToken.email!;
  // logger.debug(`idToken(uid=${uid}, email=${idToken.email})`);

  const user = await userService.findByFirebaseEmail(email);
  const emailExists = user != null;

  // setFirebaseUid if it is the placeholder one (starting with @)
  if (emailExists && user.firebaseUid.startsWith("@")) {
    try {
      await userService.setFirebaseUid({
        userId: uid,
        firebaseUid: idToken.uid,
        firebaseEmail: idToken.email,
        by: toBy(idToken),
      } as FirebaseUidSetReq);
    } catch (e) {
      logger.warn("accountStatus: unable to setFirebaseUid()!");
    }
  }

  const studio = emailExists
    ? await studioService.findById(user.studioId)
    : undefined;
  const response = {
    emailExists: emailExists,
    studioActive: studio?.active === true,
    accountActive: user?.active === true,
  };
  // logger.debug(`response=${JSON.stringify(response)}`);
  res.status(200).json(response);
};

export default accountStatus;
