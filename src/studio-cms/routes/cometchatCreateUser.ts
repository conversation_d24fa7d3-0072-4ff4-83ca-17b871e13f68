import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { StudioService } from "../services/studio-service";
import { createCometChatUser } from "@utils/cometchat";
import logger from "@utils/logger";
import { UserService } from "../services/user-service";
import { CometChatUser } from "@public-api/models/cometchat";

const userService = new UserService();
const studioService = new StudioService();

const cometchatCreateUser: RequestHandler = async (req, res) => {
    const idToken = getIdToken(req);

    // preconditions
    if (!idToken.email_verified) {
      res.status(401).json({ success: false, message: "email not verified", data: null });
      return;
    }
    const user = await userService.findByFirebaseEmail(idToken.email!);
    if (!user || !user.active || !user.admin) {
      res.status(401).json({ success: false, message: "unauthorized", data: null });
      return;
    }
    const studioId = user.studioId;
    const studio = await studioService.findById(studioId);
    const result = await createCometChatUser({
      uid: studioId,
      email: studio.email,
      name: studio.name,
      tags: ["studio"],
      userType: "studio",
    } as CometChatUser );
    if (!result.success) {
      logger.warn(`[CometChat] Studio user creation failed for studioId=${studioId}: ${result.message}`);
      res.status(500).json(result);
      return;
    }
    res.status(200).json(result);
};

export default cometchatCreateUser; 