import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { UserService } from "../services/user-service";
import { toUser<PERSON><PERSON> } from "./shared/response-output";
import crypto from "crypto";

const userService = new UserService();

const tawkToken: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const email = idToken.email!;

  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }

  const user = await userService.findByFirebaseEmail(email);
  if (!user || !user.active) {
    res.status(204).end(); // no content
  } else {
    // Generate hash using HMAC SHA256
    const hash = crypto
    .createHmac('sha256', process.env.TAWK_API_KEY!)
    .update(user.firebaseEmail)
    .digest('hex');
    res.status(200).json({
        name: user.name,
        email: user.firebaseEmail,
        hash: hash
    });
  }
};

export default tawkToken;
