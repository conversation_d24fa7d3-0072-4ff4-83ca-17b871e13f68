import logger from "@utils/logger";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import path from "path";
import process from "process";
import { StudioService } from "../services/studio-service";
import s3 from "@utils/aws-s3";
import { GetObjectCommand } from "@aws-sdk/client-s3";
import { Readable } from "stream";

const studioService = new StudioService();

// (!) NO SECURITY
const myStudioPictureGet: RequestHandler = async (req, res) => {
  const pictureId = req.params.pictureId;
  const picture = await studioService.findPictureById(pictureId);
  if (!picture) {
    res.status(204).end(); // no content
  }
  try {
    const command = new GetObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET!,
      Key: `studio-pics/${picture.path}`,
    });
    const s3Response = await s3.send(command);
    res.setHeader("Content-Type", s3Response.ContentType || "application/octet-stream");
    (s3Response.Body as Readable).pipe(res);
  } catch (error) {
    logger.error(`(studios-pics) error in myStudioPictureGet! ${error}`);
    res.status(500).end("internal server error");
  }
};

export default myStudioPictureGet;
