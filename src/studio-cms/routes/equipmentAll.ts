import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EquipmentService } from "../services/equipment-service";
import { UserService } from "../services/user-service";
import { toEquipmentSimpleJson } from "./shared/response-output";

const userService = new UserService();
const equipService = new EquipmentService();

const equipmentAll: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active) {
    res.status(401).end(); // unauthorized
    return;
  }
  const inclDeleted = req.query['inclDeleted'] + "";
  const ret = await equipService.findAllSimple(user.studioId, (inclDeleted === "true"));
  res.status(200).json(
    ret.map(o => toEquipmentSimpleJson(o))
  );
};

export default equipmentAll;
