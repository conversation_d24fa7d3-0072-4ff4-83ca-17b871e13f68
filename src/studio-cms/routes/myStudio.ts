import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { StudioService } from "../services/studio-service";
import { UserService } from "../services/user-service";
import { toStudio<PERSON>son } from "./shared/response-output";

const studioService = new StudioService();
const userService = new UserService();

const myStudio: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }

  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active) {
    res.status(204).end(); // no content
    return;
  }

  const studio = await studioService.findById(user.studioId);
  if (!studio) {
    res.status(204).end(); // no content
  } else {
    res.status(200).json(toStudioJson(studio));
  }
};

export default myStudio;
