import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EquipmentService } from "../services/equipment-service";
import { UserService } from "../services/user-service";
import { toEquipmentSaveReq } from "./shared/request-parsers";

const userService = new UserService();
const equipService = new EquipmentService();

const equipmentSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const body = req.body;

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active || !user.admin) {
    res.status(401).end(); // unauthorized
    return;
  }

  const toSave = toEquipmentSaveReq(body, idToken);
  toSave.equipment.studioId = user.studioId; // (!) user's studioId
  if (!toSave.equipment.id?.trim()) {
    // create
    await equipService.create(toSave);
  } else {
    // update
    await equipService.update(toSave);
  }
  res.status(200).end();
};

export default equipmentSave;
