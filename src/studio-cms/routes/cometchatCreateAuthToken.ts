import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { createCometChatAuthToken, getCometChatUser, createCometChatUser } from "@utils/cometchat";
import { UserService } from "../services/user-service";
import { StudioService } from "../services/studio-service";
import { CometChatUser } from "@public-api/models/cometchat";

const userService = new UserService();
const studioService = new StudioService();

const cometchatCreateAuthToken: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const user = await userService.findByFirebaseUid(uid);
  if (!user) {
    res.status(400).json({ success: false, message: "user not found", data: null });
    return;
  }
  const studioId = user.studioId;
  const studio = await studioService.findById(studioId);
  // 1. Check if CometChat user exists
  let userResult = await getCometChatUser(studioId);
  if (!userResult.success) {
    // If not exists, create user
    userResult = await createCometChatUser({
      uid: studioId,
      email: studio.email,
      name: studio.name,
      tags: ["studio"],
      userType: "studio",
    } as CometChatUser);

    if (!userResult.success) {
      res.status(500).json(userResult);
      return;
    }
  }
  // 2. Create auth token
  const tokenResult = await createCometChatAuthToken(studioId);
  if (!tokenResult.success) {
    res.status(500).json(tokenResult);
    return;
  }
  res.status(200).json(tokenResult);
};

export default cometchatCreateAuthToken; 