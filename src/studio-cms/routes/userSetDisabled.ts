import { toBy } from "@shared/request-parsers";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { UserSetActiveReq } from "../models/user";
import { UserService } from "../services/user-service";

const userService = new UserService();

const userSetDisabled: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active || !user.admin) {
    res.status(401).end(); // unauthorized
    return;
  }

  const disabled = req.params.disabled + "";
  await userService.setActive({
    userId: req.params.userId as string,
    studioId: user.studioId, // (!) important
    active: !(disabled === "true"),
    by: toBy(idToken),
  } as UserSetActiveReq);
  res.status(200).end();
};

export default userSetDisabled;
