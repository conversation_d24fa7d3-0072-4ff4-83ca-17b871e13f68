import { ExerciseRecord } from "@booking-api/models/session";
import { determineSessionStatus } from "@booking-api/models/booking";
import { PageOfData } from "@shared/services";
import { Availability, Equipment, EquipmentSimple, EquipmentType } from "../../models/equipment";
import { Event, EventType, EventCancelBy } from "@studio-cms/models/event";
import { Studio } from "../../models/studio";
import { User } from "../../models/user";
import { SessionFeedback } from "@public-api/models/public";
import { DateTime } from "luxon";

////////////
// Studio //
////////////

export function toStudioJson(studio: Studio) {
  const facebook = studio.socialMedia?.facebook;
  const instagram = studio.socialMedia?.instagram;
  const website = studio.socialMedia?.website;
  return {
    ...studio.id && { id: studio.id },
    name: studio.name,
    descr: studio.descr,
    contactNo: studio.contactNo,
    email: studio.email,
    address: studio.address,
    placeId: studio.placeId,
    geocode: studio.geocode,
    distance: studio.distance ?? null,
    ...studio.instrLess && { instrLess: true },
    usagePolicies: studio.usagePolicies,
    fav: studio.fav ?? false,
    pictures: studio.pictures ?? [],
    socialMedia: {
      ...facebook && { facebook: facebook },
      ...instagram && { instagram: instagram },
      ...website && { website: website },
    },
    avgRating: Number(studio.avgRating ?? 0).toFixed(1),
    totalRatingCount: studio.totalRatingCount ?? 0,
    equipments: studio.equipments ?? [],
  }
}

//////////
// User //
//////////

export function toUserJson(user: User) {
  const active = !!user.active;
  return {
    id: user.id,
    email: user.firebaseEmail,
    name: user.name,
    jobTitle: user.jobTitle,
    contactNo: user.contactNo,
    studioId: user.studioId,
    ...user.admin && { isAdmin: true },
    ...!active && { isDisabled: true },
    ...user.emailVerified && { isEmailVerified: true },
  }
}

export function toPageOfUsersJson(page: PageOfData<User>) {
  return {
    totalItems: page.totalItems,
    totalPages: page.totalPages,
    items: page.items.map(o => toUserJson(o)),
  };
}

///////////////
// Equipment //
///////////////

export function toEquipmentTypeJson(type: EquipmentType) {
  return {
    id: type.id,
    name: type.name,
  }
}

// (!) simple
export function toEquipmentSimpleJson(equip: EquipmentSimple) {
  return {
    id: equip.id,
    type: {
      id: equip.type.id,
      name: equip.type.name,
    },
    code: equip.code,
    ...equip.deleted && { deleted: true },
  }
}

export function toEquipmentJson(equip: Equipment) {
  return {
    id: equip.id,
    type: {
      id: equip.type.id,
      name: equip.type.name,
    },
    code: equip.code,
    ...equip.deleted && { deleted: true },
    // ...
    privacy: equip.privacy,
    startDate: !equip.startDate ? null : equip.startDate, // Already in YYYY-MM-DD format
    endDate: !equip.endDate ? null : equip.endDate, // Already in YYYY-MM-DD format
    availability: toAvailabilityJson(equip.availability),
  }
}

export function toPageOfEquipmentJson(page: PageOfData<Equipment>) {
  return {
    totalItems: page.totalItems,
    totalPages: page.totalPages,
    items: page.items.map(o => toEquipmentJson(o)),
  };
}

export function toAvailabilityJson(avail: Availability[]) {
  // @ts-ignore
  const ret = [];
  for (const o of avail) {
    const slots = o.slots;
    ret.push({
      type: o.type,
      slots: slots.map(slot => ({
        start: slot.start.substring(0, 5),
        end: slot.end.substring(0, 5),
        price: slot.price,
      })),
    });
  }
  return ret;
}

///////////
// Event //
///////////

export function toEventJson(event: Event, {
  includeInternalRemarks = false,
  uid = null,
}) {
  const session = event.session;
  const studio = event.studio;
  const status = determineSessionStatus(event);
  let type = null;
  if(uid) {
    switch (uid) {
      case event.instructorId:
        type = EventType.Session;
        break;
      case event.memberId:
        type = EventType.Booking;
        break;
      default:
        break;
    }
  }
  let reqByName = null;
  if (event.cancelReq) {
    const reqBy = (EventCancelBy as any)[event.cancelReq.reqBy];
    switch(reqBy) {
      case EventCancelBy.Instructor:
        reqByName = `${event.instructor?.fullName}`;
        break; 
      case EventCancelBy.Studio:
        reqByName = `${event.studio.name}`;
        break;
    }
  }

  return {
    id: event.id,
    studioId: event.studioId,
    ...studio && { studio: {
      name: studio.name,
      address: studio.address,
      placeId: studio.placeId,
      lat: studio.lat,
      lng: studio.lng,
    } },
    type: event.type,
    ...type && { bookingType: type},
    ...event.bookingRefNo && { bookingRefNo: event.bookingRefNo },
    ...event.bookingStatus && { bookingStatus: event.bookingStatus },
    ...event.name && { name: event.name },
    ...(includeInternalRemarks && event.internalRemarks) && { internalRemarks: event.internalRemarks },
    ...event.bookingFees && { 
      bookingFeesTotal: Number(event.bookingFees.total).toFixed(2),
      ...event.bookingFees.totalCredits && { bookingFeestotalCredits: Number(event.bookingFees.totalCredits).toFixed(0) }
    },
    startDate: event.startDate, // Already in user's timezone as YYYY-MM-DD string
    ...event.startTime && { startTime: event.startTime.substring(0, 5) },
    endDate: event.endDate, // Already in user's timezone as YYYY-MM-DD string
    ...event.endTime && { endTime: event.endTime.substring(0, 5) },
    ...event.isFullDay && { isFullDay: true },
    ...event.equipmentId && { equipmentId: event.equipmentId },
    ...event.equipment && { equipment: {
      code: event.equipment.code,
      name: event.equipment.name,
      privacy: event.equipment.privacy ?? false,
    } },
    ...event.instructorId && { instructorId: event.instructorId },
    ...event.instructor && { instructor: {
      fullName: event.instructor.fullName,
    } },
    ...event.memberId && { memberId: event.memberId },
    ...event.member && { member: {
      fullName: event.member.fullName,
    } },
    ...event.cancelled && { 
      cancelled: true,
      ...event.cancelledWithRefund && { cancelledWithRefund: true },
      ...event.cancelledById && { cancelledById: event.cancelledById },
      ...event.cancelReason && { cancelReason: event.cancelReason },
    },
    ...event.cancelReq && {
      cancelReq: {
        reqBy: event.cancelReq.reqBy,
        reqByName: reqByName,
        free: event.cancelReq.free,
        reason: event.cancelReq.reason,
        approved: event.cancelReq.approved
      }
    },

    //
    ...event.instrNoShow && {
      instrNoShow: true,
      instrNoShowRemark: event.instrNoShowRemarks
    },
    //
    ...event.session?.completed && { completed: true },
    ...event.session?.hasMemberFeedback && { hasMemberFeedback: true },
    ...event.session?.memberFeedback && { memberFeedback: event.session.memberFeedback },
    ...event.session?.memberNoFeedback && { memberNoFeedback: true },
    //
    ...session && {
      ...session?.hasMemberFeedback && {
        feedback: {
          instructorFeedback: session?.instructorFeedback,
          instructorRating: session?.instructorRating,
          studioFeedback: session?.studioFeedback,
          studioRating: session?.studioRating,
          feedbackAt: session?.feedbackAt,
          isPrivate: session?.isPrivateFeedback,
        }
      },
      notes: {
        sessionState: session.notes.state,
        sessionTarget: session.notes.target,
      },
      ...((session.record_mode === 1 && session.record) || (session.record_mode === 2 && session.record_flexi) ? {
        record: session.record_mode === 2 ? {
          // Flexi mode
          segments: session.record_flexi.segments?.filter(o => o != ""),
          feedback: session.record_flexi.feedback ?? '',
          overallRating: session.record_flexi.overallRating ?? 0,
          record_mode: 2
        } : {
          // Strict mode (original format)
          focus: session.record.focus ?? '',
          exercises: session.record.exercises.map(o => toExerciseRecordJson(o)),
          feedback: session.record.feedback ?? '',
          record_mode: 1
        }
      } : {}),
      ...session.completed && { completed: true },
      ...(session.doorPin) && { doorPin: session.doorPin },
    },
    ...event.bookedByAdmin && { bookedByAdmin: true },
    status: status
  }
}

export function toExerciseRecordJson(exercise: ExerciseRecord) {
  return {
    ...exercise.category && {
      category: {
        id: exercise.category.id,
        name: exercise.category.name,
      },
    },
    ...exercise.exercise && {
      exercise: {
        id: exercise.exercise.id,
        name: exercise.exercise.name,
      },
    },
    ...exercise.modification && {
      modification: {
        id: exercise.modification.id,
        name: exercise.modification.name,
      },
    },
    spring: exercise.spring ?? '',
    reps: exercise.reps ?? 0,
    duration: exercise.duration ?? '00:00',
    stabilityRating: exercise.stabilityRating ?? 0,
    formRating: exercise.formRating ?? 0,
  }
}

export function toScheduleJson(events: Event[]) {
  return {
    numBookings: events
      .filter(o => o.type === 'Booking' && !o.cancelled)
      .length,
    events: events.map(o => toEventJson(o, {
      includeInternalRemarks: true,
    })),
  };
}

////////////
// Review //
////////////

export function toFeedbackJson(feedback: SessionFeedback, {
  userTimezone = 'Asia/Singapore',
}: {
  userTimezone?: string;
} = {}) {
  return {
    rating: feedback.rating,
    feedback: feedback.feedback,
    feedbackAt: DateTime.fromFormat(feedback.feedbackAt, 'yyyy-MM-dd HH:mm:ss', { zone: 'UTC' }).setZone(userTimezone).toFormat('dd MMM yyyy'),
    ...feedback.member && { member: feedback.member}
  }
}

export function toPageOfFeedbacksJson(page: PageOfData<SessionFeedback>, {
  userTimezone = 'Asia/Singapore',
}: {
  userTimezone?: string;
} = {}) {
  return {
    totalItems: page.totalItems,
    totalPages: page.totalPages,
    items: page.items.map(o => toFeedbackJson(o, {
      userTimezone,
    })),
  };
}

