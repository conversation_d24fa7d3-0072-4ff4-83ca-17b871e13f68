import { toBy } from "@shared/request-parsers";
import { auth } from "firebase-admin";
import {
  Availability,
  AvailabilitySaveReq,
  AvailabilitySlot,
  Equipment,
  EquipmentSaveReq,
  EquipmentType
} from "../../models/equipment";
import { Event, EventSaveReq } from "../../models/event";
import { Studio, StudioSaveReq } from "../../models/studio";
import { User, UserSaveReq } from "../../models/user";
import { toAvailabilityJson } from "./response-output";
import DecodedIdToken = auth.DecodedIdToken;

export function toStudioSaveReq(
  body: any, idToken: DecodedIdToken
): StudioSaveReq {
  const studio = {
    id: '', // (!) default empty
    name: body.name,
    descr: body.descr,
    contactNo: body.contactNo,
    email: body.email,
    address: body.address,
    placeId: body.placeId,
    usagePolicies: body.usagePolicies,
    instrLess: body.instrLess ?? false,
    pictures: body.pictures ?? [],
    socialMedia: body.socialMedia ?? {},
  } as Studio;
  return {
    studio: studio,
    by: toBy(idToken),
  } as StudioSaveReq;
}

// (!) Intended for self
export function toSelfSaveReq(
  body: any, idToken: DecodedIdToken
): UserSaveReq {
  const user = {
    id: idToken.uid,          // (!) self
    firebaseUid: idToken.uid, // (!) self
    firebaseEmail: idToken.email,
    studioId: '',             // (!) default empty
    name: body.name,
    jobTitle: body.jobTitle,
    contactNo: body.contactNo,
  } as User;
  return {
    user: user,
    by: toBy(idToken),
  } as UserSaveReq;
}

// (!) Intended for manage users (by admin user)
export function toUserSaveReq(
  body: any, idToken: DecodedIdToken
): UserSaveReq {
  const user = {
    id: body.id,
    firebaseUid: '', // (!) default empty
    firebaseEmail: body.email,
    studioId: '',    // (!) default empty
    name: body.name,
    jobTitle: body.jobTitle,
    contactNo: body.contactNo,
    admin: body.isAdmin ?? false,
    active: !(body.isDisabled ?? false),
  } as User;
  return {
    user: user,
    by: toBy(idToken),
  } as UserSaveReq;
}

export function toEquipmentSaveReq(
  body: any, idToken: DecodedIdToken
): EquipmentSaveReq {
  const equipment = {
    id: body.id,
    type: {
      id: body.type.id,
      name: body.type.name,
    } as EquipmentType,
    code: body.code,
    privacy: body.privacy,
    startDate: body.startDate,
    endDate: body.endDate,
    availability: !body.availability ? [] : toAvailabilityJson(body.availability),
  } as Equipment;
  return {
    equipment: equipment,
    by: toBy(idToken),
  } as EquipmentSaveReq;
}

export function toAvailabilitySaveReq(
  body: any, idToken: DecodedIdToken
): AvailabilitySaveReq {
  const map: any = {};
  for (const avail of body) {
    const type = avail.type;
    const slots = avail.slots;
    for (const o of slots) {
      const slot = {
        start: o['start'],
        end: o['end'],
        price: o['price'],
      } as AvailabilitySlot;

      const val = map[type];
      if (val) {
        val.push(slot);
      } else {
        map[type] = [slot];
      }
    }
  }

  const availability: Availability[] = [];
  Object.keys(map).forEach((key: string) => {
    availability.push({
      type: key,
      slots: map[key] as AvailabilitySlot[],
    } as Availability);
  });

  return {
    availability: availability,
    by: toBy(idToken),
  } as AvailabilitySaveReq;
}

export function toEventSaveReq(
  body: any, idToken: DecodedIdToken
): EventSaveReq {
  const event = {
    id: body.id,
    type: body.type,
    name: body.name,
    internalRemarks: body.internalRemarks,
    startDate: body.startDate,
    startTime: body.startTime,
    endDate: body.endDate,
    endTime: body.endTime,
    isFullDay: body.isFullDay,
    equipmentId: body.equipmentId,
  } as Event;
  return {
    event: event,
    by: toBy(idToken),
  } as EventSaveReq;
}
