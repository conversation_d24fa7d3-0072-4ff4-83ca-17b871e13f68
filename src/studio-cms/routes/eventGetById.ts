import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EventService } from "../services/event-service";
import { UserService } from "../services/user-service";
import { toEventJson } from "./shared/response-output";

const userService = new UserService();
const eventService = new EventService();

const eventGetById: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active) {
    res.status(401).end(); // unauthorized
    return;
  }

  const eventId = req.params.eventId;
  const ret = await eventService.findById(eventId);
  if (!ret || ret.studioId !== user.studioId) {
    res.status(204).end(); // no content
  } else {
    res.status(200).json(toEventJson(ret, {
      includeInternalRemarks: true,
    }));
  }
};

export default eventGetById;
