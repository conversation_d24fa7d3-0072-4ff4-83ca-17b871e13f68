import { ServiceError } from "@shared/services";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { StudioService } from "../services/studio-service";
import { UserService } from "../services/user-service";
import { toStudioSaveReq } from "./shared/request-parsers";

const studioService = new StudioService();
const userService = new UserService();

const myStudioSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const body = req.body;

  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active) {
    res.status(401).end(); // unauthorized
    return;
  }

  const toSave = toStudioSaveReq(body, idToken);
  toSave.studio.id = user.studioId; // (!) user's studioId
  /*
  await studioService.update(toSave);
  res.status(200).end();
  */
  studioService.update(toSave)
    .then(() => res.status(200).end())
    .catch((error) => handleError(error));

  const handleError = (error: any) => {
    if (error instanceof ServiceError) {
      if (error.code === "204") {
        res.status(204).end();
      } else {
        res.status(500).end();
      }
    } else {
      res.status(500).end();
    }
  }
};

export default myStudioSave;
