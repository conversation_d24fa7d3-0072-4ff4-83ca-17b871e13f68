import { Page } from "@shared/services";
import { Request<PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EquipmentService } from "../services/equipment-service";
import { UserService } from "../services/user-service";
import { toPageOfEquipmentJson } from "./shared/response-output";

const userService = new UserService();
const equipService = new EquipmentService();

const equipmentList: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active || !user.admin) {
    res.status(401).end(); // unauthorized
    return;
  }

  const typeId = req.query['typeId'];
  const availableNow = req.query['availableNow'] + "";
  const inclDeleted = req.query['inclDeleted'] + "";
  const searchTerm = req.query['searchTerm'];
  const page = new Page(+req.params.page, +req.params.pageSize);
  const ret = await equipService.findAll({
    studioId: user.studioId, // (!) important
    ...typeId && { typeId: typeId.toString().trim() },
    availableNow: (availableNow === "true"),
    inclDeleted: (inclDeleted === "true"),
    isPrivacy: req.query['isPrivacy'] !== undefined? !!req.query['isPrivacy']: undefined,
    ...searchTerm && { searchTerm: searchTerm.toString().trim() },
  }, page);
  res.status(200).json(toPageOfEquipmentJson(ret));
};

export default equipmentList;
