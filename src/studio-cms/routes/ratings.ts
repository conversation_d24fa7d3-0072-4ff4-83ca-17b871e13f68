import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { ReviewService } from "../services/review-service";
import { UserService } from "../services/user-service";

const reviewService = new ReviewService();
const userService = new UserService();

const ratings: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active || !user.admin) {
    res.status(401).end(); // unauthorized
    return;
  }

  const ret = await reviewService.getRatingByStudioId(user.studioId);
  res.status(200).json(ret);
};

export default ratings;
