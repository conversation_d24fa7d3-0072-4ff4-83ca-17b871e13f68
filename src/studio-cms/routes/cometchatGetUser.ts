import { Request<PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { getCometChatUser } from "@utils/cometchat";

const cometchatGetUser: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const studioId = idToken.studioId;
  if (!studioId) {
    res.status(400).json({ success: false, message: "studioId missing from token", data: null });
    return;
  }
  const result = await getCometChatUser(studioId);
  if (!result.success) {
    res.status(404).json(result);
    return;
  }
  res.status(200).json(result);
};

export default cometchatGetUser; 