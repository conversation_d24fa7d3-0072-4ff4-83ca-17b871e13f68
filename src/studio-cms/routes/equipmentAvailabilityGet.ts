import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EquipmentService } from "../services/equipment-service";
import { UserService } from "../services/user-service";
import { toAvailabilityJson } from "./shared/response-output";

const userService = new UserService();
const equipService = new EquipmentService();

const equipmentAvailabilityGet: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active) {
    res.status(401).end(); // unauthorized
    return;
  }

  const equipmentId = req.params.equipmentId;
  const ret = await equipService.findById(equipmentId);
  if (!ret || ret.studioId !== user.studioId) {
    res.status(204).end(); // no content
  } else {
    res.status(200).json(
      toAvailabilityJson(ret.availability)
    );
  }
};

export default equipmentAvailabilityGet;
