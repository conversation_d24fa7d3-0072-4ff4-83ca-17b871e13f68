import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EquipmentService } from "../services/equipment-service";
import { UserService } from "../services/user-service";

const userService = new UserService();
const equipService = new EquipmentService();

const equipmentCodeExists: RequestHandler = async (req, res) => {
    const idToken = getIdToken(req);

    // preconditions
    if (!idToken.email_verified) {
        res.status(401).end("email not verified");
        return;
    }
    const user = await userService.findByFirebaseEmail(idToken.email!);
    if (!user || !user.active || !user.admin) {
        res.status(401).end(); // unauthorized
        return;
    }

    const studioId = user.studioId; // (!) user's studioId
    const equipmentId = req.query.equipmentId?.toString();
    const code = req.query.code?.toString();
    const ret = await equipService.checkCodeExists(studioId, code, equipmentId);
    res.status(200).json(ret);
};

export default equipmentCodeExists;
