import { Page, Sort } from "@shared/services";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { ReviewService } from "../services/review-service";
import { UserService } from "../services/user-service";
import { toPageOfFeedbacksJson } from "./shared/response-output";
import { add, format, isValid, parse } from "date-fns";

const reviewService = new ReviewService();
const userService = new UserService();

const reviews: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active || !user.admin) {
    res.status(401).end(); // unauthorized
    return;
  }

  const sortQuery = req.query.sort?.toString().trim() ?? 'feedbackAt';
  const sortDirQuery = req.query.sortDir?.toString().trim() ?? 'desc';
  const sort = sortQuery
    ? new Sort(sortQuery, sortDirQuery === 'asc')
    : undefined;

  const page = new Page(+req.params.page, +req.params.pageSize);
  const ret = await reviewService.findAll({
    studioId: user.studioId, // (!) important
  }, sort, page);
  res.status(200).json(toPageOfFeedbacksJson(ret, {
    userTimezone: req.userTimezone,
  }));
};

export default reviews;
