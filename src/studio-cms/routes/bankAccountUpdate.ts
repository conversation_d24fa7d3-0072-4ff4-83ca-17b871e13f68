import { Response } from "express";

import { BankService } from "@backoffice-cms/services/bank-account-service";
import { ApiRequest, ENTITY_TYPES } from "@shared/constants";
import { requireStudioAccess } from "@studio-cms/middlewares/studio-access";

const bankService = new BankService();

export default requireStudioAccess(async (req: ApiRequest, res: Response) => {
  const { auth: { studioId } } = req;
  const result = await bankService.updateBankAccount(req, ENTITY_TYPES.STUDIO, studioId);
  res.status(200).json({
    success: true,
    data: result
  });
});
