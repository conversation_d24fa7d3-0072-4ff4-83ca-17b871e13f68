import { ServiceError } from "@shared/services";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { UserService } from "../services/user-service";
import { toUserSaveReq } from "./shared/request-parsers";

const userService = new UserService();

const userSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const body = req.body;

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active || !user.admin) {
    res.status(401).end(); // unauthorized
    return;
  }

  const toSave = toUserSaveReq(body, idToken);
  toSave.user.studioId = user.studioId; // (!) user's studioId
  if (!toSave.user.id?.trim()) {
    // new user -> create
    toSave.user.active = true; // (!) active on create
    toSave.user.firebaseUid = `@${toSave.user.firebaseEmail}`; // (!) placeholder
    userService.create(toSave)
      .then(() => res.status(200).end())
      .catch((error) => handleCreateError(error));
  } else {
    // existing user -> update
    await userService.update(toSave);
    res.status(200).end();
  }

  const handleCreateError = (error: any) => {
    if (error instanceof ServiceError) {
      if (error.code === "409") {
        res.status(409).end();
      } else {
        res.status(500).end();
      }
    } else {
      res.status(500).end();
    }
  }
};

export default userSave;
