import { ServiceError } from "@shared/services";
import { Request<PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { StudioService } from "../services/studio-service";
import { toSelfSaveReq, toStudioSaveReq } from "./shared/request-parsers";
import { createCometChatUser } from "@utils/cometchat";
import logger from "@utils/logger";

const studioService = new StudioService();

const register: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const body = req.body;

  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }

  try {
    const studioId = await studioService.register(
      toSelfSaveReq(body.mainUser, idToken),
      toStudioSaveReq(body.studio, idToken),
    )
    // CometChat integration (non-blocking)
      try {
        const result = await createCometChatUser({
          uid: studioId,
          email: body.studio.email,
          name: body.studio.name,
          tags: ["studio"],
          userType: "studio",
        });
        if (!result.success) {
          logger.warn(`[CometChat] Studio user creation failed for studioId=${studioId}: ${result.message}`);
        }
      } catch (e) {
        logger.warn(`[CometChat] Studio user creation failed for studioId=${studioId}: ${e}`);
      }
      res.status(200).json({ success: true });
    } catch (e) {
      if (e instanceof ServiceError) {
        if (e.code === "409") {
          res.status(409).json({ success: false, error: e.message });
        } else {
          res.status(500).json({ success: false, error: e.message });
        }
      } else {
        res.status(500).json({ success: false, error: e.message });
      }
    }
};

export default register;
