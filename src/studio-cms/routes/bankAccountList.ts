import { Response } from "express";

import { ApiRequest, ENTITY_TYPES } from "@shared/constants";
import { BankService } from "@backoffice-cms/services/bank-account-service";
import { requireStudioAccess } from "@studio-cms/middlewares/studio-access";
import { toBankAccountViewJson } from "@database/models/bank-account";

const bankService = new BankService();

export default requireStudioAccess(async (req: ApiRequest, res: Response) => {
  const { auth: { studioId } } = req;
  const result = await bankService.getBankAccountList(req, ENTITY_TYPES.STUDIO, studioId);
  const formOptions = await bankService.getListingOptions(req);
  res.status(200).json({
    success: true,
    data: result.items.map(item => toBankAccountViewJson(item)),
    meta: {
      ...result.meta,
      options: formOptions
    }
  });
});
