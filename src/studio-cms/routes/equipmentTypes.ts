import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { EquipmentService } from "../services/equipment-service";
import { UserService } from "../services/user-service";
import { toEquipmentTypeJson } from "./shared/response-output";

const userService = new UserService();
const equipService = new EquipmentService();

const equipmentTypes: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active) {
    res.status(401).end(); // unauthorized
    return;
  }

  const ret = await equipService.findTypes();
  res.status(200).json(
    ret.map(o => toEquipmentTypeJson(o))
  );
};

export default equipmentTypes;
