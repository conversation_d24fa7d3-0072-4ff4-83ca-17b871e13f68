import { Page, Sort } from "@shared/services";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { UserService } from "../services/user-service";
import { toPageOfUsersJson } from "./shared/response-output";

const userService = new UserService();

const userList: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active || !user.admin) {
    res.status(401).end(); // unauthorized
    return;
  }

  const searchTerm = req.query['searchTerm'];
  const sortQuery = req.query.sort?.toString().trim();
  const sortDirQuery = req.query.sortDir?.toString().trim() ?? 'asc';
  const sort = sortQuery
    ? new Sort(sortQuery, sortDirQuery === 'asc')
    : undefined;

  const page = new Page(+req.params.page, +req.params.pageSize);
  const ret = await userService.findAll({
    studioId: user.studioId, // (!) important
    isActive: req.query['isActive'] !== undefined? req.query['isActive'] === "true": undefined,
    isAdmin: req.query['isAdmin'] !== undefined? req.query['isAdmin'] === "true": undefined,
    ...searchTerm && { searchTerm: searchTerm.toString().trim() },
  }, sort, page);
  res.status(200).json(toPageOfUsersJson(ret));
};

export default userList;
