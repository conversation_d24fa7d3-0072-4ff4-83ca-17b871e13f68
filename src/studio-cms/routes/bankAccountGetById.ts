import { Response } from "express";

import { BankService } from "@backoffice-cms/services/bank-account-service";
import { requireStudioAccess } from "@studio-cms/middlewares/studio-access";
import { ApiRequest } from "@shared/constants";
import { toBankAccountViewJson } from "@database/models/bank-account";

const bankService = new BankService();

export default requireStudioAccess(async (req: ApiRequest, res: Response) => {
  const result = await bankService.getBankAccountById(req);
  res.status(200).json({
    success: true,
    data: toBankAccountViewJson(result)
  });
});
