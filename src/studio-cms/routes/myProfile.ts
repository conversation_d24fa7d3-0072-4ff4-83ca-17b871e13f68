import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { UserService } from "../services/user-service";
import { toUser<PERSON>son } from "./shared/response-output";

const userService = new UserService();

const myProfile: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const email = idToken.email!;

  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }

  const user = await userService.findByFirebaseEmail(email);
  if (!user || !user.active) {
    res.status(204).end(); // no content
  } else {
    res.status(200).json(toUserJson(user));
  }
};

export default myProfile;
