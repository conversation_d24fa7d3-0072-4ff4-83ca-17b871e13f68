import logger from "@utils/logger";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { FirebaseAuth } from "../../init-firebase";
import { getIdToken } from "../../init-openapi";
import { UserService } from "../services/user-service";
import { toUserJson } from "./shared/response-output";

const userService = new UserService();

const userGetById: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);

  // preconditions
  if (!idToken.email_verified) {
    res.status(401).end("email not verified");
    return;
  }
  const user = await userService.findByFirebaseEmail(idToken.email!);
  if (!user || !user.active || !user.admin) {
    res.status(401).end(); // unauthorized
    return;
  }

  const userId = req.params.userId;
  const ret = await userService.findById(userId);
  if (!ret || ret.studioId !== user.studioId) {
    res.status(204).end(); // no content
  } else {
    ret.emailVerified = await isEmailVerified(ret.firebaseEmail);
    res.status(200).json(toUserJson(ret));
  }
};

async function isEmailVerified(firebaseEmail: string)  {
  let verified = false;
  try {
    const user = await FirebaseAuth.getUserByEmail(firebaseEmail);
    verified = user.emailVerified;
  } catch (e) {
    logger.info(`Error @ FirebaseAuth.getUserByEmail(${firebaseEmail}), ${e}`);
  }
  return verified;
}

export default userGetById;
