import { SendTemplatedEmailRequest } from "@aws-sdk/client-ses";
import { Page, PageOfData, ServiceError, Sort } from "@shared/services";
import { sendTemplatedEmail } from "@utils/aws-ses";
import logger from "@utils/logger";
import pool from "../../init-pool";
import { FirebaseUidSetReq, User, UserFindCriteria, UserSaveReq, UserSetActiveReq } from "../models/user";
import { UserRepo } from "../repo/user-repo";

export class UserService {

  private userRepo = new UserRepo();

  // Equivalent to findByFirebaseUid()
  async findById(id: string): Promise<User|undefined> {
    return this.userRepo.findById(pool, id);
  }

  // Equivalent to findById()
  async findByFirebaseUid(uid: string): Promise<User|undefined> {
    return this.findById(uid);
  }

  async findByFirebaseEmail(email: string): Promise<User|undefined> {
    return this.userRepo.findByFirebaseEmail(pool, email);
  }

  async setFirebaseUid(req: FirebaseUidSetReq): Promise<number> {
    return this.userRepo.setFirebaseUid(pool, req);
  }

  async findAll(
    criteria: UserFindCriteria, sort?: Sort, page?: Page
  ): Promise<PageOfData<User>> {
    return this.userRepo.findAll(pool, criteria, sort, page);
  }

  // (!) Intended for manage users (by admin user)
  async create(req: UserSaveReq): Promise<string> {
    let ret: string;
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // check if email exists
      const user = await this.userRepo.findByFirebaseEmail(conn, req.user.firebaseEmail);
      if (user) {
        logger.info(`email ${req.user.firebaseEmail} already exists`);
        throw new ServiceError("409", "email already exists");
      }
      // create user
      ret = await this.userRepo.create(conn, req);

      await conn.commit();
      logger.info(`create() success, userId=${ret}`);
    } catch (e) {
      logger.error(`create error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }

    // (!) Network I/O. DO NOT do this within SQL transaction.
    try {
      await this.sendStudioUserInvite(req);
    } catch (e) {
      logger.error(`create() Error @ sendStudioUserInvite() error=${e}`);
    }
    return ret;
  }

  // (!) Intended for manage users (by admin user)
  async update(req: UserSaveReq): Promise<number> {
    return this.userRepo.update(pool, req);
  }

  // (!) Intended for self
  async updateSelf(req: UserSaveReq): Promise<number> {
    return this.userRepo.updateSelf(pool, req);
  }

  // (!) Intended for invited users to update profile and Firebase UID
  async updateSelfWithFirebaseUid(req: UserSaveReq): Promise<number> {
    return this.userRepo.updateSelfWithFirebaseUid(pool, req);
  }

  async setActive(req: UserSetActiveReq): Promise<number> {
    return this.userRepo.setActive(pool, req);
  }

  async sendStudioUserInvite(req: UserSaveReq) {
    // Derive CC list, if any
    const cc = process.env.STUDIO_USER_INVITE_EMAIL_CC!.trim();
    const ccList = !cc ? [] : cc.split(",").map(o => o.trim());
    logger.debug(`ccList=${JSON.stringify(ccList)}`);

    // Prepare request
    const sendTemplatedEmailReq = {
      Source: process.env.STUDIO_USER_INVITE_EMAIL_FROM!,
      Destination: {
        ToAddresses: [req.user.firebaseEmail],
        ...cc && { CcAddresses: ccList },
      },
      Template: process.env.STUDIO_USER_INVITE_EMAIL_TEMPLATE_NAME!,
      TemplateData: JSON.stringify({
        "name": req.user.name,
        "signupURL": process.env.STUDIO_USER_INVITE_EMAIL_SIGNUP_URL!,
      }),
    } as SendTemplatedEmailRequest;

    // Send it, if live mode
    const liveMode = process.env.STUDIO_USER_INVITE_EMAIL_LIVE_MODE!.trim() === "true";
    if (liveMode) {
      await sendTemplatedEmail(sendTemplatedEmailReq);
    } else {
      logger.info("sendStudioUserInvite(). NOT LIVE MODE -> not sending email");
    }
  }
}
