import { Picture, PictureSaveReq } from "@shared/models";
import { ServiceError } from "@shared/services";
import { geocode } from "@utils/google-maps";
import logger from "@utils/logger";
import pool from "../../init-pool";
import { MainUserUpdateReq, Studio, StudioSaveReq } from "../models/studio";
import { UserSaveReq } from "../models/user";
import { StudioRepo } from "../repo/studio-repo";
import { UserRepo } from "../repo/user-repo";
import { StudioConfigSaveReq, StudioConfig } from "@studio-cms/models/studio-config";

export class StudioService {

  private studioRepo = new StudioRepo();
  private userRepo = new UserRepo();

  async findById(id: string): Promise<Studio|undefined> {
    return this.studioRepo.findById(pool, id);
  }

  private async setStudioGeocode(studio: Studio) {
    if (!studio.placeId.trim()) {
      return; // nothing else to do.
    }
    studio.geocode = await geocode(studio.placeId);
  }

  async register(userSave: UserSaveReq, studioSave: StudioSaveReq): Promise<string> {
    // check if email exists
    const user = await this.userRepo.findByFirebaseEmail(pool, userSave.user.firebaseEmail);
    if (user) {
      logger.info(`email ${userSave.user.firebaseEmail} already exists`);
      throw new ServiceError("409", "email already exists");
    }

    // (!) Network I/O. DO NOT do this within SQL transaction.
    await this.setStudioGeocode(studioSave.studio);

    let conn = null;
    let studioId: string;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // create studio
      studioId = await this.studioRepo.create(conn, studioSave);
      userSave.user.studioId = studioId;
      userSave.user.admin = true;   // (!) main user is always admin
      userSave.user.active = false; // (!) active = false by default
      userSave.user.mainUser = true; // (!) main user of the studio

      // create main user
      const userId = await this.userRepo.create(conn, userSave);

      // finally, update main user id
      await this.studioRepo.updateMainUserId(conn, {
        studioId: studioId,
        mainUserId: userId,
        by: studioSave.by,
      } as MainUserUpdateReq);

      await conn.commit();
      logger.info(`register() success, studioId=${studioId}, mainUserId=${userId}`);
      return studioId;
    } catch (e) {
      logger.error(`register error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async update(req: StudioSaveReq): Promise<number> {
    const studioId = req.studio.id;
    const found = await this.studioRepo.findById(pool, studioId);
    if (!found) {
      logger.info(`studio (id=${studioId}) not found!`);
      throw new ServiceError("204", "studio not found");
    }

    if (found.placeId !== req.studio.placeId) {
      // (!) Network I/O. DO NOT do this within SQL transaction.
      await this.setStudioGeocode(req.studio);
    } else {
      req.studio.geocode = found.geocode;
    }

    return this.studioRepo.update(pool, req);
  }

  async insertPicture(req: PictureSaveReq): Promise<string> {
    return this.studioRepo.insertPicture(pool, req);
  }

  async findPictureById(id: string): Promise<Picture|undefined> {
    return this.studioRepo.findPictureById(pool, id);
  }

  /*
  async deletePicture(req: PictureDeleteReq): Promise<number> {
    return this.studioRepo.deletePicture(pool, req);
  }
  */

  /**
   * Get all config for a studio as an array of StudioConfig
   */
  async getStudioConfig(studioId: string): Promise<StudioConfig[]> {
    return this.studioRepo.getStudioConfig(pool, studioId);
  }

  /**
   * Set a config value for a studio
   */
  async setStudioConfig(req: StudioConfigSaveReq): Promise<number> {
    return this.studioRepo.setStudioConfig(pool, req);
  }
}
