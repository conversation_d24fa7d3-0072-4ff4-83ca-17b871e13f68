import { Page, PageOfData } from "@shared/services";
import logger from "@utils/logger";
import pool from "../../init-pool";
import {
  AvailabilitySaveReq,
  Equipment,
  EquipmentFindCriteria,
  EquipmentSaveReq,
  EquipmentSetDeletedReq,
  EquipmentSimple,
  EquipmentType
} from "../models/equipment";
import { EquipmentRepo } from "../repo/equipment-repo";

export class EquipmentService {

  private equipRepo = new EquipmentRepo();

  async findTypes(): Promise<EquipmentType[]> {
    return this.equipRepo.findTypes(pool);
  }

  async findById(id: string): Promise<Equipment|undefined> {
    return this.equipRepo.findById(pool, id);
  }

  // (!) simple
  async findAllSimple(studioId: string, inclDeleted: boolean): Promise<EquipmentSimple[]> {
    return this.equipRepo.findAllSimple(pool, studioId, inclDeleted);
  }

  async findAll(
    criteria: EquipmentFindCriteria, page?: Page
  ): Promise<PageOfData<Equipment>> {
    return this.equipRepo.findAll(pool, criteria, page);
  }

  async create(req: EquipmentSaveReq): Promise<string> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();
      const equipmentId = await this.equipRepo.create(conn, req)
      const availSaveReq = {
        equipmentId: equipmentId,
        availability: req.equipment.availability,
        by: req.by,
      } as AvailabilitySaveReq;
      await this.equipRepo.deleteAvailability(conn, availSaveReq);
      await this.equipRepo.insertAvailability(conn, availSaveReq);
      await conn.commit();
      logger.info(`createEquipment() success, equipmentId=${equipmentId}`);
      return equipmentId;
    } catch (e) {
      logger.error(`createEquipment error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async update(req: EquipmentSaveReq): Promise<number> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();
      const numEquipmentUpdated = await this.equipRepo.update(conn, req)
      const equipmentId = req.equipment.id;
      const availSaveReq = {
        equipmentId: equipmentId,
        availability: req.equipment.availability,
        by: req.by,
      } as AvailabilitySaveReq;
      await this.equipRepo.deleteAvailability(conn, availSaveReq);
      await this.equipRepo.insertAvailability(conn, availSaveReq);
      await conn.commit();
      logger.info(`updateEquipment() success, equipmentId=${equipmentId}`);
      return numEquipmentUpdated;
    } catch (e) {
      logger.error(`updateEquipment error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async setDeleted(req: EquipmentSetDeletedReq): Promise<number> {
    return this.equipRepo.setDeleted(pool, req);
  }

  async saveAvailability(req: AvailabilitySaveReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();
      await this.equipRepo.deleteAvailability(conn, req);
      await this.equipRepo.insertAvailability(conn, req);
      await conn.commit();
      logger.info(`saveAvailability() success, equipmentId=${req.equipmentId}`);
    } catch (e) {
      logger.error(`saveAvailability error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async checkCodeExists(studioId: string, code: string, equipmentId?: string): Promise<boolean> {
    return this.equipRepo.checkCodeExists(pool, studioId, code, equipmentId);
  }
}
