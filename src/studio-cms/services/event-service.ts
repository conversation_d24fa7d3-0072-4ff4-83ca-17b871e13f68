import logger from "@utils/logger";
import pool from "../../init-pool";
import { BookingCancelReq, BookingType } from "@booking-api/models/booking";
import { Event, EventCancelBy, EventCancelReq, EventFindCriteria, EventNoShowReq, EventSaveReq } from "../models/event";
import { EventRepo } from "../repo/event-repo";
import { BookingRepo } from "@booking-api/repo/booking-repo";
import { DateTime } from "luxon";

export class EventService {

  private eventRepo = new EventRepo();
  private bookingRepo = new BookingRepo();

  async findById(id: string): Promise<Event|undefined> {
    return this.eventRepo.findById(pool, id);
  }

  async findAll(criteria: EventFindCriteria): Promise<Event[]> {
    const events = await this.eventRepo.findAll(pool, criteria);
    const cancelReqs = await this.bookingRepo.findCancelReqsByBookingIds(pool, events.map(event => event.id), false, [EventCancelBy[EventCancelBy.Studio], EventCancelBy[EventCancelBy.Instructor]]);
    const cancelReqMap = new Map(cancelReqs.map(req => [req.eventId, req]));
    events.forEach(event => {
      const cancelReq = cancelReqMap.get(event.id);
      if(cancelReq) {
        event.cancelReq = cancelReq;
      }
    });
    return events;
  }

  async create(req: EventSaveReq): Promise<string> {
    return this.eventRepo.create(pool, req, undefined); // fees = undefined
  }

  async update(req: EventSaveReq): Promise<number> {
    return this.eventRepo.update(pool, req);
  }

  async cancel(req: EventCancelReq): Promise<void> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // check if email exists
      const event = await this.eventRepo.findById(conn, req.eventId!);
      if (!event) {
        throw "Event not found!"; // fail fast
      }
      if (event.studioId !== req.studioId!) {
        throw "Event does not belong to studio!"; // fail fast
      }

      if(event.type == BookingType[BookingType.Booking]) {
        // check if booking is cancelled
        if(event.cancelled) {
          throw "Booking is already cancelled!"; // fail fast
        }
        const cancelReq = await this.bookingRepo.findCancelReq(conn, {
          eventId: req.eventId,
        });
        if( cancelReq ) {
          throw "Cancel booking request already exists!";
        }

        const timezone = event.timezone || 'Asia/Singapore';
        const now = DateTime.now().setZone(timezone);

        const startAt = DateTime.fromFormat(`${event.startDate} ${event.startTime}`, 'yyyy-MM-dd HH:mm:ss', { zone: timezone });
        logger.debug(`eventId=${event.id}, userId=${event.memberId}, startAtStr=${startAt}`);
        if (now > startAt) {
          throw "Cannot cancel booking after it has started!";
        }

        await this.bookingRepo.createCancelReq(conn, {
          reqBy: req.cancelBy,
          userId: req.by.uid,
          eventId: req.eventId,
          free: true,
          reason: req.cancelReason,
          approved: false, // (!) important: pending from backoffice approval
          by: req.by,
        } as BookingCancelReq);
      } else {
        await this.eventRepo.cancel(conn, req);
      }

      await conn.commit();
      return;

    } catch (e) {
      logger.error(`makeBookingByCredit error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async noShow(req: EventNoShowReq): Promise<number> {
    return this.eventRepo.noShow(pool, req);
  }
}
