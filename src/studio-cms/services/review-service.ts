import { Page, PageOfData, ServiceError, Sort } from "@shared/services";
import logger from "@utils/logger";
import pool from "../../init-pool";
import { SessionFeedbackFindCriteria, SessionFeedback, GroupRating, Feedback } from "@public-api/models/public";
import { EventRepo } from "../repo/event-repo";
import { PublicRepo } from "@public-api/repo/public-repo";

export class ReviewService {

  private eventRepo = new EventRepo();
  private publicRepo = new PublicRepo();

  async findAll(
    criteria: SessionFeedbackFindCriteria, sort?: Sort, page?: Page
  ): Promise<PageOfData<SessionFeedback>> {
    return this.eventRepo.findReviews(pool, criteria, sort, page);
  }

  async getRatingByStudioId(
    id: string,
  ): Promise<Feedback> {

    const studioRatings = await this.publicRepo.getStudioRatingByIds(pool, [id]);
    const ratingGroup = await this.publicRepo.getStudioRatingGroupingById(pool, id);
    return {
      avgRating: studioRatings[0]?.rating ?? 0,
      totalRating: studioRatings[0]?.total ?? 0,
      groupsRating: ratingGroup ?? [],
    } as Feedback;
  }

}
