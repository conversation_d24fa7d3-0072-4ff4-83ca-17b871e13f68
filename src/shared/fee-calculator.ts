/**
 * Rounds up a number to the nearest integer
 * @param value The number to round up
 * @returns The rounded up integer
 */
export function roundUp(value: number): number {
  return Math.ceil(value);
}

/**
 * Converts a fee amount to credits using the exchange rate
 * @param fee The fee amount to convert
 * @param exchangeRate The exchange rate to use
 * @returns The rounded up credit amount
 */
export function convertFeeToCredits(fee: number, exchangeRate: number): number {
  const creditAmount = fee * exchangeRate;
  return roundUp(creditAmount);
}

/**
 * Calculates a single fee and converts it to credits
 * @param fee The fee amount to convert
 * @param exchangeRate The exchange rate to use
 * @returns Object containing the fee and its credit amount
 */
export function calculateFee(fee: number, exchangeRate: number): {
  fee: string;
  credits: string;
} {
  const credits = convertFeeToCredits(fee, exchangeRate);
  return {
    fee: fee.toFixed(2),
    credits: credits.toFixed(0)
  };
}

/**
 * Converts a credit amount back to a fee using the exchange rate.
 * @param credits The credit amount to convert.
 * @param exchangeRate The exchange rate to use.
 * @returns The fee amount.
 */
export function convertCreditsToFee(credits: number, exchangeRate: number): number {
  if (exchangeRate === 0) {
    return 0;
  }
  return roundUp(credits / exchangeRate);
}