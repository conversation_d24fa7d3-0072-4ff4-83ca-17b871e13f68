import { ParamsDictionary } from "express-serve-static-core";

// Represents a page
export class Page {
  constructor(
    public page: number, // (!) 1-based
    public size: number,
  ) {
    if (!Number.isInteger(page)) throw `page must be an integer, got ${page} instead`;
    if (!Number.isInteger(size)) throw `size must be an integer, got ${size} instead`;

    if (page < 1) throw `page must be >= 1, got ${page} instead`;
    if (size < 0) throw `page must be >= 0, got ${size} instead`;
  }

  toZeroBasedOffset(): number {
    return (this.page - 1) * this.size;
  }
}

// Represents a page of data
export interface PageOfData<T> {
  totalItems: number,
  totalPages: number,
  items: T[],
}

export interface PaginationData<T> {
  items: T[],
  meta: {
    totalItems: number,
    totalPages: number,
  }
}

// Represents an error from the service layer
export class ServiceError extends Error {
  constructor(public code: string, message: string) {
    super(message);
    this.name = "ServiceError";
    this.code = code;
  }
}

// Represents a sort
export class Sort {
  constructor(
    public sort: string,
    public sortAsc: boolean,
  ) { }
}

export interface PaginationParams {
  page: string;
  pageSize: string;
  sort: string;
  sortDir: string;
}

export function getPaginationQuery(params: ParamsDictionary) {
  const { page, pageSize, sort, sortDir } = params;
  return {
    pageQuery: new Page(+page, +pageSize),
    sortQuery: getSortQuery(sort.toString(), sortDir.toString())
  };
}

function getSortQuery(sort: string, sortDir: string = 'desc') {
  return sort
    ? new Sort(sort.toString(), sortDir === 'asc')
    : undefined;
}
