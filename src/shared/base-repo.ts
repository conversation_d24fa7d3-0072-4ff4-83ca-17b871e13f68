import { Sequelize, ModelStatic, Model, WhereOptions, FindOptions } from 'sequelize';
import { getSequelize } from '@database/models';
import { Page, PageOfData, Sort } from '@shared/services';
import { DbTransactionOptions } from './constants';

export abstract class BaseRepository {
  protected getSequelize(): Sequelize {
    const sequelize = getSequelize();
    if (!sequelize) {
      throw new Error('Sequelize instance not initialized');
    }
    return sequelize;
  }

  protected model_(modelName: string): ModelStatic<Model> {
    const sequelize = this.getSequelize();

    const model = sequelize.models[modelName];
    if (!model) {
      throw new Error(`${modelName} model not found.`);
    }

    return model as ModelStatic<Model>;
  }

  protected async findById<T = any>(modelName: string, id: string | number, options?: FindOptions): Promise<T | null> {
    const model = this.model_(modelName);
    return await model.findByPk(id, options) as T | null;
  }

  protected async findOne<T = any>(modelName: string, where: WhereOptions, options?: FindOptions): Promise<T | null> {
    const model = this.model_(modelName);
    return await model.findOne({ where, ...options }) as T | null;
  }

  protected async findAll<T = any>(modelName: string, options?: FindOptions): Promise<T[]> {
    const model = this.model_(modelName);
    return await model.findAll(options) as T[];
  }

  protected async findAndCountAll<T = any>(
    modelName: string,
    page: Page,
    where?: WhereOptions,
    order?: Sort,
    options?: Omit<FindOptions, 'where' | 'limit' | 'offset' | 'order'>
  ): Promise<PageOfData<T>> {
    const model = this.model_(modelName);

    const findOptions: FindOptions = {
      where,
      limit: page.size,
      offset: page.toZeroBasedOffset(),
      ...options
    };

    if (order) {
      const { sort, sortAsc } = order;
      findOptions.order = [[sort, sortAsc ? 'ASC' : 'DESC']];
    }

    const { count, rows } = await model.findAndCountAll(findOptions);

    const totalPages = Math.ceil(count / page.size);

    return {
      totalItems: count,
      totalPages,
      items: rows as unknown as T[],
    };
  }

  protected async create<T = any>(modelName: string, data: any, options?: DbTransactionOptions): Promise<T> {
    const model = this.model_(modelName);
    return await model.create(data, options) as T;
  }

  protected async update<T = any>(
    model: T,
    data: any,
    options?: DbTransactionOptions
  ): Promise<T> {
    const updateInstance = model as unknown as Model;
    return await updateInstance.update(data, options) as T;
  }

  protected async delete<T = any>(modelName: string, where: WhereOptions, options?: DbTransactionOptions): Promise<number> {
    const model = this.model_(modelName);
    return await model.destroy({ where, ...options });
  }
}
