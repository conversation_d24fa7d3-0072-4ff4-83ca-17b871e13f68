import { Request } from "express";
import { DecodedIdToken } from "firebase-admin/lib/auth/token-verifier";
import { Transaction } from "sequelize";

export interface DbTransactionOptions {
  authId?: string;
  transaction?: Transaction;
}

export const ENTITY_TYPE_VALUES = [
  'platform',
  'studio',
  'instructor',
  'member',
] as const;

export const ENTITY_TYPES = {
  PLATFORM: ENTITY_TYPE_VALUES[0],
  STUDIO: ENTITY_TYPE_VALUES[1],
  INSTRUCTOR: ENTITY_TYPE_VALUES[2],
  MEMBER: ENTITY_TYPE_VALUES[3],
} as const;

export type EntityTypeValue = typeof ENTITY_TYPE_VALUES[number];

export const STATUSES = {
  ACTIVE: '1',
  INACTIVE: '0',
  PENDING: '2',
  COMPLETED: '3',
  REJECTED: '4',
  FAILED: '5',
} as const;

export type StatusValue = typeof STATUSES[keyof typeof STATUSES];

export const TRANSACTION_TYPES = {
  TOPUP: 'topup',
  PAYOUT: 'payout',
  WITHDRAWAL: 'withdrawal',
  REFUND: 'refund',
  ADJUST_IN: 'adjust_in',
  ADJUST_OUT: 'adjust_out',
} as const;

export type TransactionTypeValue = typeof TRANSACTION_TYPES[keyof typeof TRANSACTION_TYPES];

export type ApiRequest = Request & { idToken?: DecodedIdToken, auth?: { countryCode?: string, studioId?: string, boId?: string } }

export interface Option {
  value: string;
  label: string;
}
