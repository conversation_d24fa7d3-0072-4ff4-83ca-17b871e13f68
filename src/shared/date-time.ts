import { format } from "date-fns";
import { DateTime } from 'luxon';

export function formatDateWithTz(date: Date | string | undefined, format: string = 'yyyy-MM-dd HH:mm:ss', timezone = 'Asia/Singapore'): string | undefined {
  if (!date) return undefined;
  return DateTime.fromJSDate(new Date(date), { zone: timezone }).toFormat(format);
}


export function toMySQLDateTime(date: Date = new Date()): string {
  return format(date, "yyyy-MM-dd HH:mm:ss");
}

export function toMySQLDate(date: Date = new Date()): string {
  return format(date, "yyyy-MM-dd");
}

export function toIsoDateTime(date: Date = new Date()): string {
  return format(date, "yyyy-MM-dd'T'HH:mm:ss");
}

export function toIsoDateTimeWithOffset(date: Date = new Date()): string {
  const offset = date.getTimezoneOffset();
  const sign = offset <= 0 ? '+' : '-';
  const hours = Math.abs(Math.floor(offset / 60));
  const minutes = Math.abs(offset % 60);
  const offsetStr = `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  return `${format(date, "yyyy-MM-dd'T'HH:mm:ss")}${offsetStr}`;
}

export function toMySQLDateTimeWithOffset(date: Date = new Date()): string {
  const offset = date.getTimezoneOffset();
  const sign = offset <= 0 ? '+' : '-';
  const hours = Math.abs(Math.floor(offset / 60));
  const minutes = Math.abs(offset % 60);
  const offsetStr = `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  return `${format(date, "yyyy-MM-dd HH:mm:ss")}${offsetStr}`;
}
