import { DateTime } from 'luxon';

/**
 * Format date in user's timezone for display
 */
export function formatDateInTimezone(
  date: Date | string | DateTime,
  timezone: string = 'Asia/Singapore',
  format: string = 'yyyy-MM-dd HH:mm:ss'
): string {
  let dateTime: DateTime;

  if (date instanceof DateTime) {
    dateTime = date.setZone(timezone);
  } else if (typeof date === 'string') {
    dateTime = DateTime.fromISO(date).setZone(timezone);
  } else {
    dateTime = DateTime.fromJSDate(date).setZone(timezone);
  }

  return dateTime.toFormat(format);
}

/**
 * Format date for external APIs (like HitPay)
 */
export function formatDateForAPI(
  date: Date | string | DateTime,
  timezone: string = 'Asia/Singapore'
): string {
  return formatDateInTimezone(date, timezone, 'yyyy-MM-dd HH:mm:ss');
}

/**
 * Format date with timezone offset for debugging
 */
export function formatDateWithOffset(
  date: Date | string | DateTime,
  timezone: string = 'Asia/Singapore'
): string {
  return formatDateInTimezone(date, timezone, 'yyyy-MM-dd HH:mm:ss ZZZZ');
}

/**
 * Get current time in specific timezone
 */
export function getCurrentTimeInTimezone(
  timezone: string = 'Asia/Singapore',
  format: string = 'yyyy-MM-dd HH:mm:ss'
): string {
  return DateTime.now().setZone(timezone).toFormat(format);
}

/**
 * Convert date between timezones
 */
export function convertTimezone(
  date: Date | string | DateTime,
  fromTimezone: string,
  toTimezone: string,
  format: string = 'yyyy-MM-dd HH:mm:ss'
): string {
  let dateTime: DateTime;

  if (date instanceof DateTime) {
    dateTime = date.setZone(fromTimezone);
  } else if (typeof date === 'string') {
    dateTime = DateTime.fromISO(date).setZone(fromTimezone);
  } else {
    dateTime = DateTime.fromJSDate(date).setZone(fromTimezone);
  }

  return dateTime.setZone(toTimezone).toFormat(format);
}

/**
 * Format date for database storage (UTC)
 */
export function formatDateForDatabase(
  date: Date | string | DateTime
): string {
  let dateTime: DateTime;

  if (date instanceof DateTime) {
    dateTime = date.toUTC();
  } else if (typeof date === 'string') {
    dateTime = DateTime.fromISO(date).toUTC();
  } else {
    dateTime = DateTime.fromJSDate(date).toUTC();
  }

  return dateTime.toFormat('yyyy-MM-dd HH:mm:ss');
}

/**
 * Parse date string in specific timezone
 */
export function parseDateInTimezone(
  dateString: string,
  timezone: string = 'Asia/Singapore',
  format: string = 'yyyy-MM-dd HH:mm'
): DateTime {
  return DateTime.fromFormat(dateString, format, { zone: timezone });
}

/**
 * Converts a MySQL Date object to a date string (YYYY-MM-DD format)
 * The database already stores dates in user's timezone, so we just extract the date part
 */
export function convertMySQLDateToTimezoneString(
  mysqlDate: Date | string | null | undefined,
): string | null {
  if (!mysqlDate) return null;

  // If it's already a string, return as-is
  if (typeof mysqlDate === 'string') {
    return mysqlDate;
  }

  // If it's a Date object (from MySQL), just extract the date part
  if (mysqlDate instanceof Date) {
    return mysqlDate.toISOString().split('T')[0]; // Returns YYYY-MM-DD
  }

  return null;
}

/**
 * Converts a MySQL Time object to a time string (HH:mm:ss format)
 * The database already stores times in user's timezone, so we just extract the time part
 */
export function convertMySQLTimeToTimezoneString(
  mysqlTime: Date | string | null | undefined
): string | null {
  if (!mysqlTime) return null;

  // If it's already a string, return as-is
  if (typeof mysqlTime === 'string') {
    return mysqlTime;
  }

  // If it's a Date object (from MySQL), just extract the time part
  if (mysqlTime instanceof Date) {
    return mysqlTime.toTimeString().split(' ')[0]; // Returns HH:mm:ss
  }

  return null;
}