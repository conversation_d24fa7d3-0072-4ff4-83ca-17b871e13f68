import { auth } from "firebase-admin";
import { By } from "./models";
import DecodedIdToken = auth.DecodedIdToken;

export function toBy(idToken: DecodedIdToken): By {
  return {
    uid: idToken.uid,
    username: idToken.email,
  } as By;
}

export function toBoolean(value: any): boolean | undefined {
  if (value === 'true' || value === true) return true;
  if (value === 'false' || value === false) return false;
  return undefined;
}
