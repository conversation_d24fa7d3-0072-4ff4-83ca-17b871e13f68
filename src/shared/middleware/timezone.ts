import { Request, Response, NextFunction } from "express";
import { MemberService } from "@member-api/services/member-service";
import { PublicService } from "@public-api/services/public-service";
import { getIdToken } from "../../init-openapi";
import { DateTime } from "luxon";

const memberService = new MemberService();
const publicService = new PublicService();

// Extend Express Request interface to include timezone
declare global {
  namespace Express {
    interface Request {
      userCountryCode?: string;
      userTimezone?: string;
      userCurrency?: string;
    }
  }
}

export const setUserTimezone = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Get user ID from the request (assuming Firebase auth)
    const idToken = getIdToken(req);
    if (!idToken?.uid) {
      // No authenticated user, use default timezone
      req.userCountryCode = 'SG';
      req.userTimezone = 'Asia/Singapore';
      req.userCurrency = 'SGD';
      return next();
    }

    if(req.url.startsWith("/cms/studio/")) {
      req.userCountryCode = 'SG'; // TODO: get timezone from studio
      req.userTimezone = 'Asia/Singapore';
      req.userCurrency = 'SGD';
    } else if(req.url.startsWith("/cms/backoffice/")) {
      req.userCountryCode = 'SG';
      req.userTimezone = 'Asia/Singapore';
      req.userCurrency = 'SGD';
    } else {
      // Fetch user's timezone from database
      const member = await memberService.findByFirebaseUid(idToken.uid);
      const country = await publicService.findCountryByCode(member?.countryCode ?? 'SG');
      req.userCountryCode = member?.countryCode ?? 'SG';
      req.userTimezone = member?.timezone ?? 'Asia/Singapore';
      req.userCurrency = country?.currency ?? 'SGD';
    }


    next();
  } catch (error) {
    // If there's an error fetching user timezone (including auth errors), use default
    req.userCountryCode = 'SG';
    req.userTimezone = 'Asia/Singapore';
    req.userCurrency = 'SGD';
    next();
  }
};

// Helper function to get current time in user's timezone
export const getCurrentTimeInUserTimezone = (req: Request) => {
  const timezone = req.userTimezone ?? 'Asia/Singapore';
  return DateTime.now().setZone(timezone);
};