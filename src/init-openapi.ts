import logger from "@utils/logger";
import express, { ErrorRequestHandler, Express, Request } from "express";
import * as OpenApiValidator from "express-openapi-validator";
import { RouteMetadata } from "express-openapi-validator/dist/framework/openapi.spec.loader";
import { OpenAPIV3 } from "express-openapi-validator/dist/framework/types";
import { auth } from "firebase-admin";
import * as fs from "node:fs";
import path from "path";
import process from "process";
import { FirebaseAuth, FirebaseAuthStudios, FirebaseAuthBackOffice } from "./init-firebase";
import multer = require("multer");
import DecodedIdToken = auth.DecodedIdToken;
import { setUserTimezone } from "./shared/middleware/timezone";

function initOpenAPI(app: Express): void {
  app.use(express.urlencoded({ extended: true }));
  app.use(express.text());
  app.use(express.json());

  initAPI(app, {
    specFile: "booking-api.yaml", filePath: "booking-api",
  });
  initAPI(app, {
    specFile: "instructor-api.yaml", filePath: "instructor-api",
  });
  initAPI(app, {
    specFile: "member-api.yaml", filePath: "member-api",
  });
  initAPI(app, {
    specFile: "public-api.yaml", filePath: "public-api",
  });
  initAPI(app, {
    specFile: "studio-cms.yaml", filePath: "studio-cms",
  });
  initAPI(app, {
    specFile: "backoffice-cms.yaml", filePath: "backoffice-cms",
  });

  initErrorHandler(app);
}

function initAPI(app: Express, opts: {
  specFile: string,
  filePath: string,
}) {
  const apiSpec = path.join(__dirname, `specs/${opts.specFile}`);
  if (!fs.existsSync(apiSpec)) {
    logger.error(`${apiSpec} does not exist -> skip init`);
    return;
  }
  logger.debug(`initAPI() apiSpec=${apiSpec}, filePath=${opts.filePath}`);

  app.use(
    OpenApiValidator.middleware({
      apiSpec,
      validateRequests: {
        allowUnknownQueryParameters: true,
      },
      validateSecurity: {
        handlers: {
          BearerAuth: firebaseAuth,
        }
      },
      fileUploader: {
        storage: multer.diskStorage({
          destination: process.env.FILE_UPLOADS_DIR!,
        }),
      },
      operationHandlers: {
        basePath: path.join(__dirname, opts.filePath),
        resolver: customResolver,
      },
    })
  );
}

async function mockAuth(
  req: ApiRequest,
  scopes: string[],
  schema: OpenAPIV3.SecuritySchemeObject
) {
  req.idToken = {
    uid: process.env.MOCK_AUTH_UID,
    email: process.env.MOCK_AUTH_EMAIL,
    email_verified: true,
  } as DecodedIdToken;
  return true;
}

async function firebaseAuth(
  req: ApiRequest,
  scopes: string[],
  schema: OpenAPIV3.SecuritySchemeObject
) {
  const authzHeader = req.header("Authorization") ?? "";
  const token
    = authzHeader.startsWith("Bearer ")
    ? authzHeader.substring(7)
    : null;

  const unauthorizedResponse = { status: 401, message: 'Unauthorized' };
  if (!token) {
    throw unauthorizedResponse;
  }
  try {
    const auth = req.url.startsWith("/cms/studio/")
      ? FirebaseAuthStudios
      : req.url.startsWith("/cms/backoffice/")
        ? FirebaseAuthBackOffice
        : FirebaseAuth;
    req.idToken = await auth.verifyIdToken(
      token, false // checkRevoked = false
    );
    return true;
  } catch (e) {
    logger.info(`verifyIdToken() error! ${e}`);
    throw unauthorizedResponse;
  }
}

function customResolver(
  handlersPath: string,
  route: RouteMetadata,
  apiDoc: OpenAPIV3.DocumentV3 | OpenAPIV3.DocumentV3_1,
) {
  // Ref: express-openapi-validator/src/resolvers.ts
  const { basePath, expressRoute, openApiRoute, method } = route;
  const pathKey = openApiRoute.substring(basePath.length);
  // @ts-ignore
  const schema = apiDoc.paths[pathKey][method.toLowerCase()];
  const operationId = schema['operationId'];
  const modulePath = path.join(handlersPath, "routes", operationId);
  const handler = require(modulePath).default;
  return [setUserTimezone, handler];
}

function initErrorHandler(app: Express) {
  app.use(((err, req, res, next) => {
    // format errors
    res.status(err.status || 500).json({
      message: err.message,
      errors: err.errors,
    });
    logger.error(`error=${err}`);
  }) satisfies ErrorRequestHandler);
}

// Extend Express Request object using Typescript
// https://stackoverflow.com/a/72036331
type ApiRequest = Request & { idToken?: DecodedIdToken }
function getIdToken(req: Request): DecodedIdToken {
  return (req as ApiRequest).idToken!;
}
function getOptionalIdToken(req: Request): DecodedIdToken|undefined {
  return (req as ApiRequest).idToken;
}

// Checks if the user authorised for backoffice access
async function isAuthorisedForBackoffice(
  idToken: DecodedIdToken,
): Promise<boolean> {
  if (!idToken.email_verified) {
    logger.debug("email not verified");
    return false;
  }
  const userRecord = await FirebaseAuthBackOffice.getUser(idToken.uid);
  return userRecord?.customClaims?.backoffice == true;
}

export {
  initOpenAPI,
  getIdToken,
  getOptionalIdToken,
  isAuthorisedForBackoffice,
}
