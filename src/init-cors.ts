import logger from "@utils/logger";
import cors, { CorsOptions } from 'cors';
import { Express } from "express";
import process from "process";

const allowedCorsOriginsCfg = process.env.CORS_ALLOWED_ORIGINS?.trim() || '';
const allowedCorsOrigins = allowedCorsOriginsCfg.split(",").map(e => e.trim());
logger.debug(`allowedCorsOrigins=${JSON.stringify(allowedCorsOrigins)}`);

export function initCORS(app: Express) {
  const corsOpts: CorsOptions = {
    origin: (origin, callback) => {
      const ret = !origin
        ? ''
        : allowedCorsOrigins.includes(origin!) ? origin : '';
      callback(null, ret);
    },
    methods: ["GET", "POST", "OPTIONS", "PUT", "PATCH", "DELETE"],
    credentials: true,
    allowedHeaders: [
      "Access-Control-Allow-Headers",
      "Authorization",
      "Access-Control-Allow-Method",
      "Access-Control-Allow-Origin",
      "Access-Control-Allow-Credentials",
      "Content-Type",
      "Ngrok-Skip-Browser-Warning", // for development ngrok tunnel
    ]
  };
  // logger.debug(`corsOpts=${JSON.stringify(corsOpts)}`);
  app.use(cors(corsOpts));
}
