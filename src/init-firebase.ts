import * as admin from 'firebase-admin';
import { getAuth } from "firebase-admin/auth";
import { getMessaging } from "firebase-admin/messaging";

// Main app
const serviceAccountJson = process.env.FIREBASE_APP_CREDENTIALS_JSON;
if (!serviceAccountJson) throw new Error("FIREBASE_APP_CREDENTIALS_JSON is not set");
const serviceAccount = JSON.parse(serviceAccountJson);

const app = admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});
const auth = getAuth(app);
const messaging = getMessaging(app);

// Studios app
const serviceAccountStudiosJson = process.env.FIREBASE_APP_CREDENTIALS_JSON_STUDIOS;
if (!serviceAccountStudiosJson) throw new Error("FIREBASE_APP_CREDENTIALS_JSON_STUDIOS is not set");
const serviceAccountStudios = JSON.parse(serviceAccountStudiosJson);

const appStudios = admin.initializeApp({
  credential: admin.credential.cert(serviceAccountStudios)
}, "studios");
const authStudios = getAuth(appStudios);

// Backoffice app
const serviceAccountBackofficeJson = process.env.FIREBASE_APP_CREDENTIALS_JSON_BACKOFFICE;
if (!serviceAccountBackofficeJson) throw new Error("FIREBASE_APP_CREDENTIALS_JSON_BACKOFFICE is not set");
const serviceAccountBackoffice = JSON.parse(serviceAccountBackofficeJson);

const appBackoffice = admin.initializeApp({
  credential: admin.credential.cert(serviceAccountBackoffice)
}, "backoffice");
const authBackoffice = getAuth(appBackoffice);

export {
  auth as FirebaseAuth,
  authStudios as FirebaseAuthStudios,
  authBackoffice as FirebaseAuthBackOffice,
  messaging as Fcm,
};
