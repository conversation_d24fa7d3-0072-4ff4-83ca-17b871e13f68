import { Instructor } from "@instructor-api/models/instructor";
import { FaveInstructorSaveReq, FaveStudioSaveReq } from "@member-api/models/fave";
import { FaveRepo } from "@member-api/repo/fave-repo";
import { PublicRepo } from "@public-api/repo/public-repo";
import { SearchRepo } from "@booking-api/repo/search-repo";
import { PublicService } from "@public-api/services/public-service";
import { Studio, StudioEquipment } from "@studio-cms/models/studio";
import { LatLng } from "@booking-api/models/search";
import pool from "../../init-pool";
import { StudioRepo } from "@studio-cms/repo/studio-repo";
import { InstructorRepo } from "@instructor-api/repo/instructor-repo";

export class FaveService {

  private faveRepo = new FaveRepo();
  private publicRepo = new PublicRepo();
  private searchRepo = new SearchRepo();
  private studioRepo = new StudioRepo();
  private instructorRepo = new InstructorRepo();

  /////////////
  // Studios //
  /////////////

  async addFaveStudio(req: FaveStudioSaveReq): Promise<number> {
    return this.faveRepo.addFaveStudio(pool, req);
  }

  async removeFaveStudio(req: FaveStudioSaveReq): Promise<number> {
    return this.faveRepo.removeFaveStudio(pool, req);
  }

  async getFaveStudios(userId: string, latLng?: LatLng): Promise<Studio[]> {

    const favStudioQuery = await this.faveRepo.getFaveStudioQuery(pool, userId);
    // cache distance with filtered studio ids
    latLng && await this.searchRepo
    .cacheDistanceToStudiosBatch(pool, latLng, favStudioQuery);

    const ret = await this.faveRepo.getFaveStudios(pool, userId, favStudioQuery, latLng);
    for (const o of ret) {
      o.pictures = await this.studioRepo.prefixStudioPictures(pool, o.pictures);
    }

    const studioIds = ret.map(studio => studio.id);

    const studioRating = await this.publicRepo.getStudioRatingByIds(pool, studioIds);
    const ratingsMap = new Map<string, { rating: number; total: number }>();
    studioRating.forEach(({ id, rating, total }) => {
        ratingsMap.set(id, { rating, total });
    });

    const equipments = await this.publicRepo.getStudioEquipmentTypeByIds(pool, studioIds);
    const equipmentsMap = new Map<string, StudioEquipment[]>();

    // Group equipments by studioId
    equipments.forEach(({ studioId, typeId, typeName }) => {
        if (!equipmentsMap.has(studioId)) {
            equipmentsMap.set(studioId, []);
        }
        equipmentsMap.get(studioId)?.push({ studioId, typeId, typeName });
    });
    for (const studio of ret) {
      const ratingInfo = ratingsMap.get(studio.id);
      studio.avgRating = ratingInfo?.rating ?? 0;
      studio.totalRatingCount = ratingInfo?.total ?? 0;
      studio.equipments = equipmentsMap.get(studio.id) || [];
      studio.fav = true;
  }
    return ret;
  }

  async hasFaveStudio(userId: string, studioId: string): Promise<boolean> {
    return this.faveRepo.hasFaveStudio(pool, userId, studioId);
  }

  /////////////////
  // Instructors //
  /////////////////

  async addFaveInstructor(req: FaveInstructorSaveReq): Promise<number> {
    return this.faveRepo.addFaveInstructor(pool, req);
  }

  async removeFaveInstructor(req: FaveInstructorSaveReq): Promise<number> {
    return this.faveRepo.removeFaveInstructor(pool, req);
  }

  async getFaveInstructors(userId: string): Promise<Instructor[]> {
    const ret = await this.faveRepo.getFaveInstructors(pool, userId);
    for (const o of ret) {
      o.pictures = await this.instructorRepo.prefixInstructorPictures(pool, o.pictures);
    }

    let instrIds = ret.map(instructor => instructor.id);
    const instrRatings = await this.publicRepo.getInstructorsRatingByIds(pool, instrIds);

    const ratingsMap = new Map<string, { rating: number; total: number }>();
    instrRatings.forEach(({ id, rating, total }) => {
        ratingsMap.set(id, { rating, total });
    });

    ret.forEach((instructor) => {
      const ratingInfo = ratingsMap.get(instructor.id);
      instructor.avgRating = ratingInfo?.rating ?? 0;
      instructor.totalRatingCount = ratingInfo?.total ?? 0;
      instructor.fav = true;
  });
    return ret;
  }

  async hasFaveInstructor(userId: string, instructorId: string): Promise<boolean> {
    return this.faveRepo.hasFaveInstructor(pool, userId, instructorId);
  }
}
