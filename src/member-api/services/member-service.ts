import { ServiceError } from "@shared/services";
import logger from "@utils/logger";
import pool from "../../init-pool";
import { Member, MemberRequestDeleteReq, MemberSaveReq, RequestDelete, Credit, SmsOtpSendReq, SmsOtpVerifyReq } from "@member-api/models/member";
import { MemberRepo } from "../repo/member-repo";
import { InstructorRepo } from "@instructor-api/repo/instructor-repo";
import { CreditRepo } from "@member-api/repo/credit-repo";
import { SmsOtpLogRepo } from "@member-api/repo/sms-otp-log-repo";
import { SmsOtpLog } from "@member-api/models/member";
import { By } from "@shared/models";
import { Transaction, TransactionSaveReq, TransactionStatus, TransactionType } from "@booking-api/models/credit";
import { CreditRepo as BookingCreditRepo } from "@booking-api/repo/credit-repo";
import { DateTime } from "luxon";

export class MemberService {

  private memberRepo = new MemberRepo();
  private instructorRepo = new InstructorRepo();
  private creditRepo = new CreditRepo();
  private bookingCreditRepo = new BookingCreditRepo();
  private smsOtpLogRepo = new SmsOtpLogRepo();

  async findByFirebaseUid(uid: string): Promise<Member|undefined> {
    const member = await this.memberRepo.findByFirebaseUid(pool, uid);
    if (member) {
      const credit = await this.creditRepo.findByMemberId(pool, member.id);
      member.credit = credit;
    }
    return member;
  }

  async findMemberCreditByMemberId(uid: string): Promise<Credit|undefined> {
    return await this.creditRepo.findByMemberId(pool, uid);
  }

  async create(req: MemberSaveReq): Promise<string> {
    let conn = null;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // check if uid exists
      const user = await this.memberRepo.findByFirebaseUid(conn, req.member.firebaseUid);
      if (user) {
        logger.info(`uid=${req.member.firebaseUid} already exists`);
        throw new ServiceError("409", "uid already exists");
      }

      // create member
      const memberId = await this.memberRepo.create(conn, req);

      // create credit record
      await this.creditRepo.create(conn, memberId, req.by.username);

      if(+process.env.WELCOME_FREE_CREDIT > 0) {
        const transaction = {
          memberId: memberId,
          type: TransactionType.Promotion,
          amount: +process.env.WELCOME_FREE_CREDIT,
          status: TransactionStatus.Success,
          remark: 'Welcome Rewards',
        } as Transaction;

        await this.bookingCreditRepo.createTrx(conn, {
            transaction: transaction,
            by: req.by,
        } as TransactionSaveReq);

        await this.creditRepo.updateMemberCredit(conn, memberId, Math.abs(+process.env.WELCOME_FREE_CREDIT), 'RegistrationTrigger');
      }

      await conn.commit();
      logger.info(`create member success, userId=${memberId}`);

      return memberId;
    } catch (e) {
      logger.error(`create member error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  async update(req: MemberSaveReq): Promise<number> {
    return this.memberRepo.update(pool, req);
  }

  /////////////////////////
  // Request delete data //
  /////////////////////////

  async requestDelete(req: MemberRequestDeleteReq): Promise<void> {
    return this.memberRepo.requestDelete(pool, req);
  }

  async findRequestDeleteByUid(uid): Promise<RequestDelete> {
    return this.memberRepo.findRequestDeleteByUid(pool, uid);
  }

  async delete(req: MemberRequestDeleteReq): Promise<void> {
    let conn = null;
    try {
      // Temp Delete Account
      conn = await pool.getConnection();
      await conn.beginTransaction();
      const timestamp = DateTime.now().toISO();

      const requestDelete = await this.memberRepo.findRequestDeleteByUid(conn, req.firebaseUid);

      if (requestDelete && !requestDelete.completed) {
        await this.memberRepo.setRequestDeleteComplete(conn, requestDelete.id, req.by, timestamp);
      }

      // instructor
      const instr = await this.instructorRepo.findByFirebaseUid(conn, req.firebaseUid);
      if (instr) {
        await this.instructorRepo.softDelete(conn, instr.id, req.by, timestamp);
        // instructor related records
        await this.instructorRepo.softDeleteRelatedRecords(conn, instr.id, req.by, timestamp);
      }
      // member
      const member = await this.memberRepo.findByFirebaseUid(conn, req.firebaseUid);
      if(member) {
        await this.memberRepo.softDelete(conn, member.id, req.by, timestamp);
      }

      await conn.commit();
      // End Temp Delete Account
    } catch (e) {
      logger.error(`delete account error! ${e}`);
      await conn?.rollback();
      throw e;
    } finally {
      conn?.release();
    }
  }

  /**
   * Log OTP send event
   */
  async logOtpSend(req: SmsOtpSendReq): Promise<string> {
    const conn = await pool.getConnection();
    try {
      const log: SmsOtpLog = {
        userId: req.by.uid,
        phoneNumber: req.phoneNumber,
        action: req.action,
        status: req.status,
        twilioSid: req.twilioSid,
        error: req.error,
        rawData: req.rawData,
      };
      return await this.smsOtpLogRepo.insert(conn, log);
    } finally {
      conn.release();
    }
  }

  /**
   * Log OTP verify event
   */
  async logOtpVerify(req: SmsOtpVerifyReq): Promise<string> {
    const conn = await pool.getConnection();
    try {
      const log: SmsOtpLog = {
        userId: req.by.uid,
        phoneNumber: req.phoneNumber,
        action: req.action,
        status: req.status,
        code: req.code,
        twilioSid: req.twilioSid,
        error: req.error,
        rawData: req.rawData,
      };
      return await this.smsOtpLogRepo.insert(conn, log);
    } finally {
      conn.release();
    }
  }

  /**
   * Check if OTP send attempts exceed the allowed limit per hour
   */
  async isOtpSendRateLimited(phoneNumber: string): Promise<boolean> {
    const conn = await pool.getConnection();
    try {
      const maxPerHour = parseInt(process.env.OTP_SEND_LIMIT_PER_HOUR || "5", 10);
      const count = await this.smsOtpLogRepo.countRecentSends(conn, phoneNumber, 60);
      return count >= maxPerHour;
    } finally {
      conn.release();
    }
  }
}
