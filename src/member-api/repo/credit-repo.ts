import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import logger from "@utils/logger";
import { Credit } from "@member-api/models/member";

export class CreditRepo {
  async create(conn: Connection, memberId: string, by: string): Promise<void> {
    const sql = `insert into usr05credit (
      member_id,
      current_credits,
      created_at,
      created_by,
      updated_at,
      updated_by
    ) values (
      ?, ?, now(), ?, now(), ?
    )`;

    const params = [
      memberId,
      0, // initial credits
      by,
      by
    ];

    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created credit record for member=${memberId}, numRows=${results.affectedRows}`);
  }

  async updateMemberCredit(
    conn: Connection, memberId: string, amt: number, by: string, extend: number|null = null
  ): Promise<number> {
    const sql = `update usr05credit set
      current_credits = current_credits + ?,
      credit_expired_date = CASE 
        WHEN ? IS NULL THEN credit_expired_date
        ELSE DATE_ADD(
          NOW(),
          INTERVAL ? MONTH
        )
      END,
      notified_expired = ?,
      updated_by = ?
      where member_id = ?`;
    logger.debug(`[${by}] updateMemberCredit() memberId=${memberId}, amt=${amt}, extend=${extend}`)
    const params = [
      amt,
      extend,
      extend,
      extend !== null && extend > 0 ? false : true,
      by,
      memberId
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updateMemberCredit() memberId=${memberId}, amt=${amt}, extend=${extend}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async findByMemberId(conn: Connection, memberId: string): Promise<Credit|undefined> {
    const sql = `select * from usr05credit where member_id = ?`;
    const [results] = await conn.execute<RowDataPacket[]>(sql, [memberId]);
    if (results.length === 0) {
      return undefined;
    }
    return this.toCredit(results[0]);
  }

  async findExpiredCredits(conn: Connection): Promise<Credit[]> {
    const sql = `select * from usr05credit
      where credit_expired_date < NOW()
      and current_credits > 0
      and notified_expired = false`;
    const [results] = await conn.execute<RowDataPacket[]>(sql);
    return results.map(row => this.toCredit(row));
  }

  async bulkResetExpiredCredits(conn: Connection, by: string): Promise<number> {
    const sql = `update usr05credit
      set current_credits = 0,
          updated_by = ?,
          notified_expired = true
      where credit_expired_date < NOW()
        and current_credits > 0`;

    const [results] = await conn.execute<ResultSetHeader>(sql, [by]);
    logger.info(`[${by}] bulkResetExpiredCredits() reset ${results.affectedRows} members`);
    return results.affectedRows;
  }

  private toCredit(row: any): Credit {
    return {
      memberId: row['member_id'],
      currentCredits: row['current_credits'],
      creditExpiredDate: row['credit_expired_date'],
      notifiedExpired: row['notified_expired'],
    };
  }
}