import { Instructor } from "@instructor-api/models/instructor";
import { InstructorRepo } from "@instructor-api/repo/instructor-repo";
import { FaveInstructorSaveReq, FaveStudioSaveReq } from "@member-api/models/fave";
import { Studio } from "@studio-cms/models/studio";
import { StudioRepo } from "@studio-cms/repo/studio-repo";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import { LatLng } from "@booking-api/models/search";

export class FaveRepo {

  /////////////
  // Studios //
  /////////////

  async addFaveStudio(
    conn: Connection, req: FaveStudioSaveReq,
  ): Promise<number> {
    const sql = `insert ignore into usr02fave_studios (
        user_id, studio_id, created_by
      ) value (?, ?, ?)`;
    const by = req.by.username;
    logger.debug(`[${by}] addFaveStudio() req=${JSON.stringify(req)}`)
    const params = [
      req.userId!,
      req.studioId!,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] addFaveStudio()`
      + ` userId=${req.userId}, studioId=${req.studioId} numRows=${affectedRows}`);
    return affectedRows;
  }

  async removeFaveStudio(
    conn: Connection, req: FaveStudioSaveReq,
  ): Promise<number> {
    const sql = "delete from usr02fave_studios where user_id = ? and studio_id = ?";
    const by = req.by.username;
    logger.debug(`[${by}] removeFaveStudio() req=${JSON.stringify(req)}`)
    const params = [
      req.userId!,
      req.studioId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] removeFaveStudio()`
      + ` userId=${req.userId}, studioId=${req.studioId} numRows=${affectedRows}`);
    return affectedRows;
  }

  async getFaveStudios(
    conn: Connection, userId: string, favStudioQuery?: string, latLng?: LatLng
  ): Promise<Studio[]> {
    const columns = ["fs.*"];
    const clauses = [];
    const params = [];
    const joins = [];
    let order = "";

    {
      clauses.push("(fs.user_id = ?)");
      params.push(userId);
    }
    {
      clauses.push("(fs.deleted is false)");
    }

    if (favStudioQuery) {
      clauses.push(`(fs.id in (${favStudioQuery}))`);
    }
      // distance within
    if(latLng) {
      joins.push(` left join app03dist_cache d on fs.latlng = d.latlng_to and d.latlng_from = '${latLng.toCacheString()}'`);
      columns.push('d.distance');
      order = " order by d.distance asc";
    }

    // select items
    let selectSql = `select ${columns.join(", ")} from vw_usr02fave_studios fs`;
    if (joins.length !== 0) {
      selectSql += `${joins.join(" ")}`
    }
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }
    selectSql += order;

    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    return results.map(o => StudioRepo.toStudio(o));
  }

  async getFaveStudioQuery(
    conn: Connection, userId: string,
  ): Promise<string> {
    const sql = `select studio_id from usr02fave_studios
      where user_id = ?`;
    const params = [userId];
    return conn.format(sql, params);
  }

  async hasFaveStudio(
    conn: Connection, userId: string, studioId: string,
  ): Promise<boolean> {
    const sql = `select count(studio_id) as \`count\` from usr02fave_studios
      where user_id = ? and studio_id = ?`;
    const params = [userId, studioId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results[0]['count'] !== 0;
  }

  /////////////////
  // Instructors //
  /////////////////

  async addFaveInstructor(
    conn: Connection, req: FaveInstructorSaveReq,
  ): Promise<number> {
    const sql = `insert ignore into usr03fave_instrs (
        user_id, instr_id, created_by
      ) value (?, ?, ?)`;
    const by = req.by.username;
    logger.debug(`[${by}] addFaveInstructor() req=${JSON.stringify(req)}`)
    const params = [
      req.userId!,
      req.instructorId!,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] addFaveInstructor()`
      + ` userId=${req.userId}, instrId=${req.instructorId} numRows=${affectedRows}`);
    return affectedRows;
  }

  async removeFaveInstructor(
    conn: Connection, req: FaveInstructorSaveReq,
  ): Promise<number> {
    const sql = `delete from usr03fave_instrs where user_id = ? and instr_id = ?`;
    const by = req.by.username;
    logger.debug(`[${by}] removeFaveInstructor() req=${JSON.stringify(req)}`)
    const params = [
      req.userId!,
      req.instructorId!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] removeFaveInstructor()`
      + ` userId=${req.userId}, instrId=${req.instructorId} numRows=${affectedRows}`);
    return affectedRows;
  }

  async getFaveInstructors(
    conn: Connection, userId: string,
  ): Promise<Instructor[]> {
    const sql = `select * from vw_usr03fave_instrs
      where user_id = ? and deleted is false
      and onboarded is true`;
    const params = [userId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => InstructorRepo.toInstructor(o));
  }

  async hasFaveInstructor(
    conn: Connection, userId: string, instructorId: string,
  ): Promise<boolean> {
    const sql = `select count(instr_id) as \`count\` from usr03fave_instrs
      where user_id = ? and instr_id = ?`;
    const params = [userId,  instructorId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results[0]['count'] !== 0;
  }
}
