import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import { Contact, ContactNo, Member, MemberRequestDeleteReq, MemberSaveReq, RequestDelete } from "../models/member";
import { By } from "@shared/models";

export class MemberRepo {

  async findByFirebaseUid(conn: Connection, uid: string): Promise<Member|undefined> {
    const sql = "select * from usr01members where fbase_uid = ?";
    const params = [uid];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return this.toMember(results[0]);
  }

  private toMember(row: any): Member {
    return {
      id: row['id'],
      firebaseUid: row['fbase_uid'],
      firebaseEmail: row['fbase_email'],
      fullName: row['full_name'],
      displayName: row['display_name'],
      gender: row['gender'],
      mobileNo: {
        countryCode: row['mobile_ctry'],
        number: row['mobile_no'],
      } as ContactNo,
      emergencyContact: {
        name: row['emerg_cont'],
        contactNo: {
          countryCode: row['emerg_cont_ctry'],
          number: row['emerg_cont_no'],
        },
      } as Contact,
      timezone: row['timezone'] ?? 'Asia/Singapore',
      countryCode: row['country_code'] ?? 'SG',
      registeredAt: row['reg_at'],
      deleted: row['deleted'] === 1,
    }
  }

  async create(conn: Connection, req: MemberSaveReq): Promise<string> {
    const sql = `insert into usr01members (
        id, fbase_uid, fbase_email,
        full_name, display_name, gender,
        mobile_ctry, mobile_no,
        emerg_cont, emerg_cont_ctry, emerg_cont_no,
        reg_at,
        created_by
      ) values (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
        ?, now(), ?
      )`;
    const by = req.by.username;
    const member = req.member;
    const mobileNo = member.mobileNo;
    const emergencyContact = member.emergencyContact;
    const firebaseUid = member.firebaseUid;
    logger.debug(`[${by}] create() member=${JSON.stringify(member)}`)
    const params = [
      firebaseUid, // id
      firebaseUid, // fbase_uid
      member.firebaseEmail,
      member.fullName,
      member.displayName,
      member.gender,
      mobileNo?.countryCode ?? '',
      mobileNo?.number ?? '',
      emergencyContact?.name ?? '',
      emergencyContact?.contactNo!?.countryCode ?? '',
      emergencyContact?.contactNo!?.number ?? '',
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] created member, id=${firebaseUid}, numRows=${results.affectedRows}`);
    return firebaseUid;
  }

  async update(conn: Connection, req: MemberSaveReq): Promise<number> {
    const sql = `update usr01members set
      full_name = ?, display_name = ?, gender = ?,
      mobile_ctry = ?, mobile_no = ?,
      emerg_cont = ?, emerg_cont_ctry = ?, emerg_cont_no = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by.username;
    const member = req.member;
    const mobileNo = member.mobileNo;
    const emergencyContact = member.emergencyContact;
    logger.debug(`[${by}] update() member=${JSON.stringify(member)}`)
    const params = [
      member.fullName,
      member.displayName,
      member.gender,
      mobileNo.countryCode,
      mobileNo.number,
      emergencyContact.name,
      emergencyContact.contactNo.countryCode,
      emergencyContact.contactNo.number,
      by,
      member.id!,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] updated member, id=${member.id}, numRows=${affectedRows}`);
    return affectedRows;
  }

  ////////////////////
  // Request delete //
  ////////////////////

  async requestDelete(
    conn: Connection,
    req: MemberRequestDeleteReq,
  ): Promise<void> {
    const sql = `insert into usr04req_delete (
        at, fbase_uid, fbase_email,
        created_by
      ) values (
        now(), ?, ?, ?
      )`;
    const by = req.by.username;
    logger.debug(`[${by}] requestDelete()`)
    const params = [
      req.firebaseUid,
      req.firebaseEmail,
      by,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    logger.info(`[${by}] requestDelete() numRows=${results.affectedRows}`);
  }

  async findRequestDeleteByUid(conn: Connection, uid: string): Promise<RequestDelete|undefined> {
    const sql = "select * from usr04req_delete where fbase_uid = ? order by at desc limit 1";
    const params = [uid];

    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    if (results.length == 0) {
      return undefined;
    }
    return this.toMemberRequestDelete(results[0]);
  }

  private toMemberRequestDelete (row: any): RequestDelete {
    return {
      id: row['id'],
      firebaseEmail: row['fbase_email'],
      firebaseUid: row['fbase_uid'],
      completed: row['completed'],
    };
  }

  async setRequestDeleteComplete(conn: Connection, id: string, by: By, timestamp: string): Promise<number> {
    const sql = `update usr04req_delete set completed = true, updated_by = ?, fbase_uid = CONCAT(fbase_uid, ?) where id = ?`;
    const byWho = by.username;
    logger.debug(`[${byWho}] setRequestDeleteComplete() request delete id=${id}`)
    const params = [byWho, timestamp, id];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${byWho}] set request delete complete, id=${id}, numRows=${affectedRows}`);
    return affectedRows;
  }

  ////////////////////
  // Approve delete //
  ////////////////////
  async softDelete(conn: Connection, memberId: string, by: By, timestamp: string): Promise<number> {
    const sql = `update usr01members set deleted = true, deleted_at = now(), deleted_by = ?, updated_by = ?, id = CONCAT(id, ?), fbase_uid = CONCAT(fbase_uid, ?), fbase_email = CONCAT(fbase_email, ?) where id = ?`;
    const byWho = by.username;
    logger.debug(`[${byWho}] softDelete() member by=${JSON.stringify(memberId)}`)
    const params = [byWho, byWho, timestamp, timestamp, timestamp, memberId];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${byWho}] soft deleted member, id=${memberId}, numRows=${affectedRows}`);

    // soft delete related records
    const [results2] = await conn.execute<ResultSetHeader>(`update usr05credit set updated_by = ?, member_id = CONCAT(member_id, ?) where member_id = ?`, [byWho, timestamp, memberId]);
    logger.info(`[${byWho}] soft deleted member usr05credits, id=${memberId}, numRows=${results2.affectedRows}`);

    const [results3] = await conn.execute<ResultSetHeader>(`update usr02fave_studios set updated_by = ?, user_id = CONCAT(user_id, ?) where user_id = ?`, [byWho, timestamp, memberId]);
    logger.info(`[${byWho}] soft deleted member usr02fave_studios, id=${memberId}, numRows=${results3.affectedRows}`);

    const [results4] = await conn.execute<ResultSetHeader>(`update usr03fave_instrs set updated_by = ?, user_id = CONCAT(user_id, ?) where user_id = ?`, [byWho, timestamp, memberId]);
    logger.info(`[${byWho}] soft deleted member usr03fave_instrs, id=${memberId}, numRows=${results4.affectedRows}`);

    const [results5] = await conn.execute<ResultSetHeader>(`delete from fcm01tokens where user_id = ?`, [memberId]);
    logger.info(`[${byWho}] hard deleted member fcm01tokens, id=${memberId}, numRows=${results5.affectedRows}`);

    const [results6] = await conn.execute<ResultSetHeader>(`delete from fcm02config where user_id = ?`, [memberId]);
    logger.info(`[${byWho}] hard deleted member fcm02config, id=${memberId}, numRows=${results6.affectedRows}`);

    const [results7] = await conn.execute<ResultSetHeader>(`update fcm03messages set updated_by = ?, user_id = CONCAT(user_id, ?) where user_id = ?`, [byWho, timestamp, memberId]);
    logger.info(`[${byWho}] soft deleted member fcm03messages, id=${memberId}, numRows=${results7.affectedRows}`);

    const [results8] = await conn.execute<ResultSetHeader>(`update credit03transaction set deleted = true, deleted_at = now(), deleted_by = ?, updated_by = ?, member_id = CONCAT(member_id, ?) where member_id = ?`, [byWho, byWho, timestamp, memberId]);
    logger.info(`[${byWho}] soft deleted member credit03transaction, id=${memberId}, numRows=${results8.affectedRows}`);

    // Bookings
    const [results9] = await conn.execute<ResultSetHeader>(`update evt01events set deleted = true, deleted_at = now(), deleted_by = ?, updated_by = ?, member_id = CONCAT(member_id, ?) where member_id = ? AND start_at >= CONVERT_TZ(NOW(), @@session.time_zone, timezone)`, [byWho, byWho, timestamp, memberId]);
    const [results10] = await conn.execute<ResultSetHeader>(`update evt01events set updated_by = ?, member_id = CONCAT(member_id, ?) where member_id = ? AND start_at < CONVERT_TZ(NOW(), @@session.time_zone, timezone)`, [byWho, timestamp, memberId]);

    logger.info(`[${byWho}] soft deleted member evt01events, id=${memberId}, numRows=${results9.affectedRows + results10.affectedRows}`);

    const [results11] = await conn.execute<ResultSetHeader>(`update evt02sessions set member_id = CONCAT(member_id, ?) where member_id = ?`, [timestamp, memberId]);
    logger.info(`[${byWho}] soft deleted member evt02sessions, id=${memberId}, numRows=${results11.affectedRows}`);

    const [results12] = await conn.execute<ResultSetHeader>(`update evt03cancel_req set user_id = CONCAT(user_id, ?) where user_id = ?`, [timestamp, memberId]);
    logger.info(`[${byWho}] soft deleted member evt03cancel_req, id=${memberId}, numRows=${results12.affectedRows}`);

    return affectedRows;
  }

}
