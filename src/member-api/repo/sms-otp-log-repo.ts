import { Connection, ResultSetHeader } from "mysql2/promise";
import { SmsOtpLog } from "@member-api/models/member";

export class SmsOtpLogRepo {
  async insert(conn: Connection, log: SmsOtpLog): Promise<string> {
    const sql = `INSERT INTO sms01otp_log
      (user_id, phone_number, action, status, twilio_sid, code, raw_data, error)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
    const params = [
      log.userId,
      log.phoneNumber,
      log.action,
      log.status,
      log.twilioSid || null,
      log.code || null,
      log.rawData || null,
      log.error || null,
    ];
    const [result] = await conn.execute<ResultSetHeader>(sql, params);
    return (result as ResultSetHeader).insertId.toString();
  }

  async countRecentSends(conn: Connection, phoneNumber: string, minutes: number): Promise<number> {
    const sql = `SELECT COUNT(*) as cnt FROM sms01otp_log
      WHERE phone_number = ? AND action = 'send' AND created_at > (NOW() - INTERVAL ? MINUTE)`;
    const params = [phoneNumber, minutes];
    const [rows]: any = await conn.query(sql, params);
    return rows[0]?.cnt || 0;
  }
}