import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { MemberService } from "../services/member-service";
import { createCometChatUser } from "@utils/cometchat";
import { CometChatUser } from "@public-api/models/cometchat";
import { InstructorService } from "../../instructor-api/services/instructor-service";

const memberService = new MemberService();
const instructorService = new InstructorService();

const cometchatCreateUser: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const member = await memberService.findByFirebaseUid(uid);
  if (!member) {
    res.status(404).json({ success: false, message: "Member not found", data: null });
    return;
  }

  // Check if this member is also an instructor
  const instructor = await instructorService.findByFirebaseUid(uid);
  const userType = instructor ? "instructor" : "member";
  const tags = ["member", ...(instructor ? ["instructor"] : [])];

  const result = await createCometChatUser({
    uid: member.firebaseUid,
    email: member.firebaseEmail,
    name: member.fullName,
    phone: member.mobileNo?.countryCode + member.mobileNo?.number,
    tags,
    userType,
  } as CometChatUser);
  if (!result.success) {
    res.status(500).json(result);
    return;
  }
  res.status(200).json(result);
};

export default cometchatCreateUser; 