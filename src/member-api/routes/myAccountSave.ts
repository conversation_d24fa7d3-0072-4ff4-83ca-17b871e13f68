import logger from "@utils/logger";
import { Request<PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { MemberService } from "../services/member-service";
import { toMemberSaveReq } from "./shared/request-parsers";

const memberService = new MemberService();

const myAccountSave: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;

  const ret = await memberService.findByFirebaseUid(uid);
  if (!ret || ret.deleted) {
    logger.info(`uid=${uid}, email=${idToken.email} does not exist!`);
    res.status(204).end(); // no content
    return;
  }

  const toSave = toMemberSaveReq(body, idToken);
  toSave.member.id = idToken.uid; // (!) important
  await memberService.update(toSave)
  res.status(200).end()
};

export default myAccountSave;
