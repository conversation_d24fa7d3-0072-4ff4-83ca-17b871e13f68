import logger from "@utils/logger";
import { RequestHand<PERSON> } from "express";
import { checkVerification } from "@utils/twilio-verify";
import { MemberService } from "../services/member-service";
import { getIdToken } from "../../init-openapi";
import { toBy } from "@shared/request-parsers";
import { SmsOtpVerifyReq } from "@member-api/models/member";

const memberService = new MemberService();

const verifyOtp: RequestHandler = async (req, res) => {
    const idToken = getIdToken(req);
    const uid = idToken.uid;
    const { phoneNumber, code } = req.body;
    let logId: string | undefined;
    let logOtpVerifyReq: SmsOtpVerifyReq = {
        by: toBy(idToken),
        phoneNumber,
        code,
        action: "verify",
        status: "success",
    };
    try {
        const otp = await checkVerification(phoneNumber, code);
        logOtpVerifyReq.twilioSid = otp.sid;
        logOtpVerifyReq.rawData = JSON.stringify(otp);
        if(otp.status !== "approved") {
            throw new Error("Invalid OTP");
        }
        logId = await memberService.logOtpVerify(logOtpVerifyReq);
        res.status(200).json({ success: true, data: { logId } });
    } catch (error: any) {
        logOtpVerifyReq.status = "failed";
        logOtpVerifyReq.error = error?.message || String(error);
        await memberService.logOtpVerify(logOtpVerifyReq);
        logger.error(`[verifyOtp] error=${error}`);
        res.status(500).json({ success: false, error: "Failed to verify OTP" });
    }
};

export default verifyOtp;
