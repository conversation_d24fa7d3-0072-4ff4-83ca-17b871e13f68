import { FcmMessageStatus, FcmMessageFindCriteria } from "@fcm/models/fcm-message";
import { FcmService } from "@fcm/services/fcm-service";
import { toFcmMessageJson } from "@member-api/routes/shared/response-output";
import { Page, Sort } from "@shared/services";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { BookingService } from "@booking-api/services/booking-service";

const fcmService = new FcmService();
const bookingService = new BookingService();

const inbox: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const page = new Page(+req.params.page, +req.params.pageSize);
  const sort = new Sort("at", false); // sortAsc = false
  const [messages, unreadCount] = await Promise.all([
    fcmService.findMessages({
      userId: uid,
      scheduledToSend: true
    } as FcmMessageFindCriteria, page, sort),
    fcmService.getUnreadCount(uid)
  ]);

  res.status(200).json({
    messages: messages.map(e => toFcmMessageJson(e, { timezone: req.userTimezone })),
    unreadCount
  });
};

export default inbox;
