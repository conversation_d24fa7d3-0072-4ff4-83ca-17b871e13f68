import { RequestHand<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { createCometChatAuthToken } from "@utils/cometchat";

const cometchatCreateAuthToken: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const result = await createCometChatAuthToken(uid);
  if (!result.success) {
    res.status(500).json(result);
    return;
  }
  res.status(200).json(result);
};

export default cometchatCreateAuthToken; 