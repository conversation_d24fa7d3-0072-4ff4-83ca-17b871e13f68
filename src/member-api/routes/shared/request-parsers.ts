import { FcmToken, FcmTokenSaveReq } from "@fcm/models/fcm-token";
import { toBy } from "@shared/request-parsers";
import { auth } from "firebase-admin";
import { Contact, ContactNo, Member, MemberSaveReq } from "../../models/member";
import DecodedIdToken = auth.DecodedIdToken;

export function toMemberSaveReq(
  body: any, idToken: DecodedIdToken
): MemberSaveReq {
  const emergencyContact = body.emergencyContact;
  const member = {
    fullName: body.fullName,
    firebaseUid: idToken.uid,
    firebaseEmail: idToken.email,
    displayName: body.displayName ?? body.fullName,
    gender: body.gender,
    mobileNo: {
      countryCode: body.mobileNo?.countryCode,
      number: body.mobileNo?.number,
    } as ContactNo,
    emergencyContact: {
      name: emergencyContact?.name,
      contactNo: {
        countryCode: emergencyContact?.contactNo?.countryCode,
        number: emergencyContact?.contactNo?.number,
      } as ContactNo,
    } as Contact
  } as Member;
  return {
    member: member,
    by: toBy(idToken),
  } as MemberSaveReq;
}

export function toFcmTokenSaveReq(
  body: any, idToken: DecodedIdToken
): FcmTokenSaveReq {
  return {
    token: {
      userId: idToken.uid, // (!) important
      token: body.token ?? '',
    } as FcmToken,
    by: toBy(idToken),
  } as FcmTokenSaveReq;
}
