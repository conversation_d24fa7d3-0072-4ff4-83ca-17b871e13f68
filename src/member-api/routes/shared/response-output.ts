import { FcmMessage } from "@fcm/models/fcm-message";
import { DateTime } from 'luxon';
import { Member } from "../../models/member";

export function toMember<PERSON>son(member: Member) {
  const emergencyContact = member.emergencyContact;
  return {
    email: member.firebaseEmail,
    fullName: member.fullName,
    displayName: member.displayName,
    gender: member.gender,
    mobileNo: {
      countryCode: member.mobileNo.countryCode,
      number: member.mobileNo.number,
    },
    emergencyContact: {
      name: emergencyContact.name,
      contactNo: {
        countryCode: emergencyContact.contactNo.countryCode,
        number: emergencyContact.contactNo.number,
      }
    },
    ...(member.requestDelete != undefined) && { requestDelete: member.requestDelete},
    ...(member.credit != undefined) && {
      credit: {
        currentCredits: Number(member.credit.currentCredits).toFixed(0),
        creditExpiredDate: member.credit.creditExpiredDate ? DateTime.fromFormat(member.credit.creditExpiredDate, "yyyy-MM-dd HH:mm:ss", { zone: 'UTC' }).setZone(member.timezone).toFormat('yyyy-MM-dd HH:mm:ss') : null,
        notifiedExpired: member.credit.notifiedExpired
      }
    },
    /*
    registeredAt: formatDate(member.registeredAt, "yyyy-MM-dd HH:mm:ss"),
    */
  }
}

export function toFcmMessageJson(message: FcmMessage, { timezone = 'Asia/Singapore' }) {
  return {
    id: message.id,
    title: message.title,
    body: message.body,
    go: message.go,
    at: DateTime.fromFormat(message.at, "yyyy-MM-dd HH:mm:ss", { zone: 'UTC' }).setZone(timezone).toFormat('yyyy-MM-dd HH:mm:ss'),
    isRead: message.isRead
  };
}
