import { FaveStudioSaveReq } from "@member-api/models/fave";
import { FaveService } from "@member-api/services/fave-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const faveService = new FaveService();

const faveStudiosAdd: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  await faveService.addFaveStudio({
    userId: uid,
    studioId: req.params['studioId']!,
    by: toBy(idToken),
} as FaveStudioSaveReq)
  res.status(200).end()
};

export default faveStudiosAdd;
