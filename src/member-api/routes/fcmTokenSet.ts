import { FcmService } from "@fcm/services/fcm-service";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { toFcmTokenSaveReq } from "./shared/request-parsers";

const fcmService = new FcmService();

const fcmTokenSet: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const body = req.body;

  const toSave = toFcmTokenSaveReq(body, idToken);
  await fcmService.setToken(toSave)
  res.status(200).end()
};

export default fcmTokenSet;
