import { FcmService } from "@fcm/services/fcm-service";
import { RequestHand<PERSON>, Router } from "express";
import { getIdToken } from "../../init-openapi";

const fcmService = new FcmService();

const fmcMarkAsRead: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const messageId = req.params.messageId;

  try {
    const affectedRows = await fcmService.markAsRead(messageId, uid, idToken.email);
    if (affectedRows === 0) {
      res.status(404).json({ error: "Message not found" });
      return;
    }
    res.status(200).json({ success: true });
  } catch (error) {
    res.status(500).json({ error: "Failed to mark message as read" });
  }
};

export default fmcMarkAsRead;
