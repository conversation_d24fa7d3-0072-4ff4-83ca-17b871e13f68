import { FcmBlackoutTimeSaveReq } from "@fcm/models/fcm-config";
import { FcmService } from "@fcm/services/fcm-service";
import { toBy } from "@shared/request-parsers";
import { DateTime } from "luxon";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const fcmService = new FcmService();

const fcmBlackoutSetTime: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const start = req.query.start!.toString();
  const startTime = DateTime.fromFormat(start, "HHmm");
  const end = req.query.end!.toString();
  const endTime = DateTime.fromFormat(end, "HHmm");
  if (!startTime.isValid || !endTime.isValid) {
    res.status(400).end("invalid time");
    return;
  }

  await fcmService.setBlackoutTime({
    userId: uid,
    start: startTime.toFormat('HH:mm:ss'),
    end: endTime.toFormat('HH:mm:ss'),
    by: toBy(idToken),
  } as FcmBlackoutTimeSaveReq)
  res.status(200).end();
};

export default fcmBlackoutSetTime;
