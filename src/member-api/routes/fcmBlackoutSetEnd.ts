import { FcmBlackoutSaveReq } from "@fcm/models/fcm-config";
import { FcmService } from "@fcm/services/fcm-service";
import { toBy } from "@shared/request-parsers";
import { DateTime } from "luxon";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const fcmService = new FcmService();

const fcmBlackoutSetEnd: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const v = req.query.v!.toString();
  const time = DateTime.fromFormat(v, "HHmm");
  if (!time.isValid) {
    res.status(400).end("invalid time");
    return;
  }

  await fcmService.setBlackoutEnd({
    userId: uid,
    time: time.toFormat('HH:mm:ss'),
    by: toBy(idToken),
  } as FcmBlackoutSaveReq)
  res.status(200).end();
};

export default fcmBlackoutSetEnd;
