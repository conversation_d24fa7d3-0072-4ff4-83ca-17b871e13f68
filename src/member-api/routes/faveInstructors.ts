import { toInstructor<PERSON><PERSON> } from "@instructor-api/routes/shared/response-output";
import { FaveService } from "@member-api/services/fave-service";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const faveService = new FaveService();

const faveInstructors: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const ret = await faveService.getFaveInstructors(uid);
  res.status(200).json(
    ret.map(o => toInstructorJson(o))
  );
};

export default faveInstructors;
