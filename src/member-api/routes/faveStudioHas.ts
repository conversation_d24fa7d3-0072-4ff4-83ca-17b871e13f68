import { FaveService } from "@member-api/services/fave-service";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const faveService = new FaveService();

const faveStudioHas: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const studioId = req.params.studioId!;
  const ret = await faveService.hasFaveStudio(uid, studioId);
  res.status(200).json(ret);
};

export default faveStudioHas;
