import { FaveInstructorSaveReq } from "@member-api/models/fave";
import { FaveService } from "@member-api/services/fave-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const faveService = new FaveService();

const faveInstructorsAdd: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  await faveService.addFaveInstructor({
    userId: uid,
    instructorId: req.params['instructorId']!,
    by: toBy(idToken),
} as FaveInstructorSaveReq)
  res.status(200).end()
};

export default faveInstructorsAdd;
