import logger from "@utils/logger";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { startVerification } from "@utils/twilio-verify";
import { MemberService } from "../services/member-service";
import { getIdToken } from "../../init-openapi";
import { toBy } from "@shared/request-parsers";
import { SmsOtpSendReq } from "@member-api/models/member";

const memberService = new MemberService();

const sendOtp: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const { phoneNumber } = req.body;
  let logOtpSendReq: SmsOtpSendReq = {
    by: toBy(idToken),
    phoneNumber,
    action: "send",
    status: "success",
  };
  try {
    // Rate limit check
    const isLimited = await memberService.isOtpSendRateLimited(phoneNumber);
    if (isLimited) {
      logOtpSendReq.status = "rate_limited";
      logOtpSendReq.error = `Exceeded OTP send limit (${process.env.OTP_SEND_LIMIT_PER_HOUR || 5} per hour)`;
      await memberService.logOtpSend(logOtpSendReq);
      // Throw a custom error object
      throw { status: 429, message: logOtpSendReq.error };
    }

    const otp = await startVerification(phoneNumber);
    logOtpSendReq.twilioSid = otp.sid;
    logOtpSendReq.rawData = JSON.stringify(otp);
    if (otp.status !== "pending") {
      throw new Error("Failed to send OTP");
    }

    const logId = await memberService.logOtpSend(logOtpSendReq);
    res.status(200).json({ success: true, data: { logId } });
  } catch (error: any) {
    // If error has a status, use it; otherwise, default to 500
    logOtpSendReq.status = logOtpSendReq.status === "rate_limited" ? "rate_limited" : "failed";
    logOtpSendReq.error = error?.message || error?.toString();
    await memberService.logOtpSend(logOtpSendReq);
    logger.error(`[sendOtp] error=${JSON.stringify(error)}`);
    let status = error.status || 500;
    let message = error.message || "Failed to send OTP";
    if(error.code === 60200) {
      status = 400;
      message = "The phone number is invalid. Please enter a different number.";
    }
    res.status(status).json({ success: false, error: message });
  }
};

export default sendOtp;
