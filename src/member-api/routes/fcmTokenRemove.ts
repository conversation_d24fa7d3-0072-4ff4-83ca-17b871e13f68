import { FcmService } from "@fcm/services/fcm-service";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { toFcmTokenSaveReq } from "./shared/request-parsers";

const fcmService = new FcmService();

const fcmTokenRemove: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const toSave = toFcmTokenSaveReq({}, idToken);
  await fcmService.removeToken(toSave)
  res.status(200).end()
};

export default fcmTokenRemove;
