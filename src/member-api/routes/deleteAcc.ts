import { MemberRequestDeleteReq } from "@member-api/models/member";
import { MemberService } from "@member-api/services/member-service";
import { toBy } from "@shared/request-parsers";
import { Request<PERSON>and<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";

const memberService = new MemberService();

const deleteAcc: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  try {
    // Testing purpose
    await memberService.delete({
      firebaseUid: uid,
      firebaseEmail: idToken.email,
      by: toBy(idToken),
    } as MemberRequestDeleteReq);
    res.status(200).end();
  } catch (error) {
    res.status(404).json(error.toString())
  }
};

export default deleteAcc;
