import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { MemberService } from "../services/member-service";
import { toMemberJson } from "./shared/response-output";

const memberService = new MemberService();

const myAccount: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const ret = await memberService.findByFirebaseUid(uid);
  if (!ret || ret.deleted) {
    res.status(204).end(); // no content
  } else {
    const requestDelete = await memberService.findRequestDeleteByUid(uid);
    requestDelete && (ret.requestDelete = true);
    res.status(200).json(toMemberJson(ret));
  }
};

export default myAccount;
