import { FaveService } from "@member-api/services/fave-service";
import { toBy } from "@shared/request-parsers";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const faveService = new FaveService();

const faveStudioRemove: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const studioId = req.params.studioId!;
  const ret = await faveService.removeFaveStudio({
    userId: uid,
    studioId: studioId,
    by: toBy(idToken),
  });
  res.status(200).json();
};

export default faveStudioRemove;
