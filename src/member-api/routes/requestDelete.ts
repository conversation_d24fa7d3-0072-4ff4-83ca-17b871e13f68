import { MemberRequestDeleteReq } from "@member-api/models/member";
import { MemberService } from "@member-api/services/member-service";
import { toBy } from "@shared/request-parsers";
import { Request<PERSON>and<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";

const memberService = new MemberService();

const requestDelete: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  await memberService.requestDelete({
    firebaseUid: uid,
    firebaseEmail: idToken.email,
    by: toBy(idToken),
  } as MemberRequestDeleteReq);
  res.status(200).end();
};

export default requestDelete;
