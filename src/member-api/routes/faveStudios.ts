import { FaveService } from "@member-api/services/fave-service";
import { toStudioJson } from "@studio-cms/routes/shared/response-output";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import { LatLng } from "@booking-api/models/search";

const faveService = new FaveService();

const faveStudios: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const latLng = (req.query.lat && req.query.lng) ? new LatLng(
    +req.query.lat!.toString().trim(),
    +req.query.lng!.toString().trim(),
  ): null;

  const ret = await faveService.getFaveStudios(uid, latLng);
  res.status(200).json(
    ret.map(o => toStudioJson(o))
  );
};

export default faveStudios;
