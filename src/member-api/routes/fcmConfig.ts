import { FcmService } from "@fcm/services/fcm-service";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";
import blackoutSave from "@instructor-api/routes/blackoutSave";

const fcmService = new FcmService();

const fcmConfig: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const config = await fcmService.findFcmConfigByUserId(uid)
  res.status(200).json({
    blackout: !(config.blackoutStart == config.blackoutEnd),
    blackoutStart: config.blackoutStart.substring(0, 5),
    blackoutEnd: config.blackoutEnd.substring(0, 5),
  });
};

export default fcmConfig;
