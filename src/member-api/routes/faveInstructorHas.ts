import { FaveService } from "@member-api/services/fave-service";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const faveService = new FaveService();

const faveInstructorHas: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const instrId = req.params.instructorId!;
  const ret = await faveService.hasFaveInstructor(uid, instrId);
  res.status(200).json(ret);
};

export default faveInstructorHas;
