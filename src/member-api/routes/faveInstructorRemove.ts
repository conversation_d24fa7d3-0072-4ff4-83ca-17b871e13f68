import { FaveService } from "@member-api/services/fave-service";
import { toBy } from "@shared/request-parsers";
import { RequestHand<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";

const faveService = new FaveService();

const faveInstructorRemove: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const instrId = req.params.instructorId!;
  const ret = await faveService.removeFaveInstructor({
    userId: uid,
    instructorId: instrId,
    by: toBy(idToken),
  });
  res.status(200).json();
};

export default faveInstructorRemove;
