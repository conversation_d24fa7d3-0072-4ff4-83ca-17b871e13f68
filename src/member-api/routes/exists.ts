import { Request<PERSON>and<PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { MemberService } from "../services/member-service";

const memberService = new MemberService();

const exists: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;

  const ret = await memberService.findByFirebaseUid(uid);
  if (!ret || ret.deleted) {
    res.status(204).end(); // no content
  } else {
    res.status(200).end();
  }
};

export default exists;
