import { FcmService } from "@fcm/services/fcm-service";
import { RequestHandler } from "express";
import { getIdToken } from "../../init-openapi";

const fcmService = new FcmService();

const fcmTest: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const { title, body, go } = req.body;

  await fcmService.scheduleTestNotifyFcmMessage(idToken, uid, title, body, go);
  res.status(200).json();
};

export default fcmTest;
