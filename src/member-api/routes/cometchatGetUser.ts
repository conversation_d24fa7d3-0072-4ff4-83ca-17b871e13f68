import { Request<PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { getCometChatUser } from "@utils/cometchat";

const cometchatGetUser: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const uid = idToken.uid;
  const result = await getCometChatUser(uid);
  if (!result.success) {
    res.status(404).json(result);
    return;
  }
  res.status(200).json(result);
};

export default cometchatGetUser; 