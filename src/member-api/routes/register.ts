import { ServiceError } from "@shared/services";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getIdToken } from "../../init-openapi";
import { MemberService } from "../services/member-service";
import { toMemberSaveReq } from "./shared/request-parsers";
import { createCometChatUser } from "@utils/cometchat";
import logger from "@utils/logger";
import { CometChatUser } from "@public-api/models/cometchat";

const memberService = new MemberService();

const register: RequestHandler = async (req, res) => {
  const idToken = getIdToken(req);
  const body = req.body;

  const toSave = toMemberSaveReq(body, idToken);
  toSave.member.id = idToken.uid; // (!) important
  try {
    await memberService.create(toSave);
    // CometChat integration (non-blocking)
    try {
      const result = await createCometChatUser({
        uid: toSave.member.firebaseUid,
        email: toSave.member.firebaseEmail,
        name: toSave.member.fullName,
        phone: toSave.member.mobileNo?.countryCode + toSave.member.mobileNo?.number,
        tags: ["member"],
        userType: "member",
      } as CometChatUser);

      if (!result.success) {
        logger.warn(`[CometChat] Member user creation failed for userId=${toSave.member.id}: ${result.message}`);
      }
    } catch (e) {
      logger.warn(`[CometChat] Member user creation failed for userId=${toSave.member.id}: ${e}`);
    }
    res.status(200).end();
  } catch (error) {
    if (error instanceof ServiceError) {
      if (error.code === "409") {
        res.status(409).end();
      } else {
        res.status(500).end();
      }
    } else {
      res.status(500).end();
    }
  }
};

export default register;
