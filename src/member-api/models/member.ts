import { By } from "@shared/models";

export interface Member {
  id?: string;
  firebaseUid: string;
  firebaseEmail: string;
  fullName: string;
  displayName: string;
  gender: string;
  mobileNo?: ContactNo;
  emergencyContact?: Contact;
  timezone?: string;
  countryCode?: string;
  registeredAt: string;
  deleted?: boolean;
  requestDelete?: boolean,
  credit?: Credit,
}

export interface Contact {
  name: string;
  contactNo: ContactNo;
}

export interface ContactNo {
  countryCode: string;
  number: string;
}

export interface MemberSaveReq {
  member: Member,
  by: By,
}

export interface MemberRequestDeleteReq {
  id?: string;
  firebaseUid: string,
  firebaseEmail: string,
  by: By,
}

export interface RequestDelete {
  id?: string;
  firebaseUid: string;
  firebaseEmail: string;
  completed: boolean
}

export interface Credit {
  memberId: string;
  currentCredits: number;
  creditExpiredDate: string;
  notifiedExpired: boolean;
}

export type SmsOtpLogAction = 'send' | 'verify';

export interface SmsOtpLog {
  id?: number;
  userId: string;
  phoneNumber: string;
  action: SmsOtpLogAction;
  status: string;
  twilioSid?: string;
  code?: string;
  error?: string;
  rawData?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface SmsOtpSendReq {
  by: By;
  phoneNumber: string;
  action: SmsOtpLogAction;
  status: string;
  twilioSid?: string;
  error?: string;
  rawData?: string;
}

export interface SmsOtpVerifyReq {
  by: By;
  phoneNumber: string;
  code: string;
  action: SmsOtpLogAction;
  status: string;
  twilioSid?: string;
  error?: string;
  rawData?: string;
}

