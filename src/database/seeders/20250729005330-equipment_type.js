'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */
    const equipmentTypes = [
      { id: 't01', name: 'Reformer', isDeleted: 0, sort: 1 },
      { id: 't02', name: 'Reformer and Tower', isDeleted: 0, sort: 2 },
      { id: 't03', name: 'Pilates Chair', isDeleted: 0, sort: 3 },
      { id: 't04', name: 'Cadillac Table', isDeleted: 0, sort: 4 },
      { id: 't05', name: 'Ladder Barrel', isDeleted: 0, sort: 5 },
      { id: 't06', name: 'Arc Barrel', isDeleted: 0, sort: 6 },
      { id: 't07', name: '<PERSON><PERSON> Corrector', isDeleted: 0, sort: 7 },
      { id: 't08', name: 'Floor Mat', isDeleted: 1, sort: 8 },
      { id: 't09', name: 'Others', isDeleted: 1, sort: 9 },
      { id: 't10', name: 'Studio Rental', isDeleted: 0, sort: 10 },
    ];

    for (const equipmentType of equipmentTypes) {
      await queryInterface.sequelize.query(`
        INSERT INTO stu03equip_types (id, name, sort, deleted, deleted_at, deleted_by, created_at, created_by, updated_at, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, NOW(), 'sequelize', NOW(), 'sequelize')
        ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          sort = VALUES(sort),
          deleted = VALUES(deleted),
          deleted_at = VALUES(deleted_at),
          deleted_by = VALUES(deleted_by),
          updated_at = VALUES(updated_at),
          updated_by = VALUES(updated_by)
      `, {
        replacements: [
          equipmentType.id,
          equipmentType.name,
          equipmentType.sort,
          equipmentType.isDeleted,
          equipmentType.isDeleted === 1 ? new Date() : null,
          equipmentType.isDeleted === 1 ? 'sequelize' : null
        ]
      });
    }
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  }
};
