'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create or replace the evt01booking_details view
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE VIEW evt01booking_details AS
      SELECT
        m.display_name as member_display_name,
        m.full_name as member_full_name,
        s.name as studio_name,
        et.name as equip_name,
        e.code as equip_code,
        i.name as instr_name,
        b.*,
        CASE
          WHEN cr.id IS NOT NULL AND NOT cr.approved THEN 'pendingCancel'
          WHEN b.cancelled = 1 AND b.cancelled_with_refund = 1 THEN 'refunded'
          WHEN b.cancelled = 1 THEN 'cancelled'
          WHEN start_at > NOW() THEN 'upcoming'
          WHEN start_at <= NOW() AND end_at >= NOW() THEN 'ongoing'
          WHEN end_at < NOW() THEN 'ended'
          ELSE 'upcoming'
        END as status,
        cr.id as cancel_req_id,
        cr.req_by as cancel_req_by,
        cr.user_id as cancel_req_user_id,
        cr.free as cancel_req_free,
        cr.reason as cancel_req_reason,
        cr.approved as cancel_req_approved,
        cr.approved_at as cancel_req_approved_at,
        cr.approved_by as cancel_req_approved_by,
        cr.at as cancel_req_at
      FROM evt01events b
      INNER JOIN usr01members m ON (m.id = b.member_id)
      INNER JOIN pub01studios s ON (s.id = b.studio_id)
      INNER JOIN stu04equipment e ON (e.id = b.equip_id)
      INNER JOIN stu03equip_types et ON (et.id = e.type_id)
      LEFT JOIN pub02instructors i ON (i.id = b.instr_id)
      LEFT JOIN evt03cancel_req cr ON (cr.event_id = b.id AND cr.approved = 0 AND cr.deleted = 0 AND cr.req_by IN ('Studio', 'Instructor'))
      WHERE b.type = 'Booking' AND b.deleted = 0;
    `);
  },

  async down(queryInterface, Sequelize) {
    // Drop the view if it exists
    await queryInterface.sequelize.query(`
      DROP VIEW IF EXISTS evt01booking_details;
    `);
  }
};
