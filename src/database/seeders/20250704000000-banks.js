'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const banks = [
      {
        name: 'Maybank',
        code: 'MAYBANK',
        countryCode: 'MY',
      },
      {
        name: 'CIMB Bank',
        code: 'CIMB',
        countryCode: 'MY',
      },
      {
        name: 'Public Bank',
        code: 'PBBANK',
        countryCode: 'MY',
      },
      {
        name: 'RHB Bank',
        code: 'RHB',
        countryCode: 'MY',
      },
      {
        name: 'Hong Leong Bank',
        code: 'HLBANK',
        countryCode: 'MY',
      },
      {
        name: 'AmBank',
        code: 'AMBANK',
        countryCode: 'MY',
      },
      {
        name: 'Alliance Bank',
        code: 'ALLIANCE',
        countryCode: 'MY',
      },
      {
        name: 'Affin Bank',
        code: 'AFFIN',
        countryCode: 'MY',
      },
      {
        name: 'Bank Islam Malaysia',
        code: 'BIMB',
        countryCode: 'MY',
      },
      {
        name: 'Bank Rakyat',
        code: 'BANKRAKYAT',
        countryCode: 'MY',
      },
      {
        name: 'DBS Bank',
        code: 'DBS',
        countryCode: 'SG',
      },
      {
        name: 'OCBC Bank',
        code: 'OCBC_SG',
        countryCode: 'SG',
      },
      {
        name: 'UOB Bank',
        code: 'UOB_SG',
        countryCode: 'SG',
      },
      {
        name: 'Standard Chartered Bank',
        code: 'SCB_SG',
        countryCode: 'SG',
      },
      {
        name: 'Citibank Singapore',
        code: 'CITI_SG',
        countryCode: 'SG',
      },
      {
        name: 'HSBC Singapore',
        code: 'HSBC_SG',
        countryCode: 'SG',
      },
      {
        name: 'Bank of China Singapore',
        code: 'BOC_SG',
        countryCode: 'SG',
      },
      {
        name: 'Maybank Singapore',
        code: 'MAYBANK_SG',
        countryCode: 'SG',
      },
      {
        name: 'CIMB Singapore',
        code: 'CIMB_SG',
        countryCode: 'SG',
      },
      {
        name: 'RHB Singapore',
        code: 'RHB_SG',
        countryCode: 'SG',
      }
    ];

    for (const bank of banks) {
      await queryInterface.sequelize.query(`
        INSERT INTO trx01banks (id, name, code, country_code)
        VALUES (UUID(), ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          name = VALUES(name),
          country_code = VALUES(country_code)
      `, {
        replacements: [bank.name, bank.code, bank.countryCode]
      });
    }
  },

  async down(queryInterface, Sequelize) { }
};
