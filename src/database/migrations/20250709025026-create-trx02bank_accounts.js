'use strict';

const { DataTypes } = require('sequelize');
const { addBaseColumns } = require('./helpers/base-columns');

const BANK_ACCOUNT_STATUSES = { ACTIVE: 1, INACTIVE: 0 };

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('trx02bank_accounts');
  }
};

const createTable = async (queryInterface, Sequelize) => {
  const columns = addBaseColumns({
    entity_type: {
      type: Sequelize.ENUM('platform', 'studio', 'instructor'),
      allowNull: false,
    },
    entity_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    bank_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'trx01banks',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT'
    },
    account_name: {
      type: Sequelize.STRING(100),
      allowNull: false
    },
    account_no: {
      type: Sequelize.STRING(100),
      allowNull: false
    },
    swift_code: {
      type: Sequelize.STRING(100),
      allowNull: true
    },
    primary: {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    },
    status: {
      type: Sequelize.TINYINT,
      defaultValue: BANK_ACCOUNT_STATUSES.ACTIVE,
      comment: '0: inactive, 1: active'
    }
  });

  await queryInterface.createTable('trx02bank_accounts', columns);
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('trx02bank_accounts', {
    type: 'unique',
    fields: ['entity_type', 'entity_id', 'bank_id', 'account_no'],
    name: 'unq_trx01bank_accounts_entity_bank_account_no'
  });
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('trx02bank_accounts', ['entity_type', 'entity_id', 'status'], {
    name: 'idx_trx01bank_accounts_entity_type_entity_id_status'
  });
  await queryInterface.addIndex('trx02bank_accounts', ['entity_type', 'entity_id', 'primary'], {
    name: 'idx_trx01bank_accounts_entity_type_entity_id_primary'
  });
};
