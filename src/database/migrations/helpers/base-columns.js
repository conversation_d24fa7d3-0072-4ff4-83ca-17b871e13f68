'use strict';

const { DataTypes, Sequelize } = require('sequelize');

const DEFAULT_OPTIONS = {
  withTimestamps: true,
  withAudit: true,
  withParanoid: false,
};

const addBaseColumns = (columns, opts = {}) => {
  const OPTIONS = { ...DEFAULT_OPTIONS, ...opts };

  const OTHERS = {};

  // timestamps
  if (OPTIONS.withTimestamps) {
    Object.assign(OTHERS, {
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      },
    });
  }

  // paranoid (soft deletes)
  if (OPTIONS.withParanoid) {
    Object.assign(OTHERS, {
      deleted_at: { type: DataTypes.DATE, allowNull: true },
      deleted_by: { type: DataTypes.UUID, allowNull: true },
    });
  }

  // audit
  if (OPTIONS.withAudit) {
    Object.assign(OTHERS, {
      created_by: { type: DataTypes.UUID, allowNull: true },
      updated_by: { type: DataTypes.UUID, allowNull: true },
    });
  }

  return {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    }, ...columns, ...OTHERS
  };
};

module.exports = {
  addBaseColumns,
};
