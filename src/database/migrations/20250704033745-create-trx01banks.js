'use strict';

const { addBaseColumns } = require('./helpers/base-columns');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('trx01banks');
  }
};

const createTable = async (queryInterface, Sequelize) => {
  const columns = addBaseColumns({
    name: {
      type: Sequelize.STRING(100),
      allowNull: false
    },
    code: {
      type: Sequelize.STRING(15),
      unique: true,
      allowNull: false,
    },
    country_code: {
      type: Sequelize.STRING(2),
      allowNull: false,
    },
    status: {
      type: Sequelize.TINYINT,
      defaultValue: 1,
      comment: '0: inactive, 1: active'
    },
  });

  await queryInterface.createTable('trx01banks', columns);
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('trx01banks', ['country_code', 'status'], {
    name: 'idx_trx01banks_country_code_status'
  });
};