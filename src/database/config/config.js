require('dotenv').config();

module.exports = {
  development: {
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME,
    host: process.env.DATABASE_HOST,
    port: process.env.DATABASE_PORT || 3306,
    dialect: 'mysql',
    timezone: process.env.DATABASE_TIMEZONE,
    dialectOptions: {
      timezone: process.env.DATABASE_TIMEZONE
    },
    logging: console.log
  },
  test: {
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME,
    host: process.env.DATABASE_HOST,
    port: process.env.DATABASE_PORT || 3306,
    dialect: 'mysql',
    timezone: process.env.DATABASE_TIMEZONE,
    dialectOptions: {
      timezone: process.env.DATABASE_TIMEZONE
    },
    logging: false
  },
  production: {
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME,
    host: process.env.DATABASE_HOST,
    port: process.env.DATABASE_PORT || 3306,
    dialect: 'mysql',
    timezone: process.env.DATABASE_TIMEZONE,
    dialectOptions: {
      timezone: process.env.DATABASE_TIMEZONE
    },
    logging: false
  }
};
