import { Sequelize } from 'sequelize';

const defineAssociations = (sequelize: Sequelize) => {
  const { Bank, Country, BankAccount } = sequelize.models;

  if (Bank && Country) {
    Bank.belongsTo(Country, {
      foreignKey: 'countryCode',
      targetKey: 'code',
      as: 'country'
    });

    Country.hasMany(Bank, {
      foreignKey: 'countryCode',
      sourceKey: 'code',
      as: 'banks'
    });
  }

  if (Bank && BankAccount) {
    Bank.hasMany(BankAccount, {
      foreignKey: 'bankId',
      sourceKey: 'id',
      as: 'bankAccounts'
    });

    BankAccount.belongsTo(Bank, {
      foreignKey: 'bankId',
      targetKey: 'id',
      as: 'bank'
    });
  }
};

export default defineAssociations;
