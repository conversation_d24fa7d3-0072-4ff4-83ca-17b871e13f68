import { Model, DataTypes, Sequelize } from 'sequelize';

import { formatDate } from "date-fns";
import { StatusValue } from "@shared/constants";
import { Country } from '@database/models/country';
import { applyAuditFields } from '@mixins/audit-fields';

export interface Bank {
  id: string;
  name: string;
  code: string;
  countryCode: string;
  status: StatusValue;
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
}

export interface BankFind extends Partial<Pick<Bank, 'countryCode' | 'status'>> { }

export class BankModel extends Model<Bank> {
  public id!: string;
  public name!: string;
  public code!: string;
  public countryCode!: string;
  public status!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
  public created_by?: string;
  public updated_by?: string;

  public readonly country?: Country;
}

export interface BankOption {
  value: Bank['id'];
  label: Bank['name'];
}

export default (sequelize: Sequelize) => {
  BankModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      code: {
        type: DataTypes.STRING(15),
        allowNull: false,
        unique: true,
      },
      countryCode: {
        type: DataTypes.STRING(2),
        allowNull: false,
      },
      status: {
        type: DataTypes.TINYINT,
        defaultValue: 1,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: true,
        get() {
          const rawValue = this.getDataValue('createdAt');
          return rawValue ? formatDate(rawValue, 'yyyy-MM-dd HH:mm:ss') : null;
        }
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        get() {
          const rawValue = this.getDataValue('updatedAt');
          return rawValue ? formatDate(rawValue, 'yyyy-MM-dd HH:mm:ss') : null;
        }
      },
    },
    {
      sequelize,
      modelName: 'Bank',
      tableName: 'trx01banks',
      timestamps: true,
    }
  );

  applyAuditFields(BankModel);

  return BankModel;
};
