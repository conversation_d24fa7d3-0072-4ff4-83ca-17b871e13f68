import { readdirSync } from 'fs';
import { join } from 'path';
import { Sequelize, DataTypes } from 'sequelize';
import logger from '@utils/logger';

let sequelizeInstance: Sequelize | null = null;

export const loadModels = (sequelize: Sequelize) => {
  sequelizeInstance = sequelize;

  const modelsDir = __dirname;
  const modelFiles = readdirSync(modelsDir)
    .filter(file =>
      (file.endsWith('.js') || file.endsWith('.ts')) &&
      !file.endsWith('.d.ts') &&
      file !== 'index.js' &&
      file !== 'index.ts' &&
      !file.startsWith('_')
    );

  modelFiles.forEach(file => {
    try {
      const modelPath = join(modelsDir, file);

      const modelModule = require(modelPath);

      const modelDefinition = modelModule.default || modelModule;

      if (typeof modelDefinition === 'function') {
        const model = modelDefinition(sequelize, DataTypes);
        if (model && model.name) {
          sequelize.models[model.name] = model;
          logger.info(`Successfully loaded model: ${model.name}`);
        }
      }
    } catch (error) {
      console.error(`Failed to load model from ${file}:`, error);
    }
  });
};

export const getSequelize = () => sequelizeInstance;

export const getModels = () => sequelizeInstance?.models || {};
