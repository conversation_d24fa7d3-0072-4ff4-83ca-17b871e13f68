import { Model, DataTypes, Sequelize, Op } from 'sequelize';
import { formatDate } from "date-fns";

import { Bank, BankOption } from '@database/models/bank';
import { CountryOption } from '@database/models/country';
import { applyAuditFields } from '@mixins/audit-fields';
import { DbTransactionOptions, ENTITY_TYPES, STATUSES, StatusValue } from "@shared/constants";
import { ServiceError } from '@shared/services';

export interface BankAccount {
  id: string;
  entityType: BankAccountEntityTypeValue;
  entityId: string;
  bankId: string;
  accountNo: string;
  accountName: string;
  swiftCode: string;
  primary: boolean;
  status: StatusValue;
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: string;
  updatedBy?: string;

  isActive: boolean;
}

export interface BankAccountWithBank extends BankAccount {
  bank: Bank;
}

export interface BankAccountCreate extends Pick<BankAccount, 'entityType' | 'entityId' | 'bankId' | 'accountNo' | 'accountName' | 'primary' | 'swiftCode'> { }

export interface BankAccountUpdate extends Pick<BankAccount, 'bankId' | 'accountNo' | 'accountName' | 'primary'> { }

export interface BankAccountUpdateStatus extends Pick<BankAccount, 'status'> { }

export interface BankAccountFind extends Partial<Pick<BankAccount, 'bankId' | 'status' | 'entityType' | 'entityId'>> {
  searchTerm?: string;
}

export interface FormOption {
  bankOptions: BankOption[];
  countryOptions: CountryOption[];
}

export interface ListingOption {
  bankOptions: BankOption[];
}

export const BANK_ACCOUNT_ENTITY_TYPES = {
  PLATFORM: ENTITY_TYPES.PLATFORM,
  STUDIO: ENTITY_TYPES.STUDIO,
  INSTRUCTOR: ENTITY_TYPES.INSTRUCTOR,
} as const;

export type BankAccountEntityTypeValue = typeof BANK_ACCOUNT_ENTITY_TYPES[keyof typeof BANK_ACCOUNT_ENTITY_TYPES];

export const BANK_ACCOUNT_STATUSES = {
  ACTIVE: STATUSES.ACTIVE,
  INACTIVE: STATUSES.INACTIVE,
} as const;

export type BankAccountStatusValue = typeof BANK_ACCOUNT_STATUSES[keyof typeof BANK_ACCOUNT_STATUSES];

export class BankAccountModel extends Model<BankAccount> {
  public id!: string;
  public entityType!: BankAccountEntityTypeValue;
  public entityId!: string;
  public bankId!: string;
  public accountNo!: string;
  public accountName!: string;
  public swiftCode!: string;
  public primary!: boolean;
  public status!: StatusValue;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
  public createdBy?: string;
  public updatedBy?: string;

  public readonly bank?: Bank;
  public readonly isActive!: boolean;
}

export function toBankAccountCreateJson(bankAccount: BankAccount) {
  const { id, accountNo, accountName, bankId, primary, swiftCode } = bankAccount;
  return { id, accountNo, accountName, bankId, primary, swiftCode };
}

export function toBankAccountViewJson(bankAccount: BankAccountWithBank) {
  const { id, accountNo, accountName, bankId, bank, status, isActive, primary, swiftCode } = bankAccount;
  return {
    id,
    accountNo,
    accountName,
    bankId,
    bankCode: bank?.code ?? null,
    bankName: bank?.name ?? null,
    countryCode: bank?.countryCode ?? null,
    swiftCode,
    primary,
    status,
    isActive,
  };
}

export default (sequelize: Sequelize) => {
  BankAccountModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
        primaryKey: true,
      },
      entityType: {
        type: DataTypes.ENUM(...Object.values(ENTITY_TYPES)),
        allowNull: false,
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      bankId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'trx01banks',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      accountNo: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      accountName: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      swiftCode: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      primary: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      status: {
        type: DataTypes.TINYINT,
        defaultValue: STATUSES.ACTIVE,
      },
      isActive: {
        type: DataTypes.VIRTUAL,
        get() {
          return this.getDataValue('status') == STATUSES.ACTIVE;
        }
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: true,
        get() {
          const rawValue = this.getDataValue('createdAt');
          return rawValue ? formatDate(rawValue, 'yyyy-MM-dd HH:mm:ss') : null;
        }
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        get() {
          const rawValue = this.getDataValue('updatedAt');
          return rawValue ? formatDate(rawValue, 'yyyy-MM-dd HH:mm:ss') : null;
        }
      },
    },
    {
      sequelize,
      modelName: 'BankAccount',
      tableName: 'trx02bank_accounts',
      timestamps: true,
    }
  );

  const updatePrimary = async (bankAccount: BankAccountModel, options: DbTransactionOptions) => {
    const { entityType, entityId, primary } = bankAccount;
    const { transaction } = options;
    if (primary) {
      if (bankAccount.status.toString() !== STATUSES.ACTIVE) {
        throw new ServiceError("400", "Primary bank account must be active");
      }
      await BankAccountModel.update({ primary: false }, { where: { entityType, entityId, primary: true }, transaction });
    } else {
      const count = await BankAccountModel.count({
        where: {
          entityType,
          entityId,
          primary: true,
          id: { [Op.ne]: bankAccount.id }
        }, transaction
      });
      if (count === 0) {
        throw new ServiceError("400", "At least one primary bank account is required");
      }
    }
  };

  BankAccountModel.beforeCreate(async (bankAccount, options) => {
    await updatePrimary(bankAccount, options);
  });

  BankAccountModel.beforeUpdate(async (bankAccount, options) => {
    const { primary, status } = bankAccount;

    if (bankAccount.changed('primary')) {
      await updatePrimary(bankAccount, options);
    }
    if (bankAccount.changed('status')) {
      if (status.toString() === STATUSES.INACTIVE && primary) {
        throw new ServiceError("400", "Primary bank account cannot be deactivated");
      }
    }
  });

  applyAuditFields(BankAccountModel);

  return BankAccountModel;
};
