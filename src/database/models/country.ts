import { Model, DataTypes, Sequelize } from 'sequelize';

export interface Country {
  code: string;
  name: string;
  timezone: string;
  currency: string;
  currency_name: string;
  symbol: string;
}

export interface CountryFind extends Partial<Pick<Country, 'code' | 'name' | 'currency'>> { }

export interface CountryOption {
  value: Country['code'];
  label: Country['name'];
}

export interface CurrencyOption {
  value: Country['currency'];
  label: Country['currency_name'];
}

export class CountryModel extends Model<Country> {
  public code!: string;
  public name!: string;
  public timezone!: string;
  public currency!: string;
  public currency_name!: string;
  public symbol!: string;

  // Association properties
  public readonly banks?: any[];
}

export default (sequelize: Sequelize) => {
  CountryModel.init(
    {
      code: {
        type: DataTypes.STRING(2),
        allowNull: false,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(64),
        allowNull: false,
      },
      timezone: {
        type: DataTypes.STRING(64),
        allowNull: false,
      },
      currency: {
        type: DataTypes.STRING(3),
        allowNull: false,
      },
      currency_name: {
        type: DataTypes.STRING(32),
        allowNull: false,
      },
      symbol: {
        type: DataTypes.STRING(8),
        allowNull: false,
      },
    },
    {
      sequelize,
      modelName: 'Country',
      tableName: 'app07country_info',
      timestamps: false,
    }
  );

  return CountryModel;
};
