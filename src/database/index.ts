import { Sequelize } from 'sequelize';
import logger from '@utils/logger';

const sequelize = new Sequelize({
    dialect: 'mysql',
    host: process.env.DATABASE_HOST,
    port: +process.env.DATABASE_PORT || 3306,
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME,
    timezone: process.env.DATABASE_TIMEZONE,
    dialectOptions: {
        timezone: process.env.DATABASE_TIMEZONE
    },
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
        max: +process.env.DATABASE_POOL_CONNECTION_LIMIT || 10,
        min: 0,
        acquire: 30000,
        idle: 10000
    }
});

// Test the connection
export const testConnection = async (): Promise<void> => {
    try {
        await sequelize.authenticate();
        logger.info('Sequelize database connection has been established successfully.');
    } catch (error) {
        logger.error('Unable to connect to the database:', error);
        throw error;
    }
};

export default sequelize;
