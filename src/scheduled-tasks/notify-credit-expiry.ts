import 'dotenv/config';
import { FcmService } from "@fcm/services/fcm-service";
import { CreditExpiryNotify } from "@scheduled-tasks/notify/models/notify";
import { NotifyService } from "@scheduled-tasks/notify/services/notify-service";
import logger from "@utils/logger";

const by = "NotifyCreditExpiry";
const intervalInMillis = 15 * 1_000;

export class NotifyCreditExpiry {
    private timeoutId: NodeJS.Timeout|undefined;
    private fcmService = new FcmService();
    private notifyService = new NotifyService();

    async start(): Promise<void> {
        logger.debug("start()");
        this.timeoutId = setTimeout(() => this.execute(), 1_000);
    }

    async stop(): Promise<void> {
        logger.debug("stop()");
        if (!!this.timeoutId) {
            clearTimeout(this.timeoutId);
        }
    }

    private async execute() {
        try {
            const toNotify = await this.notifyService.findExpiredCreditsToNotify();
            for (const notify of toNotify) {
                await this.notify(notify);
                await this.notifyService.setNotifiedExpired({
                    memberId: notify.memberId,
                    by: by
                });
            }
        } catch (e) {
            logger.warn(`Exception @ execute()! exception=${JSON.stringify(e)}`);
        } finally {
            this.timeoutId = setTimeout(() => this.execute(), intervalInMillis);
        }
    }

    private async notify(notify: CreditExpiryNotify) {
        try {
            const token = await this.fcmService.findTokenByUserId(notify.memberId);
            if (token) {
                await this.fcmService.scheduleCreditExpiredNotifyFcmMessage(by, notify.memberId);
            }
        } catch (e) {
            logger.warn(`Exception @ notify()! exception=${JSON.stringify(e)}`);
        }
    }
} 