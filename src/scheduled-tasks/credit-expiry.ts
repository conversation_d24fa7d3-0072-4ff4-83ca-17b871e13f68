import 'dotenv/config';
import logger from "@utils/logger";
import { CreditService } from "@booking-api/services/credit-service";
import { FcmService } from "@fcm/services/fcm-service";
import crypto from 'crypto';

const intervalInMillis = 60 * 60 * 1_000; // Check every hour
const by = "CreditExpiryScheduler";

export class CreditExpiry {
    private timeoutId: NodeJS.Timeout|undefined;
    private creditService = new CreditService();
    private fcmService = new FcmService();

    async start(): Promise<void> {
        logger.debug("start()");
        this.timeoutId = setTimeout(() => this.execute(), 1_000);
    }

    async stop(): Promise<void> {
        logger.debug("stop()");
        if (!!this.timeoutId) {
            clearTimeout(this.timeoutId);
        }
    }

    private async execute() {
        try {
            // Process expired credits and get affected member IDs
            const expiredMemberIds = await this.creditService.processExpiredCredits(by);

            // Send FCM notifications to each affected member
            for (const memberId of expiredMemberIds) {
                try {
                    await this.fcmService.scheduleCreditExpiredNotifyFcmMessage(by, memberId);
                    logger.info(`[${by}] Sent expiry notification to member ${memberId}`);
                } catch (e) {
                    logger.error(`[${by}] Failed to send expiry notification to member ${memberId}: ${e}`);
                }
            }
        } catch (e) {
            logger.warn(`Exception @ execute()! exception=${e}`);
        }
        this.timeoutId = setTimeout(() => this.execute(), intervalInMillis);
    }
}