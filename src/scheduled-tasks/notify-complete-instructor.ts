import 'dotenv/config';
import { FcmService } from "@fcm/services/fcm-service";
import { NotifyInstructor, SetInstructorNotifiedCompleteReq } from "@scheduled-tasks/notify/models/notify";
import { NotifyService } from "@scheduled-tasks/notify/services/notify-service";
import logger from "@utils/logger";

const by = "NotifyInstructorProfileComplete";
const intervalInMillis = 15 * 1_000;

// Notify instructors to complete profile
export class NotifyProfileComplete {

  private timeoutId: NodeJS.Timeout|undefined;
  private notifyService = new NotifyService();
  private fcmService = new FcmService();

  async start(): Promise<void> {
    logger.debug("start()");
    this.timeoutId = setTimeout(() => this.execute(), 1_000);
  }

  async stop(): Promise<void> {
    logger.debug("stop()");
    if (!!this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  private async execute() {
    try {
      // First Time Notify After Session End
      const toNotify = await this.notifyService.findInstructorNotifyComplete();
      for (const notify of toNotify) {
        // not feedback yet, notify
        await this.notify(notify);
        // set notified feedback
        await this.notifyService.setInstructorNotifiedComplete({
          instrId: notify.id,
          by: by,
        } as SetInstructorNotifiedCompleteReq);
      }

    } catch (e) {
      logger.warn(`Exception @ execute()! exception=${JSON.stringify(e)}`);
    } finally {
      this.timeoutId = setTimeout(() => this.execute(), intervalInMillis);
    }
  }

  private async notify(notify: NotifyInstructor) {
    try {
        const token = await this.fcmService.findTokenByUserId(notify.id);
        if (token) {
          await this.fcmService.scheduleInstructorProfileCompleteReminderFcmMessage(by, notify.id, 1);
        }
    } catch (e) {
      logger.warn(`Exception @ notify()! exception=${JSON.stringify(e)}`);
    }
  }
}
