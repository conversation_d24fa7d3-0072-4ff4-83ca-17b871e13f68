import { CreditExpiryNotify, NotifyInstructor, NotifyModel, SetCreditExpiryNotifiedReq, SetInstructorNotifiedCompleteReq, SetNotifiedCompleteReq, SetNotifiedFeedbackReq } from "@scheduled-tasks/notify/models/notify";
import { NotifyRepo } from "@scheduled-tasks/notify/repo/notify-repo";
import pool from "../../../init-pool";

export class NotifyService {

  private notifyRepo = new NotifyRepo();

  async findNotifyComplete(): Promise<NotifyModel[]> {
    return this.notifyRepo.findNotifyComplete(pool);
  }

  async findNotifyFeedback(): Promise<NotifyModel[]> {
    return this.notifyRepo.findNotifyFeedback(pool);
  }

  async findInstructorNotifyComplete(): Promise<NotifyInstructor[]> {
    return this.notifyRepo.findInstructorNotifyComplete(pool);
  }

  async setNotifiedComplete(req: SetNotifiedCompleteReq): Promise<number> {
    return this.notifyRepo.setNotifiedComplete(pool, req);
  }

  async setNotifiedFeedback(req: SetNotifiedFeedbackReq): Promise<number> {
    return this.notifyRepo.setNotifiedFeedback(pool, req);
  }

  async setInstructorNotifiedComplete(req: SetInstructorNotifiedCompleteReq): Promise<number> {
    return this.notifyRepo.setInstructorNotifiedComplete(pool, req);
  }

  async isComplete(bookingId: string): Promise<boolean|undefined> {
    return this.notifyRepo.isComplete(pool, bookingId);
  }

  async hasFeedback(bookingId: string): Promise<boolean|undefined> {
    return this.notifyRepo.hasFeedback(pool, bookingId);
  }

  async findExpiredCreditsToNotify(): Promise<CreditExpiryNotify[]> {
    return this.notifyRepo.findExpiredCreditsToNotify(pool);
}

  async setNotifiedExpired(req: SetCreditExpiryNotifiedReq): Promise<number> {
      return this.notifyRepo.setNotifiedExpired(pool, req);
  }
}
