import { BookingStatus, BookingType } from "@booking-api/models/booking";
import {
  NotifyEquipment,
  NotifyInstructor,
  NotifyMember,
  NotifyModel,
  NotifyStudio,
  SetNotifiedCompleteReq,
  SetNotifiedFeedbackReq,
  SetInstructorNotifiedCompleteReq,
  SetCreditExpiryNotifiedReq,
  CreditExpiryNotify
} from "@scheduled-tasks/notify/models/notify";
import logger from "@utils/logger";
import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";

export class NotifyRepo {

  async findNotifyComplete(
    conn: Connection
  ): Promise<NotifyModel[]> {
    const clauses = [];
    const params = [];
    let sql = `select * from evt01events`;

    {
      clauses.push("(`notified_complete` = false)");
      clauses.push(`(exists (
          select es.event_id from evt02sessions es
          where completed is false and es.event_id = id
        ))`);
    }
    {
      clauses.push("(`instr_id` is not null)");
      clauses.push("(`type` = ?)");
      params.push(BookingType[BookingType.Booking]);
      clauses.push("(`booking_status` = ?)");
      params.push(BookingStatus[BookingStatus.Confirmed]);
      clauses.push("(`cancelled` is false)");
      clauses.push("(`deleted` is false)");
      clauses.push("(CONVERT_TZ(`end_at`, `timezone`, 'UTC') <= date_format(subdate(NOW(), INTERVAL 15 MINUTE), '%Y-%m-%d %H:%i'))");
    }
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`
    }
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => this.toNotifyModel(o));
  }

  async findNotifyFeedback(
    conn: Connection
  ): Promise<NotifyModel[]> {
    const clauses = [];
    const params = [];
    let sql = `select * from evt01events`;

    clauses.push("(`notified_feedback` = false)");
    clauses.push(`(exists (
        select es.event_id from evt02sessions es
        where m_has_feedback is false and es.event_id = id
      ))`);

    clauses.push("(`type` = ?)");
    params.push(BookingType[BookingType.Booking]);
    clauses.push("(`booking_status` = ?)");
    params.push(BookingStatus[BookingStatus.Confirmed]);
    clauses.push("(`cancelled` is false)");
    clauses.push("(`deleted` is false)");
    clauses.push("(CONVERT_TZ(`end_at`, `timezone`, 'UTC') <= date_format(subdate(NOW(), INTERVAL 72 HOUR), '%Y-%m-%d %H:%i'))");
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`
    }
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => this.toNotifyModel(o));
  }

  private toNotifyModel(row: any): NotifyModel {
    const instr_id = row["instr_id"];
    return {
      bookingId: row["id"]!,
      studio: {
        id: row["studio_id"]!,
        name: row["studio_name"]!,
      } as NotifyStudio,
      ...instr_id && {instructor: { // (!) nullable instructor
        id: instr_id!,
        name: row["instr_name"]!,
      } as NotifyInstructor},
      member: {
        id: row["member_id"]!,
        displayName: row["member_display_name"]!,
        fullName: row["member_full_name"]!,
      } as NotifyMember,
      equipment: {
        id: row["equip_id"]!,
        name: row["equip_name"]!,
      } as NotifyEquipment,
    } as NotifyModel;
  }

  async setNotifiedComplete(
    conn: Connection, req: SetNotifiedCompleteReq,
  ): Promise<number> {
    const sql = `update evt01events
      set notified_complete = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by;
    logger.debug(`[${by}] setNotifiedComplete() req=${JSON.stringify(req)}`)
    const params = [
      true, // notified_complete
      by,   // updated_by
      req.bookingId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] setNotifiedComplete() bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async setNotifiedFeedback(
    conn: Connection, req: SetNotifiedFeedbackReq,
  ): Promise<number> {
    const sql = `update evt01events
      set notified_feedback = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by;
    logger.debug(`[${by}] setNotifiedFeedback() req=${JSON.stringify(req)}`)
    const params = [
      true, // notified_feedback
      by,   // updated_by
      req.bookingId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] setNotifiedFeedback() bookingId=${req.bookingId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async isComplete(
    conn: Connection, bookingId: string
  ): Promise<boolean|undefined> {
    let sql = `select completed as ret from evt02sessions where event_id = ?`;
    const params = [bookingId!];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.length === 0 ? undefined : results[0]["ret"] === 1;
  }

  async hasFeedback(
    conn: Connection, bookingId: string
  ): Promise<boolean|undefined> {
    let sql = `select m_has_feedback as ret from evt02sessions where event_id = ?`;
    const params = [bookingId!];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.length === 0 ? undefined : results[0]["ret"] === 1;
  }

  ////////////////
  // Instructor //
  ////////////////
  async findInstructorNotifyComplete(
    conn: Connection
  ): Promise<NotifyInstructor[]> {
    const clauses = [];
    const params = [];
    let sql = `select * from instr01instructors`;

    clauses.push("(`notified_complete` is false)");
    clauses.push("(`is_complete` is false)");
    clauses.push("(`deleted` is false)");
    clauses.push("(`created_at` <= date_format(subdate(NOW(), INTERVAL 72 HOUR), '%Y-%m-%d %H:%i'))");

    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`
    }
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => this.toNotifyInstructorModel(o));
  }

  private toNotifyInstructorModel(row: any): NotifyInstructor {
    return {
        id: row['id']!,
        name: row["name"]!,
    }
  }

  async setInstructorNotifiedComplete(
    conn: Connection, req: SetInstructorNotifiedCompleteReq,
  ): Promise<number> {
    const sql = `update instr01instructors
      set notified_complete = ?,
      updated_by = ?
      where id = ?`;
    const by = req.by;
    logger.debug(`[${by}] setInstructorNotifiedComplete() req=${JSON.stringify(req)}`)
    const params = [
      true, // notified_complete
      by,   // updated_by
      req.instrId,
    ];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    const affectedRows = results.affectedRows;
    logger.info(`[${by}] setInstructorNotifiedComplete() instrId=${req.instrId}, numRows=${affectedRows}`);
    return affectedRows;
  }

  async findExpiredCreditsToNotify(conn: Connection): Promise<CreditExpiryNotify[]> {
    const sql = `select * from usr05credit
        where credit_expired_date <= date_format(adddate(now(), INTERVAL 1 MONTH), '%Y-%m-%d %H:%i')
        and current_credits > 0
        and notified_expired = false`;
    const params = [];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(row => this.toCreditExpiryNotify(row));
  }

  async setNotifiedExpired(conn: Connection, req: SetCreditExpiryNotifiedReq): Promise<number> {
      const sql = `update usr05credit
          set
            notified_expired = true,
            updated_by = ?
          where member_id = ?`;
      const by = req.by;
      logger.debug(`[${by}] setNotifiedExpired() req=${JSON.stringify(req)}`);
      const params = [by, req.memberId];
      const [results] = await conn.execute<ResultSetHeader>(sql, params);
      const affectedRows = results.affectedRows;
      logger.info(`[${by}] setNotifiedExpired() memberId=${req.memberId}, numRows=${affectedRows}`);
      return affectedRows;
  }

  private toCreditExpiryNotify(row: any): CreditExpiryNotify {
      return {
          memberId: row['member_id'],
          currentCredits: row['current_credits'],
          creditExpiredDate: row['credit_expired_date'],
      };
  }
}
