export interface NotifyModel {
  bookingId: string;
  studio: NotifyStudio;
  instructor?: NotifyInstructor;
  member: NotifyMember;
  equipment: NotifyEquipment;
}

export interface NotifyStudio {
  id: string;
  name: string;
}

export interface NotifyInstructor {
  id: string;
  name: string;
}

export interface NotifyMember {
  id: string;
  displayName: string;
  fullName: string;
}

export interface NotifyEquipment {
  id: string;
  name: string;
}

export interface SetNotifiedCompleteReq {
  bookingId: string;
  by: string;
}

export interface SetNotifiedFeedbackReq {
  bookingId: string;
  by: string;
}

export interface SetInstructorNotifiedCompleteReq {
  instrId: string;
  by: string;
}
export interface CreditExpiryNotify {
    memberId: string;
    currentCredits: number;
    creditExpiredDate: string;
}

export interface SetCreditExpiryNotifiedReq {
    memberId: string;
    by: string;
}
