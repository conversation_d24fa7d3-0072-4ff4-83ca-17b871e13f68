import 'dotenv/config';
import { BookingService } from "@booking-api/services/booking-service";
import { CreditService } from "@booking-api/services/credit-service";
import logger from "@utils/logger";

const intervalInMillis = 60 * 1_000;

export class ReleaseHold {

  private timeoutId: NodeJS.Timeout|undefined;
  private bookingService = new BookingService();
  private creditService = new CreditService();

  async start(): Promise<void> {
    logger.debug("start()");
    this.timeoutId = setTimeout(() => this.execute(), 1_000);
  }

  async stop(): Promise<void> {
    logger.debug("stop()");
    if (!!this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  private async execute() {
    try {
      await this.bookingService.releaseHold();
      await this.creditService.cleanup();
    } catch (e) {
      logger.warn(`Exception @ execute()! exception=${e}`);
    }
    this.timeoutId = setTimeout(() => this.execute(), intervalInMillis);
  }
}
