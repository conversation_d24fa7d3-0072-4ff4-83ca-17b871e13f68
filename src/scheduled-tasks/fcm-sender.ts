import 'dotenv/config';
import { FcmConfig } from "@fcm/models/fcm-config";
import {
  FcmMessage,
  FcmMessageFindCriteria,
  FcmMessageRescheduleReq,
  FcmMessageStatus,
  FcmMessageStatusSetReq
} from "@fcm/models/fcm-message";
import { FcmService } from "@fcm/services/fcm-service";
import { MemberService } from "@member-api/services/member-service";
import { DateTime } from 'luxon';
import { Page } from "@shared/services";
import logger from "@utils/logger";
import { addDays, format, isValid, parse } from "date-fns";
import { messaging } from "firebase-admin";
import { Fcm } from "../init-firebase";
import Message = messaging.Message;
import { Member } from '@member-api/models/member';

const by = "FcmSender";
const intervalInMillis = 15 * 1_000;
const maxAttempts = 3;

export class FcmSender {

  private timeoutId: NodeJS.Timeout|undefined;
  private fcmService = new FcmService();
  private memberService = new MemberService();

  async start(): Promise<void> {
    logger.debug("start()");
    this.timeoutId = setTimeout(() => this.execute(), 250);
  }

  async stop(): Promise<void> {
    logger.debug("stop()");
    if (!!this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  private async execute() {
    try {
      const messages = await this.getMessages();
      if (messages.length === 0) {
        this.timeoutId = setTimeout(() => this.execute(), intervalInMillis);
      } else {
        await this.handleMessages(messages);
        this.timeoutId = setTimeout(() => this.execute(), 250);
      }
    } catch (e) {
      logger.warn(`Exception @ execute()! exception=${e}`);
      this.timeoutId = setTimeout(() => this.execute(), intervalInMillis);
    }
  }

  private async handleMessages(messages: FcmMessage[]) {
    for (const message of messages) {
      const config = await this.fcmService.findFcmConfigByUserId(message.userId);
      const member = await this.memberService.findByFirebaseUid(message.userId);
      if (await this.isWithinBlackout(message, config, member)) {
        await this.rescheduleMessage(message, config, member);
      } else {
        await this.sendMessage(message);
      }
    }
  }

  private async sendMessage(message: FcmMessage) {
    const token = await this.fcmService.findTokenByUserId(message.userId);

    if (!token || !token.token.trim()) {
      // (!) no token
      await this.fcmService.setStatus({
        messageId: message.id,
        userId: message.userId,
        status: FcmMessageStatus.Failed,
        by: by,
      } as FcmMessageStatusSetReq);
    }

    try {
      // Set status to sending, increment attempt
      await this.fcmService.setStatus({
        messageId: message.id,
        userId: message.userId,
        status: FcmMessageStatus.Sending,
        incrementAttempt: true, // (!) incrementAttempt
        by: by,
      } as FcmMessageStatusSetReq);

      // Send it!
      const toSend = {
        notification: {
          title: message.title,
          body: message.body,
        },
        ...!!message.go && {data: {
          go: message.go,
        }},
        token: token!.token,
      } as Message;
      const response = await Fcm.send(toSend);
      await this.sendMessageSuccess(message, response);
    } catch (e) {
      await this.sendMessageFailure(message, e);
    }
  }

  private async sendMessageSuccess(
    message: FcmMessage,
    fcmResponse: string,
  ) {
    logger.debug('Successfully sent message:', fcmResponse);
    await this.fcmService.setStatus({
      messageId: message.id,
      userId: message.userId,
      status: FcmMessageStatus.Sent,
      fcmResponse: fcmResponse,
      by: by,
    } as FcmMessageStatusSetReq);
  }

  private async sendMessageFailure(
    message: FcmMessage,
    error: any,
  ) {
    logger.debug(`Error sending message: ${JSON.stringify(error)}`);
    const attempts = await this.fcmService.getAttempts(message.id);
    await this.fcmService.setStatus({
      messageId: message.id,
      userId: message.userId,
      status: (attempts <= maxAttempts - 1)
        ? FcmMessageStatus.Scheduled : FcmMessageStatus.Failed,
      fcmResponse: JSON.stringify(error ?? ''),
      by: by,
    } as FcmMessageStatusSetReq);
  }

  private async getMessages(): Promise<FcmMessage[]> {
    const page = new Page(1, 25); // (!) arbitrary page size
    const criteria = {
      status: FcmMessageStatus.Scheduled,
      scheduledToSend: true,
    } as FcmMessageFindCriteria;
    return await this.fcmService.findMessages(criteria, page);
  }

  private async isWithinBlackout(
    message: FcmMessage, config: FcmConfig, member: Member
  ): Promise<boolean> {
    const blackoutStart = DateTime.fromFormat(config.blackoutStart, "HH:mm:ss", { zone: member!.timezone }).setZone('UTC');
    const blackoutEnd = DateTime.fromFormat(config.blackoutEnd, "HH:mm:ss", { zone: member!.timezone }).setZone('UTC');

    // First, get the edge conditions out of the way.
    // If blackoutStart == blackoutEnd, means no blackout.
    if (blackoutStart == blackoutEnd) {
      logger.debug(
        `blackoutStart (${blackoutStart}) == blackoutEnd (${blackoutEnd})`
        + `, isWithinBlackout=false`);
      return false;
    }

    const nowTime = DateTime.now().setZone('UTC').toFormat('HH:mm:ss');
    const across2Days = blackoutEnd < blackoutStart;

    // (!) start inclusive, end exclusive
    const isWithinBlackout = nowTime == blackoutStart
      || (
        across2Days
          ? (nowTime > blackoutStart) || (nowTime < blackoutEnd)
          : (nowTime > blackoutStart) && (nowTime < blackoutEnd)
      );
    logger.debug(`nowTime=${nowTime},`
      + ` blackoutStart=${blackoutStart}, blackoutEnd=${blackoutEnd},`
      + ` across2Days=${across2Days}, isWithinBlackout=${isWithinBlackout}`);
    return isWithinBlackout;
  }

  private async rescheduleMessage(
    message: FcmMessage, config: FcmConfig, member: Member
  ) {
    const scheduledAt = message.scheduledAt;
    const blackoutStart = DateTime.fromFormat(config.blackoutStart, "HH:mm:ss", { zone: member!.timezone }).setZone('UTC');
    const blackoutEnd = DateTime.fromFormat(config.blackoutEnd, "HH:mm:ss", { zone: member!.timezone }).setZone('UTC');
    const across2Days = blackoutEnd < blackoutStart;

    const now = DateTime.now().setZone('UTC');
    const toScheduleNextDay = across2Days
      ? now.toFormat('HH:mm:ss') > blackoutEnd.toFormat('HH:mm:ss')
      : false;

    const rescheduleToStr = across2Days
      ? `${toScheduleNextDay ? now.plus({ days: 1 }) : now.toFormat('yyyy-MM-dd')} ${blackoutEnd.toFormat('HH:mm:ss')}`
      : `${now.toFormat('yyyy-MM-dd')} ${blackoutEnd.toFormat('HH:mm:ss')}`
      ;
    const rescheduleTo = DateTime.fromFormat(rescheduleToStr, "yyyy-MM-dd HH:mm:ss", { zone: member!.timezone }).setZone('UTC');
    if (!isValid(rescheduleTo)) {
      throw `Invalid rescheduleTo! ${rescheduleToStr}`;
    }
    logger.debug(`messageAt=${scheduledAt},`
      + ` blackoutStart=${blackoutStart}, blackoutEnd=${blackoutEnd},`
      + ` across2Days=${across2Days},`
      + ` toScheduleNextDay=${toScheduleNextDay}, rescheduleTo=${rescheduleTo}`);
    await this.fcmService.rescheduleMessage({
      message: message,
      rescheduleTo: rescheduleTo,
      by: "blackout-reschedule",
    } as FcmMessageRescheduleReq);
  }
}
