import 'dotenv/config';
import { FcmService } from "@fcm/services/fcm-service";
import { NotifyModel, SetNotifiedFeedbackReq } from "@scheduled-tasks/notify/models/notify";
import { NotifyService } from "@scheduled-tasks/notify/services/notify-service";
import logger from "@utils/logger";

const by = "NotifyFeedback";
// const intervalInMillis = 5 * 60 * 1_000;
const intervalInMillis = 15 * 1_000;

// Notify instructors to feedback session
export class NotifyFeedback {

  private timeoutId: NodeJS.Timeout|undefined;
  private notifyService = new NotifyService();
  private fcmService = new FcmService();

  async start(): Promise<void> {
    logger.debug("start()");
    this.timeoutId = setTimeout(() => this.execute(), 1_000);
  }

  async stop(): Promise<void> {
    logger.debug("stop()");
    if (!!this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  private async execute() {
    try {
      // First Time Notify After Session End
      const toNotify = await this.notifyService.findNotifyFeedback();
      for (const notify of toNotify) {
        // not feedback yet, notify
        await this.notify(notify);
        // set notified feedback
        await this.notifyService.setNotifiedFeedback({
          bookingId: notify.bookingId,
          by: by,
        } as SetNotifiedFeedbackReq);
      }

    } catch (e) {
      logger.warn(`Exception @ execute()! exception=${JSON.stringify(e)}`);
    } finally {
      this.timeoutId = setTimeout(() => this.execute(), intervalInMillis);
    }
  }

  private async notify(notify: NotifyModel) {
    try {
      if(notify.member) {
        const token = await this.fcmService.findTokenByUserId(notify.member!.id);
        if (token) {
          await this.fcmService.scheduleSessionReviewReminderFcmMessage(by, notify.member!.id, notify.bookingId, 2);
        }
      }
    } catch (e) {
      logger.warn(`Exception @ notify()! exception=${JSON.stringify(e)}`);
    }
  }
}
