import 'dotenv/config';
import logger from "@utils/logger";
import { FcmSender } from "./fcm-sender";
import { ReleaseHold } from "./release-hold";
import { NotifyComplete} from "./notify-complete";
import { NotifyFeedback } from "./notify-feedback";
import { NotifyProfileComplete } from "./notify-complete-instructor";
import { NotifyCreditExpiry } from "./notify-credit-expiry";
import { CreditExpiry } from "./credit-expiry";

interface Scheduler {
    start(): Promise<void>;
    stop?(): Promise<void>;
}

const schedulers: Scheduler[] = [
    new FcmSender(),
    new ReleaseHold(),
    new NotifyComplete(),
    new NotifyFeedback(),
    new NotifyProfileComplete(),
    new NotifyCreditExpiry(),
    new CreditExpiry()
];

// Start all schedulers
schedulers.forEach(scheduler => {
    scheduler.start()
        .then(() => logger.debug(`${scheduler.constructor.name}.start()`))
        .catch(error => logger.error(`${scheduler.constructor.name}() error! ${JSON.stringify(error)}`));
});

// Handle graceful shutdown
process.on('SIGTERM', async () => {
    logger.info('SIGTERM received. Stopping all schedulers...');
    await Promise.all(schedulers.map(scheduler => 
        scheduler.stop?.().catch(error => 
            logger.error(`Error stopping ${scheduler.constructor.name}: ${error}`)
        )
    ));
    process.exit(0);
});