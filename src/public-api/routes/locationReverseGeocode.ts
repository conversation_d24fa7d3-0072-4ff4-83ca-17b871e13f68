import { Client, ReverseGeocodeRequest } from "@googlemaps/google-maps-services-js";
import logger from "@utils/logger";
import { RequestHandler } from "express";

const locationReverseGeocode: RequestHandler = async (req, res) => {
  const lat = req.query.lat!.toString().trim();
  const lng = req.query.lng!.toString().trim();

  const client = new Client();
  const result = await client.reverseGeocode({
    params: {
      key: process.env.GOOGLE_MAPS_API_KEY,
      latlng: `${lat},${lng}`,
    }
  } as ReverseGeocodeRequest);
  const results = result.data.results;
  logger.debug(`reverseGeocode() lat=${lat}, lng=${lng}, result=${JSON.stringify(results)}`);
  res.status(200).json(results);
};

export default locationReverseGeocode;
