import { Request<PERSON><PERSON><PERSON> } from "express";
import { StudioFeedbackSubmit } from "@public-api/models/public";
import { PublicService } from "@public-api/services/public-service";

const publicService = new PublicService();

const studioFeedbackSubmit: RequestHandler = async (req, res) => {
  const { bookingRef, video, rating, feedback } = req.body as StudioFeedbackSubmit;
  if (!bookingRef || !video || !rating || !feedback) {
    res.status(400).json({ success: false, message: "Missing required fields" });
    return;
  }
  if (typeof rating !== "number" || rating < 1 || rating > 5) {
    res.status(400).json({ success: false, message: "Rating must be 1-5" });
    return;
  }
  try {
    const result = await publicService.insertTutorialFeedback(bookingRef, video, rating, feedback);
    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    res.status(400).json({ success: false, message: (error as Error).message });
  }
};

export default studioFeedbackSubmit; 