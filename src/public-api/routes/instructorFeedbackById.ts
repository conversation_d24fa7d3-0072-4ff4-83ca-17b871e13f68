import { PublicService } from "@public-api/services/public-service";
import { Page } from "@shared/services";
import { RequestHand<PERSON> } from "express";
import { toFeedbackJson } from "./shared/response-output";

const publicService = new PublicService();

const instructorFeedbackById: RequestHandler = async (req, res) => {
  // const idToken = getIdToken(req);
  // const uid = idToken.uid;

  const id = req.params['instructorId']!;
  const page = new Page(+req.params.page, +req.params.pageSize);
  const ret = await publicService.instructorFeedbackById(id, page);
  res.status(200).json(toFeedbackJson(ret, {
    userTimezone: req.userTimezone,
  }));
};

export default instructorFeedbackById;
