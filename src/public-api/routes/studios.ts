import { StudioFindCriteria } from "@public-api/models/public";
import { PublicService } from "@public-api/services/public-service";
import { SearchService } from "@booking-api/services/search-service";
import { Page } from "@shared/services";
import { toStudio<PERSON>son } from "@studio-cms/routes/shared/response-output";
import { RequestHand<PERSON> } from "express";
import { getOptionalIdToken } from "../../init-openapi";
import { LatLng } from "@booking-api/models/search";

const publicService = new PublicService();

const studios: RequestHandler = async (req, res) => {

  const searchTerm = req.query.searchTerm;
  const page = new Page(+req.params.page, +req.params.pageSize);
  const latLng = (req.query.lat && req.query.lng) ? new LatLng(
    +req.query.lat!.toString().trim(),
    +req.query.lng!.toString().trim(),
  ): null;
  const distance = +req.query.distance!?.toString().trim();
  const equipTypes
    = req.query.equipTypes?.toString().trim()
    .split(",").map(o => o.trim());
  const instrLessOnly  = (req.query.instrLessOnly  + "") == "true";
  const faveStudioOnly = (req.query.faveStudioOnly + "") == "true";
  const preferredStudioFromFaveInstrOnly  = (req.query.preferredStudioFromFaveInstrOnly  + "") == "true";
  const ret = await publicService.studios({
    ...searchTerm && { searchTerm: searchTerm.toString().trim() },
    latLng: latLng,
    distance: distance,
    equipTypes: equipTypes,
    instrLessOnly : instrLessOnly,
    faveStudioOnly: faveStudioOnly,
    preferredStudioFromFaveInstrOnly: preferredStudioFromFaveInstrOnly,
    byId: getOptionalIdToken(req)?.uid,
  } as StudioFindCriteria, page);
  res.status(200).json(
    ret.map(e => toStudioJson(e))
  );
};

export default studios;
