import { Client, PlaceAutocompleteRequest } from "@googlemaps/google-maps-services-js";
import { RequestHandler } from "express";

const locationAutocomplete: RequestHandler = async (req, res) => {
  const input = req.query.input!.toString().trim();

  if (input.length < 2) {
    res.status(200).json([]);
    return;
  }

  const client = new Client();
  const result = await client.placeAutocomplete({
    params: {
      key: process.env.GOOGLE_MAPS_API_KEY,
      input: input,
      components: ["country:sg", "country:my"],
    }
  } as PlaceAutocompleteRequest);
  const results = result.data.predictions;
  res.status(200).json(results);
};

export default locationAutocomplete;
