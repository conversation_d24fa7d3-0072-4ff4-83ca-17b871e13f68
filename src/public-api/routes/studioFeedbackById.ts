import { PublicService } from "@public-api/services/public-service";
import { Page } from "@shared/services";
import { RequestHandler } from "express";
import { toFeedbackJson } from "./shared/response-output";

const publicService = new PublicService();

const studioFeedbackById: RequestHandler = async (req, res) => {

  const id = req.params['studioId']!;
  const page = new Page(+req.params.page, +req.params.pageSize);
  const ret = await publicService.studioFeedbackById(id, page);
  res.status(200).json(toFeedbackJson(ret, {
    userTimezone: req.userTimezone,
  }));
};

export default studioFeedbackById;
