import { toInstructor<PERSON><PERSON> } from "@instructor-api/routes/shared/response-output";
import { InstructorFindCriteria } from "@public-api/models/public";
import { PublicService } from "@public-api/services/public-service";
import { Page } from "@shared/services";
import { Request<PERSON>and<PERSON> } from "express";
import { getOptionalIdToken } from "../../init-openapi";

const publicService = new PublicService();

const instructors: RequestHandler = async (req, res) => {
  // const idToken = getIdToken(req);
  // const uid = idToken.uid;

  const searchTerm = req.query.searchTerm;
  const page = new Page(+req.params.page, +req.params.pageSize);
  const equipTypes
    = req.query.equipTypes?.toString().trim()
    .split(",").map(o => o.trim());
  const faveInstrOnly = (req.query.faveInstrOnly + "") == "true";
  const ret = await publicService.instructors({
    ...searchTerm && { searchTerm: searchTerm.toString().trim() },
    equipTypes: equipTypes,
    faveInstrOnly: faveInstrOnly,
    byId: getOptionalIdToken(req)?.uid,
  } as InstructorFindCriteria, page);
  res.status(200).json(
    ret.map(e => toInstructorJson(e))
  );
};

export default instructors;
