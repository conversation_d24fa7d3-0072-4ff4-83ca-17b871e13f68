import { PublicService } from "@public-api/services/public-service";
import { SearchService } from "@booking-api/services/search-service";
import { toStudioJson } from "@studio-cms/routes/shared/response-output";
import { RequestHandler } from "express";
import { LatLng } from "@booking-api/models/search";
import { getOptionalIdToken } from "../../init-openapi";

const publicService = new PublicService();

const studioById: RequestHandler = async (req, res) => {
  // const idToken = getIdToken(req);
  // const uid = idToken.uid;

  const id = req.params['studioId']!;
  const latLng = (req.query.lat && req.query.lng) ? new LatLng(
    +req.query.lat!.toString().trim(),
    +req.query.lng!.toString().trim(),
  ): null;
  const uid = getOptionalIdToken(req)?.uid;
  const ret = await publicService.studioById(id, latLng, uid);
  if (!ret || ret.deleted) {
    res.status(204).end(); // no content
  } else {
    res.status(200).json(toStudioJson(ret));
  }
};

export default studioById;
