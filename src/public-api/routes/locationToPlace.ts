import { Client, PlaceDetailsRequest } from "@googlemaps/google-maps-services-js";
import { RequestHandler } from "express";

const locationToPlace: RequestHandler = async (req, res) => {
  const placeId = req.query.placeId!.toString().trim();

  const client = new Client();
  const result = await client.placeDetails({
    params: {
      key: process.env.GOOGLE_MAPS_API_KEY,
      place_id: placeId
    }
  } as PlaceDetailsRequest);
  res.status(200).json(result.data.result);
};

export default locationToPlace;
