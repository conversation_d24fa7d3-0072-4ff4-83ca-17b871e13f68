import { MessageService } from "@public-api/services/message-service";
import { <PERSON>quest<PERSON><PERSON><PERSON> } from "express";

const messageService = new MessageService();

const messageByCode: RequestHandler = async (req, res) => {
  const code = req.query.code?.toString()?.trim() ?? '';
  if(![
    "booking.tnc",
    "booking.footer",
    //
    "instructor.edit.instructions",
    "instructor.onboard.step1",
    "instructor.onboard.step2",
    "instructor.onboard.step3",
    "instructor.onboard.completed",
    "instructor.rates.instructions",
    //
    "member.reg.instructions",
    //
    "booking.slot.taken",
    //
    "booking.cancel.title.free",
    "booking.cancel.title.nonfree",
    "booking.cancel.message.free",
    "booking.cancel.message.nonfree",
    //
    "booking.noshow.message",
    "booking.noshow.confirmed",
    "booking.feedback.message",
    "booking.nofeedback.message",
    "booking.feedback.notstarted",
    //
    "pending.feedback.booking",
    "pending.feedback.bookings",
    //
    "session.cancel.message",
    "session.noshow.message",
    "session.noshow.confirmed",
    "session.complete.prompt",
    //
    "notfn.blackout.message",
    "account.requestDelete.confirm",
    "account.deleted.message",
  ].includes(code)) {
    res.status(204).end(); // no content
    return;
  }

  const ret = await messageService.findMessageByCode(code);
  if (!ret) {
    res.status(204).end(); // no content
  } else {
    res.status(200).end(ret);
  }
};

export default messageByCode;
