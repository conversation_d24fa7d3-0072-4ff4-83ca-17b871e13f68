import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { CometChatWebhookService } from "@public-api/services/cometchat-webhook-service";
import logger from "@utils/logger";
import crypto from "crypto";

const webhookService = new CometChatWebhookService();

const cometchatWebhookLogs: RequestHandler = async (req, res) => {
  const clientIp = req.ip || req.connection.remoteAddress || 'unknown';
  const requestId = crypto.randomUUID();
  
  try {
    // Parse and validate query parameters
    const { 
      logId, 
      senderUid, 
      conversationId, 
      limit = '50',
      offset = '0',
      status,
      trigger 
    } = req.query;
    
    // Validate limit and offset
    const limitNum = Math.min(Math.max(parseInt(limit.toString()) || 50, 1), 1000);
    const offsetNum = Math.max(parseInt(offset.toString()) || 0, 0);
    
    logger.debug(`[CometChatWebhookLogs:${requestId}] Query from ${clientIp}: logId=${logId}, senderUid=${senderUid}, conversationId=${conversationId}, limit=${limitNum}, offset=${offsetNum}`);

    // Get specific log by ID
    if (logId) {
      if (typeof logId !== 'string' || logId.trim().length === 0) {
        logger.warn(`[CometChatWebhookLogs:${requestId}] Invalid logId parameter from ${clientIp}: ${logId}`);
        res.status(400).json({ error: 'Invalid logId parameter' });
        return;
      }

      const log = await webhookService.getWebhookLogById(logId.trim());
      if (!log) {
        logger.info(`[CometChatWebhookLogs:${requestId}] Log not found: ${logId}`);
        res.status(404).json({ 
          error: 'Webhook log not found',
          logId: logId.trim(),
          requestId 
        });
        return;
      }
      
      logger.info(`[CometChatWebhookLogs:${requestId}] Retrieved log: ${logId}`);
      res.status(200).json({
        log,
        requestId,
      });
      return;
    }

    // Get logs by sender UID
    if (senderUid) {
      if (typeof senderUid !== 'string' || senderUid.trim().length === 0) {
        logger.warn(`[CometChatWebhookLogs:${requestId}] Invalid senderUid parameter from ${clientIp}: ${senderUid}`);
        res.status(400).json({ error: 'Invalid senderUid parameter' });
        return;
      }

      const logs = await webhookService.getWebhookLogsBySenderUid(
        senderUid.trim(), 
        limitNum
      );
      
      logger.info(`[CometChatWebhookLogs:${requestId}] Retrieved ${logs.length} logs for sender: ${senderUid}`);
      res.status(200).json({
        logs,
        count: logs.length,
        senderUid: senderUid.trim(),
        limit: limitNum,
        requestId,
      });
      return;
    }

    // Get logs by conversation ID
    if (conversationId) {
      if (typeof conversationId !== 'string' || conversationId.trim().length === 0) {
        logger.warn(`[CometChatWebhookLogs:${requestId}] Invalid conversationId parameter from ${clientIp}: ${conversationId}`);
        res.status(400).json({ error: 'Invalid conversationId parameter' });
        return;
      }

      const logs = await webhookService.getWebhookLogsByConversationId(
        conversationId.trim(), 
        limitNum
      );
      
      logger.info(`[CometChatWebhookLogs:${requestId}] Retrieved ${logs.length} logs for conversation: ${conversationId}`);
      res.status(200).json({
        logs,
        count: logs.length,
        conversationId: conversationId.trim(),
        limit: limitNum,
        requestId,
      });
      return;
    }

    // Get unprocessed logs (default)
    const logs = await webhookService.getUnprocessedWebhookLogs(limitNum);
    
    logger.info(`[CometChatWebhookLogs:${requestId}] Retrieved ${logs.length} unprocessed logs`);
    res.status(200).json({
      logs,
      count: logs.length,
      type: 'unprocessed',
      limit: limitNum,
      requestId,
    });

  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : String(error);
    logger.error(`[CometChatWebhookLogs:${requestId}] Error from ${clientIp}: ${errorMsg}`);
    res.status(500).json({
      error: 'Internal server error',
      message: errorMsg,
      requestId,
    });
  }
};

export default cometchatWebhookLogs; 