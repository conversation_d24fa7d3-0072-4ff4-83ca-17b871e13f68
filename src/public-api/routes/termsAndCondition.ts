import { RequestHand<PERSON> } from "express";
import { PublicService } from "@public-api/services/public-service";
import logger from "@utils/logger";

const publicService = new PublicService();

const termsAndCondition: RequestHandler = async (req, res) => {
    try {
        const terms = await publicService.findLatestTermsAndConditions();
        if (!terms) {
            res.status(404).json({ message: "No terms and conditions found" });
            return;
        }
        res.status(200).json(terms);
    } catch (error) {
        logger.error("Error fetching terms:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};

export default termsAndCondition;