import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { CometChatWebhookService } from "@public-api/services/cometchat-webhook-service";
import { CometChatWebhookPayload } from "@public-api/models/cometchat";
import logger from "@utils/logger";
import crypto from "crypto";

const webhookService = new CometChatWebhookService();

const cometchatWebhook: RequestHandler = async (req, res) => {
  const clientIp = req.ip || req.connection.remoteAddress || 'unknown';
  const requestId = crypto.randomUUID();
  
  try {
    // Log incoming request with request ID
    const bodyString = JSON.stringify(req.body);
    logger.debug(`[CometChatWebhook:${requestId}] Received webhook from ${clientIp}: ${bodyString}`);

    // Validate Basic Authentication
    const authHeader = req.headers['authorization'] as string;
    const expectedUsername = process.env.COMETCHAT_USERNAME;
    const expectedPassword = process.env.COMETCHAT_PASSWORD;

    if (!expectedUsername || !expectedPassword) {
      logger.error(`[CometChatWebhook:${requestId}] Missing authentication credentials in environment`);
      res.status(500).json({ error: 'Server configuration error' });
      return;
    }

    if (!authHeader) {
      logger.warn(`[CometChatWebhook:${requestId}] Missing authorization header from ${clientIp}`);
      res.status(401).json({ error: 'Authentication required' });
      return;
    }

    const isValid = validateBasicAuth(authHeader, expectedUsername, expectedPassword);
    if (!isValid) {
      logger.warn(`[CometChatWebhook:${requestId}] Invalid Basic Auth from ${clientIp}: ${authHeader.substring(0, 20)}...`);
      res.status(401).json({ error: 'Invalid authentication' });
      return;
    }

    // Validate payload structure
    if (!req.body || typeof req.body !== 'object') {
      logger.warn(`[CometChatWebhook:${requestId}] Invalid request body from ${clientIp}`);
      res.status(400).json({ error: 'Invalid request body' });
      return;
    }

    const payload = req.body as CometChatWebhookPayload;
    if (!isValidWebhookPayload(payload)) {
      logger.warn(`[CometChatWebhook:${requestId}] Invalid payload structure from ${clientIp}: ${bodyString.substring(0, 200)}...`);
      res.status(400).json({ error: 'Invalid payload structure' });
      return;
    }

    // Process the webhook - ensure it gets stored in DB
    logger.info(`[CometChatWebhook:${requestId}] Processing webhook: ${payload.trigger} from ${clientIp}`);
    const result = await webhookService.processWebhook(payload);

    // Always return success if webhook was logged to DB, even if processing failed
    if (result.logId) {
      logger.info(`[CometChatWebhook:${requestId}] Webhook logged to DB: ${result.logId}, processed: ${result.processed}`);
      res.status(200).json({
        success: true,
        message: 'Webhook received and logged',
        logId: result.logId,
        processed: result.processed,
        requestId,
      });
    } else {
      // Only return error if we couldn't even log to DB
      logger.error(`[CometChatWebhook:${requestId}] Failed to log webhook to DB: ${result.message}`);
      res.status(500).json({
        success: false,
        message: 'Failed to log webhook',
        requestId,
      });
    }

  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : String(error);
    logger.error(`[CometChatWebhook:${requestId}] Critical error from ${clientIp}: ${errorMsg}`);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      requestId,
    });
  }
};

function validateBasicAuth(authHeader: string, expectedUsername: string, expectedPassword: string): boolean {
  try {
    // Check if header starts with "Basic "
    if (!authHeader.startsWith('Basic ')) {
      return false;
    }

    // Extract the Base64 encoded credentials
    const base64Credentials = authHeader.substring(6); // Remove "Basic "

    // Decode Base64 to get username:password
    const credentials = Buffer.from(base64Credentials, 'base64').toString('utf8');
    const [username, password] = credentials.split(':');

    // Validate credentials exist
    if (!username || !password) {
      return false;
    }

    // Compare with expected credentials using timing-safe comparison
    const usernameMatch = crypto.timingSafeEqual(
      Buffer.from(username, 'utf8'),
      Buffer.from(expectedUsername, 'utf8')
    );
    
    const passwordMatch = crypto.timingSafeEqual(
      Buffer.from(password, 'utf8'),
      Buffer.from(expectedPassword, 'utf8')
    );

    return usernameMatch && passwordMatch;
  } catch (error) {
    logger.error(`[CometChatWebhook] Error validating Basic Auth: ${error}`);
    return false;
  }
}

function isValidWebhookPayload(payload: any): payload is CometChatWebhookPayload {
  try {
    return !!(
      payload &&
      typeof payload === 'object' &&
      typeof payload.trigger === 'string' &&
      payload.trigger.length > 0 &&
      payload.data &&
      typeof payload.data === 'object' &&
      typeof payload.appId === 'string' &&
      typeof payload.region === 'string' &&
      typeof payload.webhook === 'string' &&
      (
        // Check for message webhook
        (payload.data.message &&
         typeof payload.data.message === 'object' &&
         typeof payload.data.message.id === 'string' &&
         typeof payload.data.message.conversationId === 'string' &&
         typeof payload.data.message.sender === 'string' &&
         typeof payload.data.message.receiver === 'string' &&
         typeof payload.data.message.receiverType === 'string' &&
         typeof payload.data.message.category === 'string' &&
         typeof payload.data.message.type === 'string' &&
         typeof payload.data.message.sentAt === 'number') ||
        // Check for delivery receipt webhook
        (typeof payload.data.receiver === 'string' &&
         typeof payload.data.receiverType === 'string' &&
         typeof payload.data.type === 'string' &&
         typeof payload.data.sender === 'string' &&
         typeof payload.data.messageSender === 'string' &&
         payload.data.body &&
         typeof payload.data.body === 'object' &&
         typeof payload.data.body.action === 'string' &&
         typeof payload.data.body.messageId === 'string' &&
         typeof payload.data.body.timestamp === 'number') ||
        // Check for reaction webhook
        (payload.data.reaction &&
         typeof payload.data.reaction === 'object' &&
         typeof payload.data.reaction.id === 'string' &&
         typeof payload.data.reaction.messageId === 'string' &&
         typeof payload.data.reaction.reaction === 'string' &&
         typeof payload.data.reaction.uid === 'string' &&
         typeof payload.data.reaction.reactedAt === 'number' &&
         payload.data.reaction.reactedBy &&
         typeof payload.data.reaction.reactedBy === 'object' &&
         typeof payload.data.reaction.reactedBy.uid === 'string' &&
         typeof payload.data.reaction.reactedBy.name === 'string' &&
         typeof payload.data.reaction.reactedBy.status === 'string' &&
         typeof payload.data.reaction.reactedBy.role === 'string' &&
         typeof payload.data.reaction.reactedBy.lastActiveAt === 'number' &&
         typeof payload.data.reaction.reactedBy.resource === 'string' &&
         payload.data.reaction.reactedOn &&
         typeof payload.data.reaction.reactedOn === 'object' &&
         typeof payload.data.reaction.reactedOn.id === 'string' &&
         typeof payload.data.reaction.reactedOn.conversationId === 'string' &&
         typeof payload.data.reaction.reactedOn.sender === 'string' &&
         typeof payload.data.reaction.reactedOn.receiver === 'string' &&
         typeof payload.data.reaction.reactedOn.receiverType === 'string' &&
         typeof payload.data.reaction.reactedOn.category === 'string' &&
         typeof payload.data.reaction.reactedOn.type === 'string' &&
         typeof payload.data.reaction.reactedOn.sentAt === 'number')
      )
    );
  } catch (error) {
    logger.error(`[CometChatWebhook] Error validating webhook payload: ${error}`);
    return false;
  }
}

export default cometchatWebhook;