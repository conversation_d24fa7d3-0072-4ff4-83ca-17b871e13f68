import { Feedback } from "@public-api/models/public";
import { DateTime } from "luxon";

export function toFeedback<PERSON>son(feedback: Feedback, {
  userTimezone = 'Asia/Singapore',
}: {
  userTimezone?: string;
} = {}) {
    return {
        avgRating: Number(feedback.avgRating).toFixed(1),
        totalRating: feedback.totalRating,
        groupsRating: feedback.groupsRating && feedback.groupsRating.map(o => ({
            rating: Number(o.rating).toFixed(1),
            totalRating: o.totalRating,
        })),
        feedback: feedback.feedback && feedback.feedback.map(o => ({
            rating: Number(o.rating).toFixed(1),
            feedback: o.feedback,
            feedbackAt: DateTime.fromFormat(o.feedbackAt, 'yyyy-MM-dd HH:mm:ss', { zone: 'UTC' }).setZone(userTimezone).toFormat('yyyy-MM-dd HH:mm:ss'),
      })),
  }
}