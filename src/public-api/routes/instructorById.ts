import { toInstru<PERSON><PERSON><PERSON> } from "@instructor-api/routes/shared/response-output";
import { PrefService } from "@instructor-api/services/pref-service";
import { PublicService } from "@public-api/services/public-service";
import { RequestHandler } from "express";
import { getOptionalIdToken } from "../../init-openapi";

const publicService = new PublicService();
const prefService = new PrefService();

const instructorById: RequestHandler = async (req, res) => {
  // const idToken = getIdToken(req);
  // const uid = idToken.uid;

  const id = req.params['instructorId']!;
  const uid = getOptionalIdToken(req)?.uid;
  const ret = await publicService.instructorById(id, uid);
  if (!ret || ret.deleted || !ret.onboarded) {
    res.status(204).end(); // no content
  } else {
    ret.prefStudios = await prefService.getPrefStudios(ret.id);
    res.status(200).json(toInstructorJson(ret));
  }
};

export default instructorById;
