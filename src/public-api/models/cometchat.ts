export interface CometChatUser {
  uid: string;
  name: string;
  email: string;
  phone?: string;
  tags: string[];
  metadata?: Record<string, any>; // allow custom metadata like userType
  userType?: string; // explicit userType property for clarity
}

export interface CometChatReaction {
  reaction: string;
  count: number;
  reactedByMe: boolean;
}

export interface CometChatReactionData {
  id: string;
  messageId: string;
  reaction: string;
  uid: string;
  reactedAt: number;
  reactedBy: {
    uid: string;
    name: string;
    status: string;
    role: string;
    lastActiveAt: number;
    resource: string;
  };
  reactedOn: CometChatMessage;
}

export interface CometChatMessage {
  id: string;
  muid: string;
  conversationId: string;
  sender: string;
  receiverType: string;
  receiver: string;
  category: string;
  type: string;
  data: {
    text?: string;
    resource?: string;
    reactions?: CometChatReaction[];
    entities?: {
      sender?: {
        entity: {
          uid: string;
          name: string;
          status: string;
          role: string;
          lastActiveAt: number;
          metadata?: {
            '@private'?: {
              contactNumber?: string | null;
              email?: string;
            };
          };
        };
        entityType: string;
      };
      receiver?: {
        entity: {
          uid: string;
          name: string;
          status: string;
          role: string;
          lastActiveAt: number;
          conversationId?: string;
          metadata?: {
            '@private'?: {
              contactNumber?: string;
              email?: string;
            };
          };
        };
        entityType: string;
      };
    };
  };
  sentAt: number;
  deliveredAt?: number;
  readAt?: number;
  updatedAt: number;
}

export interface CometChatDeliveryReceipt {
  receiver: string;
  receiverType: string;
  type: string;
  sender: string;
  messageSender: string;
  body: {
    action: string;
    messageId: string;
    user: {
      hasBlockedMe: boolean;
      blockedByMe: boolean;
      deactivatedAt: number;
      uid: string;
      name: string;
      metadata?: {
        '@private'?: {
          contactNumber?: string;
          email?: string;
        };
      };
      role: string;
      status: string;
    };
    timestamp: number;
  };
}

export interface CometChatWebhookPayload {
  trigger: string;
  data: CometChatMessage | CometChatDeliveryReceipt | { reaction: CometChatReactionData };
  appId: string;
  region: string;
  webhook: string;
}

export interface CometChatWebhookLog {
  id?: string;
  trigger: string;
  category?: string;
  messageId?: string;
  senderUid?: string;
  senderUserType?: string;
  receiverUid?: string;
  receiverUserType?: string;
  receiverType?: string;
  conversationId?: string;
  messageType?: string;
  messageText?: string;
  action?: string;
  payload: string;
  processed: boolean;
  error?: string;
  createdAt?: Date;
  processedAt?: Date;
}

export interface CometChatWebhookResponse {
  success: boolean;
  message: string;
  processed: boolean;
  logId?: string;
}

// Webhook event types for better type safety
export type WebhookEventType = 'message_sent' | 'message_delivery_receipt' | 'message_read' | 'conversation_created' | 'conversation_updated' | 'user_online' | 'user_offline' | 'message_reaction_added';

// Event data interfaces for better type safety
export interface MessageSentEvent {
  messageId: string;
  senderUid: string;
  senderUserType?: string;
  receiverUid: string;
  receiverUserType?: string;
  receiverType: string;
  conversationId: string;
  message?: string;
  messageType: string;
  sentAt: number;
  metadata?: any;
}

export interface MessageDeliveredEvent {
  messageId: string;
  receiverUid: string;
  deliveredAt: number;
}

export interface MessageReadEvent {
  messageId: string;
  readerUid: string;
  conversationId: string;
  readAt: number;
}

export interface ConversationEvent {
  conversationId: string;
  conversationType: string;
  participants?: string[];
  createdAt?: number;
  updatedAt?: number;
  metadata?: any;
}

export interface UserStatusEvent {
  userId: string;
  lastActiveAt?: number;
  metadata?: any;
}

export interface MessageReactionEvent {
  reactionId: string;
  messageId: string;
  reaction: string;
  reactedByUid: string;
  reactedByUserType?: string;
  reactedAt: number;
  conversationId: string;
  messageText?: string;
  metadata?: any;
} 