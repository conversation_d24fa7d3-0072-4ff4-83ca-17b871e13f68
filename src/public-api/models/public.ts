import { LatLng } from "@booking-api/models/search";
import { EventMember } from "@studio-cms/models/event";
export interface StudioFindCriteria {
  id?: string,
  searchTerm?: string,
  latLng?: LatLng,
  distance?: number,
  equipTypes?: string[],
  instrLessOnly: boolean,
  faveStudioOnly: boolean,
  preferredStudioFromFaveInstrOnly: boolean,
  byId?: string,
  inclDeleted: boolean,
}

export interface InstructorFindCriteria {
  id?: string,
  searchTerm?: string,
  equipTypes?: string[],
  faveInstrOnly: boolean,
  byId?: string,
  inclDeleted: boolean,
}

export interface Feedback {
  avgRating: number;
  totalRating: number;
  groupsRating: GroupRating[];
  feedback: SessionFeedback[];
}

export interface GroupRating {
  rating: number;
  totalRating: number;
}

export interface SessionFeedback {
  rating: number;
  feedback: string;
  feedbackAt: string;
  memberId?: string;
  member?: EventMember;
}

export interface SessionFeedbackFindCriteria {
  studioId?: string;
  memberId?: string;
  startDate?: string;
  endDate?: string;
}

export interface Terms {
  version: number;
  content: string;
  createdAt: string;
} 

export interface PrivacyPolicy {
  version: number;
  content: string;
  createdAt: string;
}

export interface StudioFeedbackSubmit {
  bookingRef: string;
  video: string;
  rating: number;
  feedback: string;
}

export interface Country {
  code: string;
  name: string;
  timezone: string;
  currency: string;
  currency_name: string;
  symbol: string;
}
