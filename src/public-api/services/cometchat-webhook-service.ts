import {
  CometChatWebhookPayload,
  CometChatWebhookLog,
  CometChatWebhookResponse,
  CometChatMessage,
  CometChatDeliveryReceipt,
  CometChatReactionData,
  WebhookEventType,
  MessageSentEvent,
  MessageDeliveredEvent,
  MessageReadEvent,
  ConversationEvent,
  UserStatusEvent,
  MessageReactionEvent
} from "@public-api/models/cometchat";
import { CometChatWebhookRepo } from "@public-api/repo/cometchat-webhook-repo";
import pool from "../../init-pool";
import logger from "@utils/logger";
import eventEmitter from "@utils/event-emitter";

export class CometChatWebhookService {
  private webhookRepo = new CometChatWebhookRepo();

  /**
   * Enhanced type guard for reaction webhooks
   */
  private isReactionWebhook(payload: CometChatWebhookPayload): payload is CometChatWebhookPayload & { data: { reaction: CometChatReactionData } } {
    try {
      return !!(
        payload?.data &&
        typeof payload.data === 'object' &&
        'reaction' in payload.data &&
        payload.data.reaction &&
        typeof payload.data.reaction === 'object' &&
        'id' in payload.data.reaction &&
        'messageId' in payload.data.reaction &&
        'reaction' in payload.data.reaction &&
        'uid' in payload.data.reaction &&
        'reactedAt' in payload.data.reaction
      );
    } catch (error) {
      logger.error(`[CometChatWebhook] Error in isReactionWebhook type guard: ${error}`);
      return false;
    }
  }

  /**
   * Enhanced type guard for message webhooks
   */
  private isMessageWebhook(payload: CometChatWebhookPayload): payload is CometChatWebhookPayload & { data: { message: CometChatMessage } } {
    try {
      return !!(
        payload?.data &&
        typeof payload.data === 'object' &&
        'message' in payload.data &&
        payload.data.message &&
        typeof payload.data.message === 'object' &&
        'id' in payload.data.message &&
        'conversationId' in payload.data.message &&
        'sender' in payload.data.message &&
        'receiver' in payload.data.message
      );
    } catch (error) {
      logger.error(`[CometChatWebhook] Error in isMessageWebhook type guard: ${error}`);
      return false;
    }
  }

  /**
   * Enhanced type guard for delivery receipt webhooks
   */
  private isDeliveryReceiptWebhook(payload: CometChatWebhookPayload): payload is CometChatWebhookPayload & { data: CometChatDeliveryReceipt } {
    try {
      return !!(
        payload?.data &&
        typeof payload.data === 'object' &&
        'body' in payload.data &&
        payload.data.body &&
        typeof payload.data.body === 'object' &&
        'messageId' in payload.data.body &&
        'action' in payload.data.body &&
        'timestamp' in payload.data.body
      );
    } catch (error) {
      logger.error(`[CometChatWebhook] Error in isDeliveryReceiptWebhook type guard: ${error}`);
      return false;
    }
  }

  /**
   * Validate webhook payload structure
   */
  private validateWebhookPayload(payload: any): payload is CometChatWebhookPayload {
    return !!(
      payload &&
      typeof payload === 'object' &&
      'trigger' in payload &&
      'data' in payload &&
      'appId' in payload &&
      'region' in payload &&
      'webhook' in payload
    );
  }

  /**
   * Utility to extract userType from CometChat metadata
   */
  private extractUserType(metadata: any): string | undefined {
    if (!metadata) return undefined;
    if (metadata['@private'] && metadata['@private'].userType) {
      return metadata['@private'].userType;
    }
    if (metadata.userType) {
      return metadata.userType;
    }
    return undefined;
  }

  /**
   * Create webhook log entry with proper error handling
   */
  private createWebhookLog(payload: CometChatWebhookPayload): CometChatWebhookLog {
    try {
      if (this.isMessageWebhook(payload)) {
        const message = (payload.data as any).message;
        // Extract userType from sender/receiver metadata if available
        const senderUserType = this.extractUserType(message.data?.entities?.sender?.entity?.metadata);
        const receiverUserType = this.extractUserType(message.data?.entities?.receiver?.entity?.metadata);
        logger.debug(`[CometChatWebhook] Creating webhook log for message webhook, messageId: ${message.id}`);

        return {
          trigger: payload.trigger,
          category: message.category,
          messageId: message.id,
          senderUid: message.sender,
          senderUserType,
          receiverUid: message.receiver,
          receiverUserType,
          receiverType: message.receiverType,
          conversationId: message.conversationId,
          messageType: message.type,
          messageText: message.data?.text,
          payload: JSON.stringify(payload),
          processed: false,
        };
      } else if (this.isDeliveryReceiptWebhook(payload)) {
        const receipt = payload.data as CometChatDeliveryReceipt;
        // Extract userType from body.user.metadata if available
        const receiverUserType = this.extractUserType(receipt.body?.user?.metadata);
        logger.debug(`[CometChatWebhook] Creating webhook log for delivery receipt, messageId: ${receipt.body.messageId}`);

        return {
          trigger: payload.trigger,
          messageId: receipt.body.messageId,
          senderUid: receipt.sender,
          receiverUid: receipt.receiver,
          receiverUserType,
          receiverType: receipt.receiverType,
          action: receipt.body.action,
          payload: JSON.stringify(payload),
          processed: false,
        };
      } else if (this.isReactionWebhook(payload)) {
        const reaction = payload.data.reaction as CometChatReactionData;
        // Extract userType from reactedBy metadata if available
        const reactedByUserType = this.extractUserType(reaction.reactedBy);
        logger.debug(`[CometChatWebhook] Creating webhook log for reaction webhook, reactionId: ${reaction.id}`);

        return {
          trigger: payload.trigger,
          messageId: reaction.messageId,
          senderUid: reaction.uid,
          senderUserType: reactedByUserType,
          receiverUid: reaction.reactedOn.sender,
          receiverType: reaction.reactedOn.receiverType,
          conversationId: reaction.reactedOn.conversationId,
          messageType: reaction.reactedOn.type,
          messageText: reaction.reactedOn.data?.text,
          payload: JSON.stringify(payload),
          processed: false,
        };
      } else {
        // Fallback for unknown webhook types
        logger.warn(`[CometChatWebhook] Unknown webhook type, creating minimal log entry`);
        return {
          trigger: payload.trigger,
          payload: JSON.stringify(payload),
          processed: false,
        };
      }
    } catch (error) {
      logger.error(`[CometChatWebhook] Error creating webhook log: ${error}`);
      throw new Error(`Failed to create webhook log: ${error}`);
    }
  }

  /**
   * Process webhook with robust database storage and error handling
   */
  async processWebhook(payload: CometChatWebhookPayload): Promise<CometChatWebhookResponse> {
    let conn;
    let logId: string | undefined;

    // Validate payload structure
    if (!this.validateWebhookPayload(payload)) {
      logger.error(`[CometChatWebhook] Invalid webhook payload structure`);
      return {
        success: false,
        message: 'Invalid webhook payload structure',
        processed: false,
      };
    }

    try {
      // Get database connection with retry logic
      conn = await this.getConnectionWithRetry();
      if (!conn) {
        throw new Error('Failed to get database connection after retries');
      }

      // Create webhook log entry
      const webhookLog = this.createWebhookLog(payload);

      // Insert webhook log with retry logic
      logId = await this.insertWebhookLogWithRetry(conn, webhookLog);
      if (!logId) {
        throw new Error('Failed to insert webhook log after retries');
      }

      logger.info(`[CometChatWebhook] Successfully logged webhook: ${logId}, trigger: ${payload.trigger}`);

      // Process webhook based on trigger (non-blocking)
      this.processWebhookAsync(payload, logId, conn).catch(error => {
        logger.error(`[CometChatWebhook] Async processing failed for log ${logId}: ${error}`);
      });

      return {
        success: true,
        message: 'Webhook received and logged successfully',
        processed: false, // Will be updated by async processing
        logId,
      };

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      logger.error(`[CometChatWebhook] Critical error: ${errorMsg}`);

      // Try to log the error if we have a logId
      if (logId && conn) {
        try {
          await this.webhookRepo.updateWebhookLogProcessed(conn, logId, false, errorMsg);
        } catch (updateError) {
          logger.error(`[CometChatWebhook] Failed to update webhook log with error: ${updateError}`);
        }
      }

      return {
        success: false,
        message: 'Failed to process webhook',
        processed: false,
        logId,
      };
    } finally {
      if (conn) {
        conn.release();
      }
    }
  }

  /**
   * Process webhook based on trigger type
   */
  private async processWebhookByTrigger(payload: CometChatWebhookPayload): Promise<{ success: boolean; error?: string }> {
    try {
      const trigger = payload.trigger as WebhookEventType;

      switch (trigger) {
        case 'message_sent':
          return { success: await this.handleMessageSent(payload) };
        case 'message_delivery_receipt':
          return { success: await this.handleMessageDeliveryReceipt(payload) };
        case 'message_read':
          return { success: await this.handleMessageRead(payload) };
        case 'message_reaction_added':
          return { success: await this.handleMessageReactionAdded(payload) };
        case 'conversation_created':
          return { success: await this.handleConversationCreated(payload) };
        case 'conversation_updated':
          return { success: await this.handleConversationUpdated(payload) };
        case 'user_online':
          return { success: await this.handleUserOnline(payload) };
        case 'user_offline':
          return { success: await this.handleUserOffline(payload) };
        default:
          logger.warn(`[CometChatWebhook] Unhandled trigger: ${trigger}`);
          return { success: true }; // Mark as processed to avoid reprocessing
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      logger.error(`[CometChatWebhook] Error processing webhook trigger ${payload.trigger}: ${errorMsg}`);
      return { success: false, error: errorMsg };
    }
  }

  /**
   * Emit event with error handling
   */
  private emitEvent(eventName: string, eventData: any): void {
    try {
      eventEmitter.emit(eventName, eventData);
      logger.debug(`[CometChatWebhook] Emitted event: ${eventName}`);
    } catch (error) {
      logger.error(`[CometChatWebhook] Error emitting event ${eventName}: ${error}`);
    }
  }

  /**
   * Extract message data safely
   */
  private extractMessageData(payload: CometChatWebhookPayload): CometChatMessage | null {
    try {
      if (this.isMessageWebhook(payload)) {
        return (payload.data as any).message;
      }
      return null;
    } catch (error) {
      logger.error(`[CometChatWebhook] Error extracting message data: ${error}`);
      return null;
    }
  }

  /**
   * Extract delivery receipt data safely
   */
  private extractDeliveryReceiptData(payload: CometChatWebhookPayload): CometChatDeliveryReceipt | null {
    try {
      if (this.isDeliveryReceiptWebhook(payload)) {
        return payload.data as CometChatDeliveryReceipt;
      }
      return null;
    } catch (error) {
      logger.error(`[CometChatWebhook] Error extracting delivery receipt data: ${error}`);
      return null;
    }
  }

  /**
   * Extract reaction data safely
   */
  private extractReactionData(payload: CometChatWebhookPayload): CometChatReactionData | null {
    try {
      if (this.isReactionWebhook(payload)) {
        return payload.data.reaction as CometChatReactionData;
      }
      return null;
    } catch (error) {
      logger.error(`[CometChatWebhook] Error extracting reaction data: ${error}`);
      return null;
    }
  }

  /**
   * Handle message sent events
   */
  private async handleMessageSent(payload: CometChatWebhookPayload): Promise<boolean> {
    try {
      const message = this.extractMessageData(payload);
      if (!message) {
        logger.warn(`[CometChatWebhook] handleMessageSent called but no message data found`);
        return false;
      }

      // Extract user types for event emission
      const senderUserType = this.extractUserType(message.data?.entities?.sender?.entity?.metadata);
      const receiverUserType = this.extractUserType(message.data?.entities?.receiver?.entity?.metadata);

      const eventData: MessageSentEvent = {
        messageId: message.id,
        senderUid: message.sender,
        senderUserType,
        receiverUid: message.receiver,
        receiverUserType,
        receiverType: message.receiverType,
        conversationId: message.conversationId,
        message: message.data?.text,
        messageType: message.type,
        sentAt: message.sentAt,
        metadata: message.data?.entities,
      };

      this.emitEvent('cometchat-message-sent', eventData);
      logger.info(`[CometChatWebhook] Message sent: ${message.id} from ${message.sender} to ${message.receiver}`);
      return true;
    } catch (error) {
      logger.error(`[CometChatWebhook] Error handling message sent: ${error}`);
      return false;
    }
  }

  /**
   * Handle message delivery receipt events
   */
  private async handleMessageDeliveryReceipt(payload: CometChatWebhookPayload): Promise<boolean> {
    try {
      const receipt = this.extractDeliveryReceiptData(payload);
      if (!receipt) {
        logger.warn(`[CometChatWebhook] handleMessageDeliveryReceipt called but no receipt data found`);
        return false;
      }

      const eventData: MessageDeliveredEvent = {
        messageId: receipt.body.messageId,
        receiverUid: receipt.receiver,
        deliveredAt: receipt.body.timestamp,
      };

      this.emitEvent('cometchat-message-delivered', eventData);
      logger.info(`[CometChatWebhook] Message delivery receipt: ${receipt.body.messageId} to ${receipt.receiver}, action: ${receipt.body.action}`);
      return true;
    } catch (error) {
      logger.error(`[CometChatWebhook] Error handling message delivery receipt: ${error}`);
      return false;
    }
  }

  /**
   * Handle message read events
   */
  private async handleMessageRead(payload: CometChatWebhookPayload): Promise<boolean> {
    try {
      const message = this.extractMessageData(payload);
      if (!message) {
        return false;
      }

      const eventData: MessageReadEvent = {
        messageId: message.id,
        readerUid: message.receiver,
        conversationId: message.conversationId,
        readAt: message.updatedAt,
      };

      this.emitEvent('cometchat-message-read', eventData);
      logger.info(`[CometChatWebhook] Message read: ${message.id} by ${message.receiver}`);
      return true;
    } catch (error) {
      logger.error(`[CometChatWebhook] Error handling message read: ${error}`);
      return false;
    }
  }

  /**
   * Handle message reaction added events
   */
  private async handleMessageReactionAdded(payload: CometChatWebhookPayload): Promise<boolean> {
    try {
      const reaction = this.extractReactionData(payload);
      if (!reaction) {
        logger.warn(`[CometChatWebhook] handleMessageReactionAdded called but no reaction data found`);
        return false;
      }

      // Extract user type for event emission
      const reactedByUserType = this.extractUserType(reaction.reactedBy);

      const eventData: MessageReactionEvent = {
        reactionId: reaction.id,
        messageId: reaction.messageId,
        reaction: reaction.reaction,
        reactedByUid: reaction.uid,
        reactedByUserType,
        reactedAt: reaction.reactedAt,
        conversationId: reaction.reactedOn.conversationId,
        messageText: reaction.reactedOn.data?.text,
        metadata: {
          reactedBy: reaction.reactedBy,
          reactedOn: reaction.reactedOn,
        },
      };

      this.emitEvent('cometchat-message-reaction-added', eventData);
      logger.info(`[CometChatWebhook] Message reaction added: ${reaction.reaction} on message ${reaction.messageId} by ${reaction.uid}`);
      return true;
    } catch (error) {
      logger.error(`[CometChatWebhook] Error handling message reaction added: ${error}`);
      return false;
    }
  }

  /**
   * Handle conversation created events
   */
  private async handleConversationCreated(payload: CometChatWebhookPayload): Promise<boolean> {
    try {
      const message = this.extractMessageData(payload);
      if (!message) {
        return false;
      }

      const eventData: ConversationEvent = {
        conversationId: message.conversationId,
        conversationType: message.receiverType,
        participants: [message.sender, message.receiver],
        createdAt: message.sentAt,
      };

      this.emitEvent('cometchat-conversation-created', eventData);
      logger.info(`[CometChatWebhook] Conversation created: ${message.conversationId}`);
      return true;
    } catch (error) {
      logger.error(`[CometChatWebhook] Error handling conversation created: ${error}`);
      return false;
    }
  }

  /**
   * Handle conversation updated events
   */
  private async handleConversationUpdated(payload: CometChatWebhookPayload): Promise<boolean> {
    try {
      const message = this.extractMessageData(payload);
      if (!message) {
        return false;
      }

      const eventData: ConversationEvent = {
        conversationId: message.conversationId,
        conversationType: message.receiverType,
        updatedAt: message.updatedAt,
        metadata: message.data?.entities,
      };

      this.emitEvent('cometchat-conversation-updated', eventData);
      logger.info(`[CometChatWebhook] Conversation updated: ${message.conversationId}`);
      return true;
    } catch (error) {
      logger.error(`[CometChatWebhook] Error handling conversation updated: ${error}`);
      return false;
    }
  }

  /**
   * Handle user online events
   */
  private async handleUserOnline(payload: CometChatWebhookPayload): Promise<boolean> {
    try {
      const message = this.extractMessageData(payload);
      if (!message) {
        return false;
      }

      const senderEntity = message.data?.entities?.sender?.entity;
      const eventData: UserStatusEvent = {
        userId: senderEntity?.uid || message.sender,
        lastActiveAt: senderEntity?.lastActiveAt,
        metadata: message.data?.entities,
      };

      this.emitEvent('cometchat-user-online', eventData);
      logger.info(`[CometChatWebhook] User online: ${eventData.userId}`);
      return true;
    } catch (error) {
      logger.error(`[CometChatWebhook] Error handling user online: ${error}`);
      return false;
    }
  }

  /**
   * Handle user offline events
   */
  private async handleUserOffline(payload: CometChatWebhookPayload): Promise<boolean> {
    try {
      const message = this.extractMessageData(payload);
      if (!message) {
        return false;
      }

      const senderEntity = message.data?.entities?.sender?.entity;
      const eventData: UserStatusEvent = {
        userId: senderEntity?.uid || message.sender,
        lastActiveAt: senderEntity?.lastActiveAt,
        metadata: message.data?.entities,
      };

      this.emitEvent('cometchat-user-offline', eventData);
      logger.info(`[CometChatWebhook] User offline: ${eventData.userId}`);
      return true;
    } catch (error) {
      logger.error(`[CometChatWebhook] Error handling user offline: ${error}`);
      return false;
    }
  }

  // Repository methods with connection pooling optimization
  async getWebhookLogById(logId: string): Promise<CometChatWebhookLog | undefined> {
    const conn = await pool.getConnection();
    try {
      return await this.webhookRepo.findWebhookLogById(conn, logId);
    } finally {
      conn.release();
    }
  }

  async getUnprocessedWebhookLogs(limit: number = 100): Promise<CometChatWebhookLog[]> {
    const conn = await pool.getConnection();
    try {
      return await this.webhookRepo.findUnprocessedWebhookLogs(conn, limit);
    } finally {
      conn.release();
    }
  }

  async getWebhookLogsBySenderUid(senderUid: string, limit: number = 50): Promise<CometChatWebhookLog[]> {
    const conn = await pool.getConnection();
    try {
      return await this.webhookRepo.findWebhookLogsBySenderUid(conn, senderUid, limit);
    } finally {
      conn.release();
    }
  }

  async getWebhookLogsByConversationId(conversationId: string, limit: number = 50): Promise<CometChatWebhookLog[]> {
    const conn = await pool.getConnection();
    try {
      return await this.webhookRepo.findWebhookLogsByConversationId(conn, conversationId, limit);
    } finally {
      conn.release();
    }
  }

  /**
   * Get webhook processing statistics
   */
  async getProcessingStats(): Promise<{ total: number; processed: number; failed: number; pending: number }> {
    const conn = await pool.getConnection();
    try {
      const unprocessed = await this.webhookRepo.findUnprocessedWebhookLogs(conn, 1000);
      const processed = await this.webhookRepo.findProcessedWebhookLogs(conn, 1000);

      return {
        total: unprocessed.length + processed.length,
        processed: processed.length,
        failed: unprocessed.filter(log => log.error).length,
        pending: unprocessed.filter(log => !log.error).length,
      };
    } finally {
      conn.release();
    }
  }

  /**
   * Get webhook logs by trigger type
   */
  async getWebhookLogsByTrigger(trigger: string, limit: number = 50): Promise<CometChatWebhookLog[]> {
    const conn = await pool.getConnection();
    try {
      return await this.webhookRepo.findWebhookLogsByTrigger(conn, trigger, limit);
    } finally {
      conn.release();
    }
  }

  /**
   * Get webhook logs by status (processed/unprocessed)
   */
  async getWebhookLogsByStatus(processed: boolean, limit: number = 50): Promise<CometChatWebhookLog[]> {
    const conn = await pool.getConnection();
    try {
      return await this.webhookRepo.findWebhookLogsByStatus(conn, processed, limit);
    } finally {
      conn.release();
    }
  }

  /**
   * Retry processing failed webhook logs
   */
  async retryFailedWebhooks(limit: number = 10): Promise<{ success: number; failed: number; errors: string[] }> {
    const conn = await pool.getConnection();
    try {
      const failedLogs = await this.webhookRepo.findFailedWebhookLogs(conn, limit);
      let success = 0;
      let failed = 0;
      const errors: string[] = [];

      for (const log of failedLogs) {
        try {
          const payload = JSON.parse(log.payload) as CometChatWebhookPayload;
          const result = await this.processWebhookByTrigger(payload);

          if (result.success) {
            await this.webhookRepo.updateWebhookLogProcessed(conn, log.id, true);
            success++;
          } else {
            await this.webhookRepo.updateWebhookLogProcessed(conn, log.id, false, result.error);
            failed++;
            errors.push(`Log ${log.id}: ${result.error}`);
          }
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : String(error);
          await this.webhookRepo.updateWebhookLogProcessed(conn, log.id, false, errorMsg);
          failed++;
          errors.push(`Log ${log.id}: ${errorMsg}`);
        }
      }

      return { success, failed, errors };
    } finally {
      conn.release();
    }
  }

  /**
   * Clean up old processed webhook logs
   */
  async cleanupOldLogs(daysToKeep: number = 30): Promise<{ deleted: number }> {
    const conn = await pool.getConnection();
    try {
      const deleted = await this.webhookRepo.deleteOldProcessedLogs(conn, daysToKeep);
      logger.info(`[CometChatWebhook] Cleaned up ${deleted} old webhook logs (older than ${daysToKeep} days)`);
      return { deleted };
    } finally {
      conn.release();
    }
  }

  /**
   * Get database connection with retry logic
   */
  private async getConnectionWithRetry(maxRetries: number = 3): Promise<any> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await pool.getConnection();
      } catch (error) {
        logger.warn(`[CometChatWebhook] Database connection attempt ${attempt} failed: ${error}`);
        if (attempt === maxRetries) {
          throw error;
        }
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
      }
    }
  }

  /**
   * Insert webhook log with retry logic
   */
  private async insertWebhookLogWithRetry(conn: any, webhookLog: CometChatWebhookLog, maxRetries: number = 3): Promise<string | undefined> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.webhookRepo.insertWebhookLog(conn, webhookLog);
      } catch (error) {
        logger.warn(`[CometChatWebhook] Insert webhook log attempt ${attempt} failed: ${error}`);
        if (attempt === maxRetries) {
          throw error;
        }
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 100));
      }
    }
  }

  /**
   * Process webhook asynchronously to avoid blocking the response
   */
  private async processWebhookAsync(payload: CometChatWebhookPayload, logId: string, conn: any): Promise<void> {
    try {
      // Get a new connection for async processing
      const asyncConn = await pool.getConnection();

      try {
        // Process webhook based on trigger
        const processed = await this.processWebhookByTrigger(payload);

        // Update log with processing result
        await this.webhookRepo.updateWebhookLogProcessed(asyncConn, logId, processed.success, processed.error);

        if (processed.success) {
          logger.info(`[CometChatWebhook] Successfully processed webhook: ${logId}`);
        } else {
          logger.warn(`[CometChatWebhook] Failed to process webhook: ${logId}, error: ${processed.error}`);
        }
      } finally {
        asyncConn.release();
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      logger.error(`[CometChatWebhook] Async processing error for log ${logId}: ${errorMsg}`);

      // Try to update the log with the error
      try {
        const errorConn = await pool.getConnection();
        try {
          await this.webhookRepo.updateWebhookLogProcessed(errorConn, logId, false, errorMsg);
        } finally {
          errorConn.release();
        }
      } catch (updateError) {
        logger.error(`[CometChatWebhook] Failed to update webhook log with async error: ${updateError}`);
      }
    }
  }
}