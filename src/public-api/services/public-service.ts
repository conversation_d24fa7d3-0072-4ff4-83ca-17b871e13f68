import { SearchStudio, SearchInstructor, LatLng } from "@booking-api/models/search";
import { Instructor, InstructorEquipment } from "@instructor-api/models/instructor";
import { InstructorFindCriteria, StudioFindCriteria, Feedback, SessionFeedback, Terms, PrivacyPolicy, Country } from "@public-api/models/public";
import { PublicRepo } from "@public-api/repo/public-repo";
import { SearchRepo } from "@booking-api/repo/search-repo";
import { Page } from "@shared/services";
import { Studio, StudioEquipment } from "@studio-cms/models/studio";
import pool from "../../init-pool";
import { StudioRepo } from "@studio-cms/repo/studio-repo";
import { InstructorRepo } from "@instructor-api/repo/instructor-repo";
import { DateTime } from "luxon";
import logger from "@utils/logger";

export class PublicService {

  private publicRepo = new PublicRepo();
  private searchRepo = new SearchRepo();
  private studioRepo = new StudioRepo();
  private instructorRepo = new InstructorRepo();

  async findCountryByCode(code: string): Promise<Country|undefined> {
    return this.publicRepo.findCountryByCode(pool, code);
  }

  async studios(
    criteria: StudioFindCriteria,
    page?: Page,
  ): Promise<Studio[]> {

    const filterQuery = this.publicRepo.findMatchedStudioQuery(pool, criteria);

    // cache distance with filtered studio ids
    criteria.latLng && await this.searchRepo
      .cacheDistanceToStudiosBatch(pool, criteria.latLng, filterQuery);

    const ret = await this.publicRepo.findStudios(pool, criteria, filterQuery, page);
    for (const o of ret) {
      o.pictures = await this.studioRepo.prefixStudioPictures(pool, o.pictures);
    }

    let studioIds = ret.map(studio => studio.id);
    const studioRating = await this.publicRepo.getStudioRatingByIds(pool, studioIds);

    const ratingsMap = new Map<string, { rating: number; total: number }>();
    studioRating.forEach(({ id, rating, total }) => {
        ratingsMap.set(id, { rating, total });
    });

    const equipments = await this.publicRepo.getStudioEquipmentTypeByIds(pool, studioIds);
    const equipmentsMap = new Map<string, StudioEquipment[]>();

    // Group equipments by studioId
    equipments.forEach(({ studioId, typeId, typeName }) => {
        if (!equipmentsMap.has(studioId)) {
            equipmentsMap.set(studioId, []);
        }
        equipmentsMap.get(studioId)?.push({ studioId, typeId, typeName });
    });

    let favStudios = [];
    criteria.byId && (favStudios = await this.publicRepo.getFaveStudiosByUserId(pool, criteria.byId));

    for (const studio of ret) {
        const ratingInfo = ratingsMap.get(studio.id);
        studio.avgRating = ratingInfo?.rating ?? 0;
        studio.totalRatingCount = ratingInfo?.total ?? 0;
        studio.equipments = equipmentsMap.get(studio.id) || [];
        studio.fav = favStudios.includes(studio.id);
    }
    return ret;
  }

  async studioById(id: string, latLng: LatLng = null, uid: string = null): Promise<Studio|undefined> {
    const ret = await this.publicRepo.findStudioById(pool, id);
    if (ret) {
      ret.pictures = await this.studioRepo.prefixStudioPictures(pool, ret.pictures);
      const studioRating = await this.publicRepo.getStudioRatingByIds(pool, [id]);
      if(studioRating[0]) {
        ret.avgRating = studioRating[0].rating ?? 0;
        ret.totalRatingCount = studioRating[0].total ?? 0;
      }

      const equipments = await this.publicRepo.getStudioEquipmentTypeByIds(pool, [id]);
      ret.equipments = equipments || [];

      if(latLng && ret.latlng) {
        ret.distance = await this.publicRepo.findDistance(pool, latLng, ret.latlng);
      }

      if(uid) {
        const favStudios = await this.publicRepo.getFaveStudiosByUserId(pool, uid, [ret.id]);
        ret.fav = favStudios.includes(ret.id);
      }
    }
    return ret;
  }

  async studioFeedbackById(
    id: string,
    page?: Page,
  ): Promise<Feedback> {

    const studioRatings = await this.publicRepo.getStudioRatingByIds(pool, [id]);
    const sessionFeedback = await this.publicRepo.getStudioFeedbackById(pool, id, page);
    const ratingGroup = await this.publicRepo.getStudioRatingGroupingById(pool, id);
    return {
      avgRating: studioRatings[0]?.rating ?? 0,
      totalRating: studioRatings[0]?.total ?? 0,
      feedback: sessionFeedback ?? [],
      groupsRating: ratingGroup ?? [],
    } as Feedback;
  }

  async instructors(
    criteria: InstructorFindCriteria,
    page?: Page,
  ): Promise<Instructor[]> {
    const ret = await this.publicRepo.findInstructors(pool, criteria, page);
    for (const o of ret) {
      o.pictures = await this.instructorRepo.prefixInstructorPictures(pool, o.pictures);
    }

    let instrIds = ret.map(instructor => instructor.id);
    const instrRatings = await this.publicRepo.getInstructorsRatingByIds(pool, instrIds);

    const equipments = await this.publicRepo.getInstructorEquipmentTypeByIds(pool, instrIds);
    const equipmentsMap = new Map<string, InstructorEquipment[]>();

    // Group equipments by instrId
    equipments.forEach(({ instructorId, typeId, typeName }) => {
        if (!equipmentsMap.has(instructorId)) {
            equipmentsMap.set(instructorId, []);
        }
        equipmentsMap.get(instructorId)?.push({ instructorId, typeId, typeName });
    });

    const ratingsMap = new Map<string, { rating: number; total: number }>();
    instrRatings.forEach(({ id, rating, total }) => {
        ratingsMap.set(id, { rating, total });
    });

    let favInstrs = [];
    criteria.byId && (favInstrs = await this.publicRepo.getFaveInstructorsByUserId(pool, criteria.byId));

    ret.forEach((instructor) => {
        const ratingInfo = ratingsMap.get(instructor.id);
        instructor.avgRating = ratingInfo?.rating ?? 0;
        instructor.totalRatingCount = ratingInfo?.total ?? 0;
        instructor.equipments = equipmentsMap.get(instructor.id) || [];
        instructor.fav = favInstrs.includes(instructor.id);
    });
    return ret;
  }

  async instructorById(id: string, uid: string = null): Promise<Instructor|undefined> {
    const ret = await this.publicRepo.findInstructorById(pool, id);
    if (ret) {
      ret.pictures = await this.instructorRepo.prefixInstructorPictures(pool, ret.pictures);
      const instrRatings = await this.publicRepo.getInstructorsRatingByIds(pool, [id]);
      if(instrRatings[0]) {
        ret.avgRating = instrRatings[0].rating ?? 0;
        ret.totalRatingCount = instrRatings[0].total ?? 0;
      }
      const equipments = await this.publicRepo.getInstructorEquipmentTypeByIds(pool, [id]);
      ret.equipments = equipments || [];

      if(uid) {
        const favInstrs = await this.publicRepo.getFaveInstructorsByUserId(pool, uid, [ret.id]);
        ret.fav = favInstrs.includes(ret.id);
      }
    };
    return ret;
  }

  async instructorFeedbackById(
    id: string,
    page?: Page,
  ): Promise<Feedback> {

    const instrRatings = await this.publicRepo.getInstructorsRatingByIds(pool, [id]);
    const sessionFeedback = await this.publicRepo.getInstructorFeedbackById(pool, id, page);
    const ratingGroup = await this.publicRepo.getInstructorRatingGroupingById(pool, id);
    return {
      avgRating: instrRatings[0]?.rating ?? 0,
      totalRating: instrRatings[0]?.total ?? 0,
      feedback: sessionFeedback ?? [],
      groupsRating: ratingGroup ?? [],
    } as Feedback;
  }

  async findDistance(latLngFrom: LatLng, latLngTo: LatLng,
  ): Promise<number|undefined> {
    const ret = await this.publicRepo.findDistance(pool, latLngFrom, latLngTo);
    return ret;
  }

  async findLatestTermsAndConditions(): Promise<Terms|undefined> {
    return await this.publicRepo.findLatestTermsAndConditions(pool);
  }

  async findTermsAndConditionsByVersion(version: number): Promise<Terms|undefined> {
    return await this.publicRepo.findTermsAndConditionsByVersion(pool, version);
  }

  async findLatestPrivacyPolicy(): Promise<PrivacyPolicy|undefined> {
    return await this.publicRepo.findLatestPrivacyPolicy(pool);
  }

  async findPrivacyPolicyByVersion(version: number): Promise<PrivacyPolicy|undefined> {
    return await this.publicRepo.findPrivacyPolicyByVersion(pool, version);
  }

  async insertTutorialFeedback(bookingRef: string, video: string, rating: number, feedback: string): Promise<{ success: boolean, message: string }> {
    // 1. Find booking by booking_ref and check date is within allowed window in booking's timezone
    const booking = await this.publicRepo.findBookingByRef(pool, bookingRef);
    if (!booking) {
      return { success: false, message: "Booking not found" };
    }
    const timezone = booking.timezone || "Asia/Singapore";
    const country = booking.country_code || "SG";

    const bookingStart = DateTime.fromJSDate(booking.start_at, { zone: timezone });
    const bookingEnd = DateTime.fromJSDate(booking.end_at, { zone: timezone });
    logger.info(`bookingStart=${bookingStart}, bookingEnd=${bookingEnd}`);
    // Allow feedback from 30 minutes before start to 30 minutes after end
    const windowStart = bookingStart.minus({ minutes: 30 });
    const windowEnd = bookingEnd.plus({ minutes: 30 });

    const nowInBookingTz = DateTime.now().setZone(timezone);
    logger.info(`nowInBookingTz=${nowInBookingTz}`);
    if (nowInBookingTz < windowStart || nowInBookingTz > windowEnd) {
      return { success: false, message: "Feedback can only be submitted from 30 minutes before class starts to 30 minutes after it ends." };
    }
    // 2. Insert feedback into evt04tutorial_feedback
    const insertedId = await this.publicRepo.insertTutorialFeedback(pool, bookingRef, booking.member_id, video, rating, feedback, 'tablet-app');
    if (insertedId > 0) {
      return { success: true, message: "Feedback submitted" };
    } else {
      return { success: false, message: "Failed to submit feedback" };
    }
  }
}
