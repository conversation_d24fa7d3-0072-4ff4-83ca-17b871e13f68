import { Connection, RowDataPacket } from "mysql2/promise";

export class MessageRepo {

  async findMessageByCode(
    conn: Connection,
    code: string,
  ): Promise<String|undefined> {
    const sql = "select * from app01messages where `code` = ?";
    const params = [code];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return results[0].value;
  }
}
