import { Instructor, InstructorEquipment } from "@instructor-api/models/instructor";
import { InstructorRepo } from "@instructor-api/repo/instructor-repo";
import { InstructorFindCriteria, StudioFindCriteria, SessionFeedback, GroupRating, Terms, PrivacyPolicy, Country } from "@public-api/models/public";
import { Page } from "@shared/services";
import { Studio, StudioEquipment } from "@studio-cms/models/studio";
import { StudioRepo } from "@studio-cms/repo/studio-repo";
import { Connection, RowDataPacket, ResultSetHeader } from "mysql2/promise";
import { LatLng } from "@booking-api/models/search";
import { DateTime } from "luxon";

export class PublicRepo {

  // studioIdsIn
  // faveStudioOnly (and !!byId)
  // instrless
  // equipTypes
  // deleted
  findMatchedStudioQuery(
    conn: Connection,
    criteria: StudioFindCriteria,
    studioIdsIn: string[] = [],
  ): string {

    const clauses = [];
    const params = [];
    if (criteria.id) {
      studioIdsIn.push(criteria.id);
    }

    if (studioIdsIn.length) {
      const qMarks = Array(studioIdsIn.length).fill("?").join(",")
      clauses.push(`(sub_s.id in (${qMarks}))`);
      params.push(...studioIdsIn);
    }

    if (criteria.searchTerm) {
      clauses.push("("
        + "(sub_s.name like ?)"
        + " or (sub_s.descr like ?)"
        + ")");
      for (let i = 0; i < 2; i++) {
        params.push(`%${criteria.searchTerm}%`);
      }
    }
    if (criteria.faveStudioOnly && criteria.byId) {
      clauses.push(`(sub_s.id in (
          select sub_fs.studio_id from usr02fave_studios sub_fs
          where sub_fs.user_id = ?
        ))`);
      params.push(criteria.byId);
    }
    if (criteria.preferredStudioFromFaveInstrOnly && criteria.byId) {
      clauses.push(`(id in (
          select studio_id from usr03fave_instrs fi INNER JOIN instr05pref_studios ps ON fi.instr_id = ps.user_id where fi.user_id = ?
        ))`);
      params.push(criteria.byId);
    }
    if (criteria.instrLessOnly) {
      clauses.push("(sub_s.instrless is true)");
    }
    if (criteria.equipTypes && criteria.equipTypes.length > 0) {
      const qMarks = Array(criteria.equipTypes.length).fill("?").join(",");
      clauses.push(`(sub_s.id in (
          select vw_e.studio_id from vw_stu04equipment vw_e
          where vw_e.type_id in (${qMarks}) and vw_e.deleted is false
        ))`);
      params.push(...criteria.equipTypes);
    }
    { // active
      clauses.push("(sub_s.active is true)");
    }
    if (!criteria.inclDeleted) {
      clauses.push("(sub_s.deleted is false)");
    }

    let sql = `select sub_s.id from pub01studios sub_s`;
    if (clauses.length !== 0) {
      sql += ` where ${clauses.join(" and ")}`;
    }

    return conn.format(sql, params);
  }

  async findStudios(
    conn: Connection,
    criteria: StudioFindCriteria,
    studioQuery: string|undefined,
    page?: Page,
  ): Promise<Studio[]> {
    // prepare clauses and params
    const columns = ["s.*"];
    const clauses = [];
    const params = [];
    const joins = [];
    let order = "";
    if (studioQuery) {
      clauses.push(`(id IN (${studioQuery}))`);
    }
    // distance within
    if(criteria.latLng) {
      if(criteria.distance) {
        clauses.push(`(d.latlng_from is null or (d.latlng_from = ? and d.distance <= ?))`);
        params.push(...[
          criteria.latLng.toCacheString(),
          criteria.distance,
        ]);
      }

      joins.push(` left join app03dist_cache d on s.latlng = d.latlng_to and d.latlng_from = '${criteria.latLng.toCacheString()}'`);
      columns.push('d.distance');
      order = " order by d.distance asc";
    }

    // select items
    let selectSql = `select ${columns.join(", ")} from pub01studios s`;
    if (joins.length !== 0) {
      selectSql += `${joins.join(" ")}`
    }
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }
    selectSql += order;
    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    return results.map(o => StudioRepo.toStudio(o));

    /* unused
    // select total
    let totalSql = "select count(*) as `count` from pub01studios";
    if (clauses.length !== 0) {
      totalSql += ` where ${clauses.join(" and ")}`
    }
    const [results2] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = results2[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;

    // return page of data
    return {
      totalItems: totalItems,
      totalPages: totalPages,
      items: items,
    } as PageOfData<Studio>;
    */
  }

  async findStudioById(
    conn: Connection, id: string,
  ): Promise<Studio|undefined> {
    const sql = "select * from pub01studios where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return StudioRepo.toStudio(results[0]);
  }

  async getStudioRatingByIds(
    conn: Connection, studioIds: string[],
  ): Promise<{ id: string; rating: number, total: number }[]> {
    if (studioIds.length === 0) {
      return []; // nothing else to do
    }
    const clauses = [];
    const params = [];
    if (studioIds) {
      const qMarks = Array(studioIds.length).fill("?").join(",")
      clauses.push(`(e.studio_id in (${qMarks}))`);
      params.push(...studioIds);
    }
    { // is completed session, member given feedback and have rating
      clauses.push("(s.completed is true)");
      clauses.push("(s.m_has_feedback is true)");
      clauses.push("(s.m_studio_rating <> 0)");
    }

    let sql = `SELECT e.studio_id, AVG(s.m_studio_rating) AS rating, count(e.studio_id) as total FROM evt02sessions s INNER JOIN evt01events e ON e.id = s.event_id`;
    if (clauses.length !== 0) {
      sql += ` WHERE ${clauses.join(" and ")}`;
    }

    sql += `GROUP BY e.studio_id`;
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(row => ({
        id: row.studio_id,
        rating: +row.rating || 0,
        total: +row.total || 0,
    }));
  }

  async getStudioFeedbackById(
    conn: Connection, id: string, page?: Page,
  ): Promise<SessionFeedback[]|undefined> {
    let selectSql = "select s.event_id, e.studio_id, m_has_feedback, m_studio_rating, m_studio_feedback, m_feedback_at from evt02sessions s INNER JOIN evt01events e ON e.id = s.event_id";
    const clauses = [];
    const params = [];
    clauses.push("(e.studio_id = ?)");
    params.push(id);
    { // is completed session, member given feedback and have rating
      clauses.push("(s.completed is true)");
      clauses.push("(s.m_has_feedback is true)");
    }

    if (clauses.length !== 0) {
      selectSql += ` WHERE ${clauses.join(" and ")}`;
    }

    selectSql += " order by m_feedback_at desc";

    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    return results.map(o => StudioRepo.toSessionFeedback(o));
  }

  async getStudioRatingGroupingById(
    conn: Connection, id: string
  ): Promise<GroupRating[]|undefined> {
    let selectSql = "SELECT s.m_studio_rating AS rating, count(s.m_studio_rating) as totalRating FROM evt02sessions s INNER JOIN evt01events e ON e.id = s.event_id";
    const clauses = [];
    const params = [];
    clauses.push("(e.studio_id = ?)");
    params.push(id);
    { // is completed session, member given feedback and have rating
      clauses.push("(s.completed is true)");
      clauses.push("(s.m_has_feedback is true)");
      clauses.push("(s.m_studio_rating <> 0)");
    }

    if (clauses.length !== 0) {
      selectSql += ` WHERE ${clauses.join(" AND ")}`;
    }

    selectSql += " GROUP BY s.m_studio_rating ORDER BY s.m_studio_rating";

    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    return results.map(o => ({
      rating: o.rating,
      totalRating: o.totalRating
    } as GroupRating));
  }

  async getStudioEquipmentTypeByIds(
    conn: Connection,
    studioIds: string[],
  ): Promise<StudioEquipment[]> {
    if (studioIds.length == 0) {
      return []; // no need to continue
    }
    const qMarks = Array(studioIds.length).fill("?").join(",")
    const sql = `select studio_id, type_id, type_name from vw_stu04equipment where studio_id in (${qMarks}) and deleted is false group by studio_id, type_id`;
    const params = [...studioIds];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => StudioRepo.toEquipment(o));
  }

  async getFaveStudiosByUserId(
    conn: Connection, userId: string, studioIds: string[] = []
  ): Promise<string[]> {
    let sql = `select studio_id from usr02fave_studios
      where user_id = ?`;
    const params = [userId];
    if(studioIds.length) {
      const qMarks = Array(studioIds.length).fill("?").join(",")
      sql += ` and studio_id in (${qMarks})`;
      params.push(...studioIds);
    }
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map( row => row['studio_id']);
  }

  async findInstructors(
    conn: Connection,
    criteria: InstructorFindCriteria,
    page?: Page,
  ): Promise<Instructor[]> {
    // prepare clauses and params
    const clauses = [];
    const params = [];
    if (criteria.id) {
      clauses.push("(id = ?)");
      params.push(criteria.id);
    }
    if (!criteria.inclDeleted) {
      clauses.push("(deleted is false)");
    }
    if (criteria.searchTerm) {
      clauses.push("("
        + "(name like ?)"
        + " or (descr like ?)"
        + ")");
      for (let i = 0; i < 2; i++) {
        params.push(`%${criteria.searchTerm}%`);
      }
    }
    if (criteria.faveInstrOnly && criteria.byId) {
      clauses.push(`(id in (
          select instr_id from usr03fave_instrs
          where user_id = ?
        ))`);
      params.push(criteria.byId);
    }
    if (criteria.equipTypes && criteria.equipTypes.length > 0) {
      const qMarks = Array(criteria.equipTypes.length).fill("?").join(",");
      clauses.push(`(exists (
          select i08e.instr_id from instr08equipment i08e
          where i08e.type_id in (${qMarks}) and i08e.instr_id = id
        ))`);
      params.push(...criteria.equipTypes);
    }
    { // onboarded
      clauses.push("(onboarded is true)");
    }

    // select items
    let selectSql = "select * from pub02instructors";
    if (clauses.length !== 0) {
      selectSql += ` where ${clauses.join(" and ")}`
    }
    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    const [results1] = await conn.query<RowDataPacket[]>(selectSql, params);
    return results1.map(o => InstructorRepo.toInstructor(o));

    /* unused
    // select total
    let totalSql = "select count(*) as `count` from pub02instructors";
    if (clauses.length !== 0) {
      totalSql += ` where ${clauses.join(" and ")}`
    }
    const [results2] = await conn.query<RowDataPacket[]>(totalSql, params);
    const totalItems = results2[0].count;
    const totalPages = page ? Math.ceil(totalItems / page.size) : 1;

    // return page of data
    return {
      totalItems: totalItems,
      totalPages: totalPages,
      items: items,
    } as PageOfData<Instructor>;
    */
  }

  async findInstructorById(
    conn: Connection, id: string,
  ): Promise<Instructor|undefined> {
    const sql = "select * from pub02instructors where id = ?";
    const params = [id];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return InstructorRepo.toInstructor(results[0]);
  }

  async getInstructorsRatingByIds(
    conn: Connection, instrIdsIn: string[],
  ): Promise<{ id: string; rating: number, total: number }[]> {
    if (instrIdsIn.length === 0) {
      return []; // nothing else to do
    }
    const clauses = [];
    const params = [];
    if (instrIdsIn) {
      const qMarks = Array(instrIdsIn.length).fill("?").join(",")
      clauses.push(`(s.instr_id in (${qMarks}))`);
      params.push(...instrIdsIn);
    }
    { // is completed session, member given feedback and have rating
      clauses.push("(s.completed is true)");
      clauses.push("(s.m_has_feedback is true)");
      clauses.push("(s.m_instructor_rating <> 0)");
    }

    let sql = `SELECT s.instr_id, AVG(s.m_instructor_rating) AS rating, count(s.instr_id) as total FROM evt02sessions s`;
    if (clauses.length !== 0) {
      sql += ` WHERE ${clauses.join(" and ")}`;
    }

    sql += `GROUP BY s.instr_id`;
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(row => ({
        id: row.instr_id,
        rating: row.rating || 0,
        total: row.total || 0,
    }));
  }

  async getInstructorFeedbackById(
    conn: Connection, id: string, page?: Page,
  ): Promise<SessionFeedback[]|undefined> {
    let selectSql = "select event_id, instr_id, m_has_feedback, m_instructor_rating, m_instructor_feedback, m_feedback_at from evt02sessions";
    const clauses = [];
    const params = [];
    clauses.push("(instr_id = ?)");
    params.push(id);
    { // member given feedback and have rating
      clauses.push("(m_has_feedback is true)");
    }

    if (clauses.length !== 0) {
      selectSql += ` WHERE ${clauses.join(" and ")}`;
    }

    selectSql += " order by m_feedback_at desc";

    if (page) {
      selectSql += " limit " + page.toZeroBasedOffset() + "," + page.size;
    }
    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    return results.map(o => InstructorRepo.toSessionFeedback(o));
  }

  async getInstructorRatingGroupingById(
    conn: Connection, id: string
  ): Promise<GroupRating[]|undefined> {
    let selectSql = "select m_instructor_rating AS rating, count(m_instructor_rating) AS totalRating from evt02sessions";
    const clauses = [];
    const params = [];
    clauses.push("(instr_id = ?)");
    params.push(id);
    { // is completed session, member given feedback and have rating
      clauses.push("(completed is true)");
      clauses.push("(m_has_feedback is true)");
      clauses.push("(m_instructor_rating <> 0)");
    }

    if (clauses.length !== 0) {
      selectSql += ` WHERE ${clauses.join(" and ")}`;
    }

    selectSql += " group by m_instructor_rating order by m_instructor_rating";

    const [results] = await conn.query<RowDataPacket[]>(selectSql, params);
    return results.map(o => ({
      rating: o.rating,
      totalRating: o.totalRating
    } as GroupRating));
  }

  async findDistance(
    conn: Connection, latLngFrom: LatLng, latLngTo: LatLng,
  ): Promise<number|undefined> {
    const sql = "select distance from app03dist_cache where latlng_from = ? and latlng_to = ? ";
    const params = [
      latLngFrom.toCacheString(),
      latLngTo.toCacheString(),
    ];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length == 0) {
      return undefined;
    }
    return results[0]['distance'];
  }

  async getInstructorEquipmentTypeByIds(
    conn: Connection,
    instrIds: string[],
  ): Promise<InstructorEquipment[]> {
    if (instrIds.length == 0) {
      return []; // no need to continue
    }
    const qMarks = Array(instrIds.length).fill("?").join(",")
    const sql = `select ie.instr_id ,ie.type_id, et.name as type_name from instr08equipment ie inner join stu03equip_types et on ie.type_id = et.id where ie.instr_id in (${qMarks}) and et.deleted is false group by ie.instr_id ,ie.type_id`;
    const params = [...instrIds];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map(o => InstructorRepo.toEquipment(o));
  }

  async getFaveInstructorsByUserId(
    conn: Connection, userId: string, instrIds: string[] = []
  ): Promise<string[]> {
    let sql = `select instr_id from usr03fave_instrs
      where user_id = ?`;
    const params = [userId];
    if(instrIds.length) {
      const qMarks = Array(instrIds.length).fill("?").join(",")
      sql += ` and instr_id in (${qMarks})`;
      params.push(...instrIds);
    }
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    return results.map( row => row['instr_id']);
  }

  async findLatestTermsAndConditions(conn: Connection): Promise<Terms|undefined> {
    const sql = `
        SELECT version, content, created_at as createdAt
        FROM app06terms
        WHERE deleted = false
        ORDER BY version DESC
        LIMIT 1
    `;
    const [results] = await conn.query<RowDataPacket[]>(sql);
    if (results.length === 0) {
        return undefined;
    }
    return this.toTerms(results[0]);
  }

  async findTermsAndConditionsByVersion(conn: Connection, version: number): Promise<Terms|undefined> {
    const sql = `
        SELECT version, content, created_at as createdAt
        FROM app06terms
        WHERE version = ?
    `;
    const params = [version];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    if (results.length === 0) {
        return undefined;
    }
    return this.toTerms(results[0]);
  }

  async findLatestPrivacyPolicy(conn: Connection): Promise<PrivacyPolicy|undefined> {
    const sql = `
        SELECT version, content, created_at as createdAt
        FROM app08privacy_policy
        WHERE deleted = false
        ORDER BY version DESC
        LIMIT 1
    `;
    const [results] = await conn.query<RowDataPacket[]>(sql);
    if (results.length === 0) {
        return undefined;
    }
    return this.toPrivacyPolicy(results[0]);
  }

  async findPrivacyPolicyByVersion(conn: Connection, version: number): Promise<PrivacyPolicy|undefined> {

    const sql = `
        SELECT version, content, created_at as createdAt
        FROM app08privacy_policy
        WHERE version = ?
    `;
    const params = [version];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    if (results.length === 0) {
        return undefined;
    }
    return this.toPrivacyPolicy(results[0]);
  }

  private toTerms(row: any): Terms {
    return {
        version: row['version'],
        content: row['content'],
        createdAt: DateTime.fromFormat(row['createdAt'], 'yyyy-MM-dd HH:mm:ss', { zone: 'UTC' }).toFormat('yyyy-MM-dd')
    };
  }

  private toPrivacyPolicy(row: any): PrivacyPolicy {
      return {
          version: row['version'],
          content: row['content'],
          createdAt: DateTime.fromFormat(row['createdAt'], 'yyyy-MM-dd HH:mm:ss', { zone: 'UTC' }).toFormat('yyyy-MM-dd')
      };
  }

  async findBookingByRef(
    conn: Connection, bookingRef: string
  ): Promise<{ id: string, start_date: Date, end_date: Date, start_at: Date, end_at: Date, member_id: string, country_code: string, timezone: string }|undefined> {
    const sql = `SELECT id, start_date, end_date, start_at, end_at, member_id, country_code, timezone FROM evt01events WHERE booking_ref = ? AND type = 'Booking' AND deleted = 0 LIMIT 1`;
    const params = [bookingRef];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    if (results.length === 0) return undefined;
    const row = results[0];
    return {
      id: row.id,
      start_date: row.start_date instanceof Date ? row.start_date : new Date(row.start_date),
      end_date: row.end_date instanceof Date ? row.end_date : new Date(row.end_date),
      start_at: row.start_at instanceof Date ? row.start_at : new Date(row.start_at),
      end_at: row.end_at instanceof Date ? row.end_at : new Date(row.end_at),
      member_id: row.member_id,
      country_code: row.country_code || 'SG',
      timezone: row.timezone || 'Asia/Singapore',
    };
  }

  async insertTutorialFeedback(
    conn: Connection,
    bookingRef: string,
    memberId: string,
    video: string,
    rating: number,
    feedback: string,
    createdBy?: string
  ): Promise<number> {
    const sql = `INSERT INTO evt04tutorial_feedback (booking_ref, member_id, video, rating, feedback, created_by) VALUES (?, ?, ?, ?, ?, ?)`;
    const params = [bookingRef, memberId, video, rating, feedback, createdBy || null];
    const [result] = await conn.execute<ResultSetHeader>(sql, params);
    return result.insertId;
  }

  async findCountryByCode(conn: Connection, code: string): Promise<Country|undefined> {
    const sql = `SELECT code, name, timezone, currency, currency_name, symbol FROM app07country_info WHERE code = ?`;
    const params = [code];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);
    if (results.length === 0) return undefined;
    const row = results[0];
    return {
      code: row.code,
      name: row.name,
      timezone: row.timezone,
      currency: row.currency,
      currency_name: row.currency_name,
      symbol: row.symbol,
    };
  }
}
