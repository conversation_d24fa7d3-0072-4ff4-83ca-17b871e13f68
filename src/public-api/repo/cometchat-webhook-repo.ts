import { Connection, ResultSetHeader, RowDataPacket } from "mysql2/promise";
import { CometChatWebhookLog } from "@public-api/models/cometchat";
import crypto from "crypto";

export class CometChatWebhookRepo {

  async insertWebhookLog(conn: Connection, log: CometChatWebhookLog): Promise<string> {
    const sql = `INSERT INTO cometchat01webhook_log (
        id, \`trigger\`, category, message_id, sender_uid, sender_user_type, receiver_uid, receiver_user_type, receiver_type,
        conversation_id, message_type, message_text, action, payload, processed, error
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    const uuid = crypto.randomUUID();
    const params = [
      uuid,
      log.trigger,
      log.category || null,
      log.messageId || null,
      log.senderUid || null,
      log.senderUserType || null,
      log.receiverUid || null,
      log.receiverUserType || null,
      log.receiverType || null,
      log.conversationId || null,
      log.messageType || null,
      log.messageText || null,
      log.action || null,
      log.payload,
      log.processed,
      log.error || null,
    ];

    const [results] = await conn.execute<ResultSetHeader>(sql, params);
    return uuid;
  }

  async updateWebhookLogProcessed(conn: Connection, logId: string, processed: boolean, error?: string): Promise<void> {
    const sql = `UPDATE cometchat01webhook_log
      SET processed = ?, processed_at = NOW(), error = ?
      WHERE id = ?`;
    const params = [processed, error || null, logId];
    await conn.execute(sql, params);
  }

  async findWebhookLogById(conn: Connection, logId: string): Promise<CometChatWebhookLog | undefined> {
    const sql = `SELECT * FROM cometchat01webhook_log WHERE id = ?`;
    const params = [logId];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    if (results.length === 0) {
      return undefined;
    }

    return this.toWebhookLog(results[0]);
  }

  async findUnprocessedWebhookLogs(conn: Connection, limit: number = 100): Promise<CometChatWebhookLog[]> {
    const sql = `SELECT * FROM cometchat01webhook_log
      WHERE processed = false
      ORDER BY created_at ASC
      LIMIT ?`;
    const params = [limit];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    return results.map(row => this.toWebhookLog(row));
  }

  async findWebhookLogsBySenderUid(conn: Connection, senderUid: string, limit: number = 50): Promise<CometChatWebhookLog[]> {
    const sql = `SELECT * FROM cometchat01webhook_log
      WHERE sender_uid = ?
      ORDER BY created_at DESC
      LIMIT ?`;
    const params = [senderUid, limit];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    return results.map(row => this.toWebhookLog(row));
  }

  async findWebhookLogsByConversationId(conn: Connection, conversationId: string, limit: number = 50): Promise<CometChatWebhookLog[]> {
    const sql = `SELECT * FROM cometchat01webhook_log
      WHERE conversation_id = ?
      ORDER BY created_at DESC
      LIMIT ?`;
    const params = [conversationId, limit];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    return results.map(row => this.toWebhookLog(row));
  }

  async findProcessedWebhookLogs(conn: Connection, limit: number = 100): Promise<CometChatWebhookLog[]> {
    const sql = `SELECT * FROM cometchat01webhook_log
      WHERE processed = true
      ORDER BY processed_at DESC
      LIMIT ?`;
    const params = [limit];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    return results.map(row => this.toWebhookLog(row));
  }

  async findWebhookLogsByTrigger(conn: Connection, trigger: string, limit: number = 50): Promise<CometChatWebhookLog[]> {
    const sql = `SELECT * FROM cometchat01webhook_log
      WHERE \`trigger\` = ?
      ORDER BY created_at DESC
      LIMIT ?`;
    const params = [trigger, limit];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    return results.map(row => this.toWebhookLog(row));
  }

  async findWebhookLogsByStatus(conn: Connection, processed: boolean, limit: number = 50): Promise<CometChatWebhookLog[]> {
    const sql = `SELECT * FROM cometchat01webhook_log
      WHERE processed = ?
      ORDER BY created_at DESC
      LIMIT ?`;
    const params = [processed, limit];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    return results.map(row => this.toWebhookLog(row));
  }

  async findFailedWebhookLogs(conn: Connection, limit: number = 50): Promise<CometChatWebhookLog[]> {
    const sql = `SELECT * FROM cometchat01webhook_log
      WHERE processed = false AND error IS NOT NULL
      ORDER BY created_at ASC
      LIMIT ?`;
    const params = [limit];
    const [results] = await conn.query<RowDataPacket[]>(sql, params);

    return results.map(row => this.toWebhookLog(row));
  }

  async deleteOldProcessedLogs(conn: Connection, daysToKeep: number = 30): Promise<number> {
    const sql = `DELETE FROM cometchat01webhook_log
      WHERE processed = true 
      AND processed_at < DATE_SUB(NOW(), INTERVAL ? DAY)`;
    const params = [daysToKeep];
    const [results] = await conn.execute<ResultSetHeader>(sql, params);

    return results.affectedRows;
  }

  private toWebhookLog(row: any): CometChatWebhookLog {
    return {
      id: row.id,
      trigger: row.trigger,
      category: row.category,
      messageId: row.message_id,
      senderUid: row.sender_uid,
      senderUserType: row.sender_user_type,
      receiverUid: row.receiver_uid,
      receiverUserType: row.receiver_user_type,
      receiverType: row.receiver_type,
      conversationId: row.conversation_id,
      messageType: row.message_type,
      messageText: row.message_text,
      action: row.action,
      payload: row.payload,
      processed: row.processed,
      error: row.error,
      createdAt: row.created_at,
      processedAt: row.processed_at,
    };
  }
}