const UPPERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
const DIGITS = "0123456789";

function randomString(len: number, chars: string) {
  if (!Number.isInteger(len)) throw "len must be an integer";
  if (len < 0) throw "len must be >= 0";

  let result = '';
  const charsLen = chars.length;
  for (let i = 0; i < len; i++) {
    result += chars.charAt(Math.floor(Math.random() * charsLen));
  }
  return result;
}

function randomUppers(len: number) {
  return randomString(len, UPPERS);
}

function randomDigits(len: number) {
  return randomString(len, DIGITS);
}

export {
  randomUppers,
  randomDigits,
};
