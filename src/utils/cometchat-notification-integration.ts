import { FcmService } from "@fcm/services/fcm-service";
import { FcmMessage, FcmMessageCreateReq, FcmMessageStatus } from "@fcm/models/fcm-message";
import eventEmitter from "@utils/event-emitter";
import logger from "@utils/logger";
import {
  CometChatMessageEvent,
  CometChatDeliveryEvent,
  CometChatReadEvent,
  CometChatConversationEvent,
  CometChatUserEvent
} from "@utils/cometchat-webhook-handler";
import { DateTime } from "luxon";

/**
 * Example integration showing how to connect CometChat webhooks
 * with your existing FCM notification system
 */
export class CometChatNotificationIntegration {

  private fcmService: FcmService;

  constructor() {
    this.fcmService = new FcmService();
    this.initializeNotificationHandlers();
  }

  /**
   * Initialize notification handlers for CometChat events
   */
  private initializeNotificationHandlers() {
    // Listen directly to normalized CometChat events
    eventEmitter.on('cometchat-message-sent', async (event: CometChatMessageEvent) => {
      await this.handleMessageSent(event);
    });
    eventEmitter.on('cometchat-message-delivered', async (event: CometChatDeliveryEvent) => {
      await this.handleMessageDelivered(event);
    });
    eventEmitter.on('cometchat-message-read', async (event: CometChatReadEvent) => {
      await this.handleMessageRead(event);
    });
    eventEmitter.on('cometchat-conversation-created', async (event: CometChatConversationEvent) => {
      await this.handleConversationCreated(event);
    });
    eventEmitter.on('cometchat-conversation-updated', async (event: CometChatConversationEvent) => {
      await this.handleConversationUpdated(event);
    });
    eventEmitter.on('cometchat-user-online', async (event: CometChatUserEvent) => {
      await this.handleUserOnline(event);
    });
    eventEmitter.on('cometchat-user-offline', async (event: CometChatUserEvent) => {
      await this.handleUserOffline(event);
    });
    logger.info('[CometChatNotificationIntegration] Notification handlers initialized');
  }

  /**
   * Handle message sent events
   */
  private async handleMessageSent(event: CometChatMessageEvent) {
    try {
      // Only send FCM if receiver is member or instructor
      if (
        event.receiverUserType === 'member' || event.receiverUserType === 'instructor'
      ) {
        // Send push notification to receiver
        const now = DateTime.now().setZone('UTC');
        await this.fcmService.scheduleMessage({
          message: {
            userId: event.receiverUid,
            title: 'New Message',
            body: this.truncateMessage(event.message, 100),
            go: `/chat?uid=${event.conversationId}`,
            at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
            scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
            status: FcmMessageStatus.Scheduled,
          } as FcmMessage,
          by: 'CometChatWebhook',
        } as FcmMessageCreateReq );

        // Update unread message count (you would implement this)
        await this.updateUnreadMessageCount(event.receiverUid, event.conversationId);

        logger.info(`[CometChatNotificationIntegration] Push notification scheduled for message: ${event.messageId}`);
      } else {
        logger.info(`[CometChatNotificationIntegration] Skipped FCM for message: ${event.messageId} (receiverUserType not member/instructor)`);
      }
    } catch (error) {
      logger.error(`[CometChatNotificationIntegration] Error scheduling push notification: ${error}`);
    }
  }

  /**
   * Handle message delivered events
   */
  private async handleMessageDelivered(event: CometChatDeliveryEvent) {
    try {
      // You could send a delivery receipt to the sender
      const now = DateTime.now().setZone('UTC');
      await this.fcmService.scheduleMessage({
        message: {
          userId: event.receiverUid, // Use receiverUid as the recipient
          title: 'Message Delivered',
          body: 'Your message has been delivered',
          go: `/chat?uid=${event.conversationId}`,
          at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
          scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
          status: FcmMessageStatus.Scheduled,
        } as FcmMessage,
        by: 'CometChatWebhook',
      } as FcmMessageCreateReq );

      logger.info(`[CometChatNotificationIntegration] Delivery receipt scheduled for message: ${event.messageId}`);
    } catch (error) {
      logger.error(`[CometChatNotificationIntegration] Error scheduling delivery receipt: ${error}`);
    }
  }

  /**
   * Handle message read events
   */
  private async handleMessageRead(event: CometChatReadEvent) {
    try {
      // You could send a read receipt to the sender
      const now = DateTime.now().setZone('UTC');
      await this.fcmService.scheduleMessage({
        message: {
          userId: event.readerUid, // Use readerUid as the recipient
          title: 'Message Read',
          body: 'Your message has been read',
          go: `/chat?uid=${event.conversationId}`,
          at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
          scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
          status: FcmMessageStatus.Scheduled,
        } as FcmMessage,
        by: 'CometChatWebhook',
      } as FcmMessageCreateReq );

      // Update read status in your database
      await this.updateMessageReadStatus(event.messageId, event.readerUid);

      logger.info(`[CometChatNotificationIntegration] Read receipt scheduled for message: ${event.messageId}`);
    } catch (error) {
      logger.error(`[CometChatNotificationIntegration] Error scheduling read receipt: ${error}`);
    }
  }

  /**
   * Handle conversation notifications
   */
  private async handleConversationCreated(event: CometChatConversationEvent) {
    try {
      // Notify participants about new conversation
      if (event.participants) {
        for (const participantId of event.participants) {
          const now = DateTime.now().setZone('UTC');
          await this.fcmService.scheduleMessage({
            message: {
              userId: participantId,
              title: 'New Conversation',
              body: 'You have a new conversation',
              go: `/chat?uid=${event.conversationId}`,
              at: now.toFormat('yyyy-MM-dd HH:mm:ss'),
              scheduledAt: now.toFormat('yyyy-MM-dd HH:mm:ss'),
              status: FcmMessageStatus.Scheduled,
            } as FcmMessage,
            by: 'CometChatWebhook',
          } as FcmMessageCreateReq );
        }
      }
      logger.info(`[CometChatNotificationIntegration] New conversation notifications scheduled: ${event.conversationId}`);
    } catch (error) {
      logger.error(`[CometChatNotificationIntegration] Error handling conversation created: ${error}`);
    }
  }

  /**
   * Handle conversation updated events
   */
  private async handleConversationUpdated(event: CometChatConversationEvent) {
    try {
      // Handle conversation updates (e.g., name changes, participant changes)
      logger.info(`[CometChatNotificationIntegration] Conversation updated: ${event.conversationId}`);
      // You could notify participants about conversation changes
      // This depends on your specific business logic
    } catch (error) {
      logger.error(`[CometChatNotificationIntegration] Error handling conversation updated: ${error}`);
    }
  }

  /**
   * Handle user status notifications
   */
  private async handleUserOnline(event: CometChatUserEvent) {
    try {
      // Update user online status in your database
      await this.updateUserOnlineStatus(event.userId, true);
      logger.info(`[CometChatNotificationIntegration] User online: ${event.userId}`);
    } catch (error) {
      logger.error(`[CometChatNotificationIntegration] Error handling user online: ${error}`);
    }
  }

  private async handleUserOffline(event: CometChatUserEvent) {
    try {
      // Update user offline status in your database
      await this.updateUserOnlineStatus(event.userId, false);
      logger.info(`[CometChatNotificationIntegration] User offline: ${event.userId}`);
    } catch (error) {
      logger.error(`[CometChatNotificationIntegration] Error handling user offline: ${error}`);
    }
  }

  /**
   * Utility method to truncate long messages
   */
  private truncateMessage(message: string, maxLength: number): string {
    if (message.length <= maxLength) {
      return message;
    }
    return message.substring(0, maxLength - 3) + '...';
  }

  /**
   * Update unread message count for a user
   * This is a placeholder - implement based on your database schema
   */
  private async updateUnreadMessageCount(userId: string, conversationId: string): Promise<void> {
    // TODO: Implement based on your database schema
    logger.debug(`[CometChatNotificationIntegration] Updating unread count for user: ${userId}, conversation: ${conversationId}`);
  }

  /**
   * Update message read status
   * This is a placeholder - implement based on your database schema
   */
  private async updateMessageReadStatus(messageId: string, readerUid: string): Promise<void> {
    // TODO: Implement based on your database schema
    logger.debug(`[CometChatNotificationIntegration] Updating read status for message: ${messageId}, reader: ${readerUid}`);
  }

  /**
   * Update user online status
   * This is a placeholder - implement based on your database schema
   */
  private async updateUserOnlineStatus(userId: string, isOnline: boolean): Promise<void> {
    // TODO: Implement based on your database schema
    logger.debug(`[CometChatNotificationIntegration] Updating online status for user: ${userId}, online: ${isOnline}`);
  }
}

/**
 * Initialize the CometChat notification integration
 * Call this in your application startup
 */
export function initializeCometChatNotifications(): CometChatNotificationIntegration {
  return new CometChatNotificationIntegration();
} 