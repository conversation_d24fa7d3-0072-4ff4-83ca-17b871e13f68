import {
  CreateTemplateCommand,
  ListTemplatesCommand,
  ListTemplatesResponse,
  SendTemplatedEmailCommand,
  SendTemplatedEmailRequest,
  SESClient,
  UpdateTemplateCommand
} from "@aws-sdk/client-ses";
import { Template } from "@aws-sdk/client-ses/dist-types/models/models_0";
import logger from "@utils/logger";
import process from "process";

function sesClient() {
  return new SESClient({
    ...(process.env.AWS_ACCESS_KEY_ID && {
      credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        }
      }),
    region: process.env.AWS_SES_REGION,
  });
}

async function listTemplates(command: ListTemplatesCommand): Promise<ListTemplatesResponse> {
  const response = await sesClient().send(command);
  logger.debug(`listTemplates() response=${JSON.stringify(response)}`);
  return response;
}

async function createTemplate(template: Template) {
  const command = new CreateTemplateCommand({
    Template: template,
  });
  const response = await sesClient().send(command);
  logger.debug(`createTemplate() response=${JSON.stringify(response)}`);
}

async function updateTemplate(template: Template) {
  const command = new UpdateTemplateCommand({
    Template: template,
  });
  const response = await sesClient().send(command);
  logger.debug(`updateTemplate() response=${JSON.stringify(response)}`);
}

async function sendTemplatedEmail(req: SendTemplatedEmailRequest) {
  const command = new SendTemplatedEmailCommand(req);
  const response = await sesClient().send(command);
  logger.info(`sendTemplatedEmail() response=${JSON.stringify(response)}`);
}

export {
  listTemplates,
  createTemplate,
  updateTemplate,
  sendTemplatedEmail,
};

/*
  const template = {
    TemplateName: "StudioUser_Invite",
    SubjectPart: "Invitation to Sign Up",
    HtmlPart: "Hi {{name}},<br><br>Please sign up @ {{signupURL}}",
    TextPart: "Hi {{name}},\n\nPlease sign up @ {{signupURL}}",
  } as Template;
  const listTemplatesResponse = await listTemplates(new ListTemplatesCommand({MaxItems: 10}));
  const found = listTemplatesResponse.TemplatesMetadata!.find(o => o.Name === template.TemplateName);
  if (found) {
    await updateTemplate(template);
  } else {
    await createTemplate(template);
  }
*/
