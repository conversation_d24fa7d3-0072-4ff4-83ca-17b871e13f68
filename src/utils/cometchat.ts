import axios from 'axios';
import logger from '@utils/logger';
import { CometChatUser } from '@public-api/models/cometchat';

const COMETCHAT_API_KEY = process.env.COMETCHAT_API_KEY!;
const COMETCHAT_APP_ID = process.env.COMETCHAT_APP_ID!;
const COMETCHAT_REGION = process.env.COMETCHAT_REGION!;

const BASE_URL = `https://${COMETCHAT_APP_ID}.api-${COMETCHAT_REGION}.cometchat.io/v3`;

export async function createCometChatUser(user: CometChatUser) {
  const url = `${BASE_URL}/users`;
  const headers = {
    apiKey: COMETCHAT_API_KEY,
    'Content-Type': 'application/json',
    Accept: 'application/json',
  };
  const body = {
    uid: user.uid,
    name: user.name,
    tags: user.tags || [],
    metadata: {
      '@private': {
        email: user.email,
        ...(user.phone && { contactNumber: user.phone }),
        ...(user.userType && { userType: user.userType }),
        ...(user.metadata && user.metadata),
      },
    },
  };
  try {
    const res = await axios.post(url, body, { headers });
    logger.info(`[CometChat] Created user for uid=${user.uid}`);
    return { success: true, data: res.data.data, message: '' };
  } catch (err: any) {
    let message = err.message;
    if (err.response?.data?.error) {
      message = err.response.data.error.message;
    }
    logger.error(`[CometChat] Failed to create user for uid=${user.uid}: ${err?.response?.data ? JSON.stringify(err.response.data) : err.message}`);
    return { success: false, data: null, message: message };
  }
}

export async function getCometChatUser(uid: string) {
  const url = `${BASE_URL}/users/${uid}`;
  const headers = {
    apiKey: COMETCHAT_API_KEY,
    'Content-Type': 'application/json',
    Accept: 'application/json',
  };
  try {
    const res = await axios.get(url, { headers });
    logger.info(`[CometChat] Fetched user for uid=${uid}`);
    return { success: true, data: res.data.data, message: '' };
  } catch (err: any) {
    let message = err.message;
    if (err.response?.data?.error) {
      message = err.response.data.error.message;
    }
    logger.error(`[CometChat] Failed to fetch user for uid=${uid}: ${err?.response?.data ? JSON.stringify(err.response.data) : err.message}`);
    return { success: false, data: null, message: message };
  }
}

export async function createCometChatAuthToken(uid: string) {
  const url = `${BASE_URL}/users/${uid}/auth_tokens`;
  const headers = {
    apiKey: COMETCHAT_API_KEY,
    'Content-Type': 'application/json',
    Accept: 'application/json',
  };
  const body = { force: true };
  try {
    const res = await axios.post(url, body, { headers });
    logger.info(`[CometChat] Created auth token for uid=${uid}`);
    return { success: true, data: res.data.data, message: '' };
  } catch (err: any) {
    let message = err.message;
    if (err.response?.data?.error) {
      message = err.response.data.error.message;
    }
    logger.error(`[CometChat] Failed to create auth token for uid=${uid}: ${err?.response?.data ? JSON.stringify(err.response.data) : err.message}`);
    return { success: false, data: null, message: message };
  }
} 