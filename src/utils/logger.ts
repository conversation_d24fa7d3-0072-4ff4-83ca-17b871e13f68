import process from "process";
import winston from "winston";
import "winston-daily-rotate-file";

const timestampFormat = winston.format.timestamp({
  format: "YYYY-MM-DD HH:mm:ss,SSS",
});

const console = new winston.transports.Console({
  format: winston.format.combine(
    winston.format.colorize({ all: true }),
    timestampFormat,
    winston.format.cli(),
    winston.format.printf(info =>
      `${info.timestamp} ${info.level} ${info.message}`
    ),
  ),
});
const logFile = new winston.transports.DailyRotateFile({
  format: winston.format.combine(
    timestampFormat,
    /*
    winston.format.json(),
    */
    timestampFormat,
    winston.format.cli(),
    winston.format.printf(info =>
      `${info.timestamp} ${info.level} ${info.message}`
    ),
  ),
  dirname: "./logs",
  filename: "app.log",
});

const logger = winston.createLogger({
  level: "debug",
  transports: [
    // add below
  ],
  exitOnError: false,
});

if (process.env.NODE_ENV === "production") {
  logger.add(logFile);
}

logger.add(console);
logger.debug(`logger initialised, NODE_ENV=${process.env.NODE_ENV}`);

export default logger;
