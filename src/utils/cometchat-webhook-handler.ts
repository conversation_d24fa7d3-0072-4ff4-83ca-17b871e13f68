import eventEmitter from "@utils/event-emitter";
import logger from "@utils/logger";

export interface CometChatMessageEvent {
  messageId: string;
  senderUid: string;
  senderUserType?: string;
  receiverUid: string;
  receiverUserType?: string;
  receiverType: string;
  conversationId: string;
  message: string;
  messageType: string;
  sentAt: number;
  metadata?: any;
}

export interface CometChatDeliveryEvent {
  messageId: string;
  receiverUid: string;
  conversationId: string;
  deliveredAt: number;
}

export interface CometChatReadEvent {
  messageId: string;
  readerUid: string;
  conversationId: string;
  readAt: number;
}

export interface CometChatConversationEvent {
  conversationId: string;
  conversationType: string;
  participants?: string[];
  createdAt?: number;
  updatedAt?: number;
  metadata?: any;
}

export interface CometChatUserEvent {
  userId: string;
  lastActiveAt?: number;
  metadata?: any;
}

export class CometChatWebhookHandler {
  /**
   * Initialize webhook event listeners
   */
  static initializeListeners() {
    // Message sent event
    eventEmitter.on('cometchat-message-sent', (event: CometChatMessageEvent) => {
      this.handleMessageSent(event);
    });

    // Message delivered event
    eventEmitter.on('cometchat-message-delivered', (event: CometChatDeliveryEvent) => {
      this.handleMessageDelivered(event);
    });

    // Message read event
    eventEmitter.on('cometchat-message-read', (event: CometChatReadEvent) => {
      this.handleMessageRead(event);
    });

    // Conversation created event
    eventEmitter.on('cometchat-conversation-created', (event: CometChatConversationEvent) => {
      this.handleConversationCreated(event);
    });

    // Conversation updated event
    eventEmitter.on('cometchat-conversation-updated', (event: CometChatConversationEvent) => {
      this.handleConversationUpdated(event);
    });

    // User online event
    eventEmitter.on('cometchat-user-online', (event: CometChatUserEvent) => {
      this.handleUserOnline(event);
    });

    // User offline event
    eventEmitter.on('cometchat-user-offline', (event: CometChatUserEvent) => {
      this.handleUserOffline(event);
    });

    logger.info('[CometChatWebhookHandler] Event listeners initialized');
  }

  /**
   * Handle message sent events
   */
  private static async handleMessageSent(event: CometChatMessageEvent) {
    try {
      logger.info(`[CometChatWebhookHandler] Message sent: ${event.messageId} from ${event.senderUid} to ${event.receiverUid}`);
      // No further event emission here
    } catch (error) {
      logger.error(`[CometChatWebhookHandler] Error handling message sent: ${error}`);
    }
  }

  /**
   * Handle message delivered events
   */
  private static async handleMessageDelivered(event: CometChatDeliveryEvent) {
    try {
      logger.info(`[CometChatWebhookHandler] Message delivered: ${event.messageId} to ${event.receiverUid}`);
      // No further event emission here
    } catch (error) {
      logger.error(`[CometChatWebhookHandler] Error handling message delivered: ${error}`);
    }
  }

  /**
   * Handle message read events
   */
  private static async handleMessageRead(event: CometChatReadEvent) {
    try {
      logger.info(`[CometChatWebhookHandler] Message read: ${event.messageId} by ${event.readerUid}`);
      // No further event emission here
    } catch (error) {
      logger.error(`[CometChatWebhookHandler] Error handling message read: ${error}`);
    }
  }

  /**
   * Handle conversation created events
   */
  private static async handleConversationCreated(event: CometChatConversationEvent) {
    try {
      logger.info(`[CometChatWebhookHandler] Conversation created: ${event.conversationId}`);
      // No further event emission here
    } catch (error) {
      logger.error(`[CometChatWebhookHandler] Error handling conversation created: ${error}`);
    }
  }

  /**
   * Handle conversation updated events
   */
  private static async handleConversationUpdated(event: CometChatConversationEvent) {
    try {
      logger.info(`[CometChatWebhookHandler] Conversation updated: ${event.conversationId}`);
      // No further event emission here
    } catch (error) {
      logger.error(`[CometChatWebhookHandler] Error handling conversation updated: ${error}`);
    }
  }

  /**
   * Handle user online events
   */
  private static async handleUserOnline(event: CometChatUserEvent) {
    try {
      logger.info(`[CometChatWebhookHandler] User online: ${event.userId}`);
      // No further event emission here
    } catch (error) {
      logger.error(`[CometChatWebhookHandler] Error handling user online: ${error}`);
    }
  }

  /**
   * Handle user offline events
   */
  private static async handleUserOffline(event: CometChatUserEvent) {
    try {
      logger.info(`[CometChatWebhookHandler] User offline: ${event.userId}`);
      // No further event emission here
    } catch (error) {
      logger.error(`[CometChatWebhookHandler] Error handling user offline: ${error}`);
    }
  }
} 