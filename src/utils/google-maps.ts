import { Client, GeocodeRequest, GeocodeResult } from "@googlemaps/google-maps-services-js";
import logger from "@utils/logger";

async function geocode(placeId: string): Promise<GeocodeResult|undefined> {
  let ret = undefined;
  try {
    const client = new Client();
    const geoCodeResp = await client.geocode({
      params: {
        key: process.env.GOOGLE_MAPS_API_KEY,
        place_id: placeId,
      }
    } as GeocodeRequest);
    const results = geoCodeResp.data.results;
    logger.debug(`geocode() placeId=${placeId}, response=${JSON.stringify(results)}`);
    ret = results.length > 0 ? results[0] : undefined;
  } catch (e) {
    logger.error(`geocode() error! ${e}`);
  }
  return ret;
}

export {
  geocode,
};
