import { Twilio } from 'twilio';

const accountSid = process.env.TWILIO_ACCOUNT_SID!;
const authToken = process.env.TWILIO_AUTH_TOKEN!;
const verifyServiceSid = process.env.TWILIO_VERIFY_SID!;

const client = new Twilio(accountSid, authToken);

/**
 * Start a verification (send OTP)
 * @param phoneNumber E.164 format, e.g. +**********
 * @returns Twilio API response
 */
export async function startVerification(phoneNumber: string) {
  return client.verify.v2.services(verifyServiceSid)
    .verifications
    .create({ to: phoneNumber, channel: 'sms' });
}

/**
 * Check a verification (verify OTP)
 * @param phoneNumber E.164 format, e.g. +**********
 * @param code The OTP code user received
 * @returns Twilio API response
 */
export async function checkVerification(phoneNumber: string, code: string) {
  return client.verify.v2.services(verifyServiceSid)
    .verificationChecks
    .create({ to: phoneNumber, code });
}