import eventEmitter from './event-emitter';
import { FcmService } from "@fcm/services/fcm-service";
import { CreditService } from "@booking-api/services/credit-service";
import logger from "@utils/logger";
import { CometChatWebhookHandler } from "@utils/cometchat-webhook-handler";
import { initializeCometChatNotifications } from "@utils/cometchat-notification-integration";
import { TransactionStatus } from "@booking-api/models/credit";

// Initialize CometChat event normalization and notification integration
CometChatWebhookHandler.initializeListeners();
initializeCometChatNotifications();

const fcmService = new FcmService();
const creditService = new CreditService();

// Session confirmed
eventEmitter.on('session-confirmed', async ({ by, id, data }) => {
    const event = data;
    try {
        if(event?.memberId) {
            await fcmService.scheduleSessionConfirmationNotifyFcmMessage(by, event.memberId, id, 1);
            await fcmService.scheduleSessionReminderFcmMessage(by, event.memberId, event, 1);
            await fcmService.scheduleSessionReminderFcmMessage(by, event.memberId, event, 2);
        }

        if(event?.instructorId) {
            await fcmService.scheduleSessionConfirmationNotifyFcmMessage(by, event.instructorId, id, 2);
            await fcmService.scheduleSessionReminderFcmMessage(by, event.instructorId, event, 3);
            await fcmService.scheduleSessionReminderFcmMessage(by, event.instructorId, event, 4);
        }

    } catch (error) {
        logger.error(error);
    }
});

// Session completed
eventEmitter.on('session-completed', async ({ by, id, data }) => {
    const session = data;
    if(session?.memberId) {
        await fcmService.scheduleSessionFeedbackNotifyFcmMessage(by, session.memberId, id);
        await fcmService.scheduleSessionReviewReminderFcmMessage(by, session.memberId, id);
    }
});

// Session feedback
eventEmitter.on('session-feedback', async ({ by, id, data }) => {
    const session = data;
    if(session?.instructorId) {
        if(!!session.hasMemberFeedback && !session.isPrivateFeedback) {
            await fcmService.scheduleSessionReviewNotifyFcmMessage(by, session.instructorId, id);
        }
    }
});

// Session cancelled
eventEmitter.on('session-cancelled', async ({ by, id, data }) => {
    const event = data;
    if(event?.memberId) {
        await fcmService.scheduleSessionCancelledFcmMessage(by, event.memberId, event, 1);
    }
    if(event?.instructorId) {
        await fcmService.scheduleSessionCancelledFcmMessage(by, event.instructorId, event, 2);
        event?.instructorId == event?.cancelledById && (await fcmService.scheduleSessionCancelledFcmMessage(by, event.instructorId, event, 3));
    }
});

// Instructor approved
eventEmitter.on('instructor-approved', async ({ by, id }) => {
    if(id) {
        await fcmService.scheduleInstructorProfileApprovalNotifyFcmMessage(by, id);
    }
});

// Transaction topup
eventEmitter.on('transaction-topup', async ({ by, id, data }) => {
    const transaction = data;
    if(transaction) {
        // Send notification
        await fcmService.scheduleTopupSuccessfulNotifyFcmMessage(by, transaction.memberId);

        // Track package purchase if this is a confirmed payment transaction with any package
        await creditService.incrementPackagePurchaseCount(transaction, by);
    }
});
