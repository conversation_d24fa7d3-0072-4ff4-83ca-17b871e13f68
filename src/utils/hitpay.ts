import logger from "@utils/logger";
import crypto from "crypto";

function parse(webhookPayload: Map<string, string>) : {
  hmac: string,
  payload: string,
} {
  /* unused
  logger.debug(`(parse) webhookPayload=${webhookPayload}`);

  // Convert to map of key, values
  // Eg. b=222&a=111&hmac=xxx
  // ...to...
  // Map<string, string>(
  //   "b": "222",
  //   "a": "111",
  //   "hmac": "xxx"
  // )
  const keyValues = webhookPayload.split("&");
  const params = new Map<string, string>();
  for (const keyValue of keyValues) {
    const tokens = keyValue.split("=");
    if (tokens.length != 2) {
      continue;
    }
    params.set(tokens[0], tokens[1]);
  }
  const hmac = params.get("hmac") ?? "";
  */
  const hmac = webhookPayload.get("hmac") ?? "";

  // Remove hmac and sort by keys
  // Map<string, string>(
  //   "a": "111",
  //   "b": "222",
  // )
  const payloadMap = new Map<string, string>(webhookPayload);
  payloadMap.delete("hmac");
  const payloadMapSorted = new Map([...payloadMap.entries()].sort());

  // Join key, values into a string
  // a111b222
  const payloadArr = new Array<string>();
  payloadMapSorted.forEach((v, k) => payloadArr.push(k + v));
  const payload = payloadArr.join("");
  logger.debug(`(parse) payload=${payload}`);

  return {
    hmac: hmac,
    payload: payload,
  };
}

// HitPay Online Payments
// https://docs.hitpayapp.com/apis/guide/online-payments#validating-a-webhook
function sign(payload: string, secret: string): string {
  const hmac = crypto.createHmac('sha256', secret);
  return hmac.update(Buffer.from(payload, 'utf-8')).digest("hex");
}

function verify(
  webhookPayload: Map<string, string>,
  apiSalt: string,
): boolean {
  let ret = false;
  try {
    const prepped = parse(webhookPayload);
    const signature = sign(prepped.payload, apiSalt);
    logger.debug(`(verify) prepped=${JSON.stringify(prepped)}`);
    logger.debug(`(verify) hmac=${prepped.hmac}}`);
    logger.debug(`(verify) signature=${signature}}`);
    ret = (prepped.hmac == signature);
  } catch (e) {
    logger.error(`Error @ verify! ${e}`);
  }
  return ret;
}

export {
  verify
};
