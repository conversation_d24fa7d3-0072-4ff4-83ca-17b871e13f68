import fetch from 'node-fetch';
import { DateTime } from 'luxon';

// Types
export interface IgloohomeConfig {
  clientId?: string;
  clientSecret?: string;
  deviceId?: string;
  bridgeId?: string;
  tokenUrl?: string;
  apiRoot?: string;
  pinType?: number;
}

export interface CreatePinJobInput {
  date: string; // YYYY-MM-DD
  startTime: string; // HH:mm or HHmm
  endTime?: string; // HH:mm or HHmm, optional
  pin?: string; // optional, 4 digits
  timezone?: string; // e.g., 'Asia/Singapore', 'UTC', 'America/New_York'
}

export interface CreatePinJobResult {
  jobId?: string;
  pin?: string;
  accessName?: string;
  startIso?: string;
  endIso?: string;
  error?: string;
  pinExists?: boolean;
}

// Default config from env
function getConfig(overrides: IgloohomeConfig = {}): Required<IgloohomeConfig> {
  return {
    clientId: overrides.clientId || process.env.IGLOO_CLIENT_ID!,
    clientSecret: overrides.clientSecret || process.env.IGLOO_CLIENT_SECRET!,
    deviceId: overrides.deviceId || process.env.IGLOO_DEVICE_ID!,
    bridgeId: overrides.bridgeId || process.env.IGLOO_BRIDGE_ID!,
    tokenUrl: overrides.tokenUrl || process.env.IGLOO_TOKEN_URL!,
    apiRoot: overrides.apiRoot || process.env.IGLOO_API_ROOT!,
    pinType: overrides.pinType || 4,
  };
}

// Helpers
function randomPin(): string {
  return Math.floor(1000 + Math.random() * 9000).toString();
}

function validatePin(pin: string) {
  if (!/^\d{4}$/.test(pin)) {
    throw new Error('PIN must be 4 digits');
  }
}

function parseTime(timeStr: string): { hour: number; minute: number } {
  let t = timeStr.trim();
  if (/^\d{1,2}:\d{2}$/.test(t)) {
    const [h, m] = t.split(':').map(Number);
    return { hour: h, minute: m };
  } else if (/^\d{3,4}$/.test(t)) {
    t = t.padStart(4, '0');
    return { hour: Number(t.slice(0, 2)), minute: Number(t.slice(2)) };
  } else {
    throw new Error('Invalid time format. Use HH:mm or HHmm.');
  }
}

function toZonedIso(dateStr: string, hour: number, minute: number, timezone: string): string {
  // Returns ISO string with correct offset for the timezone
  const dt = DateTime.fromObject(
    { year: Number(dateStr.slice(0, 4)), month: Number(dateStr.slice(5, 7)), day: Number(dateStr.slice(8, 10)), hour, minute, second: 0, millisecond: 0 },
    { zone: timezone }
  );
  return dt.toISO({ suppressMilliseconds: true, includeOffset: true });
}

function addMinutesZoned(isoString: string, minutes: number, timezone: string): string {
  const dt = DateTime.fromISO(isoString, { zone: timezone }).plus({ minutes });
  return dt.toISO({ suppressMilliseconds: true, includeOffset: true });
}

function buildAccessName(startIso: string, endIso: string, timezone: string): string {
  // Format: "PIN yyyy-MM-dd HH:mm-HH:mm" in local time
  const start = DateTime.fromISO(startIso, { zone: timezone });
  const end = DateTime.fromISO(endIso, { zone: timezone });
  return `PIN ${start.toFormat('yyyy-MM-dd HH:mm')}-${end.toFormat('HH:mm')}`;
}

async function getAccessToken(config: Required<IgloohomeConfig>): Promise<string> {
  const btoa = (str: string) => Buffer.from(str, 'utf8').toString('base64');
  const credentials = btoa(`${config.clientId}:${config.clientSecret}`);
  const params = new URLSearchParams();
  params.append('grant_type', 'client_credentials');

  const response = await fetch(config.tokenUrl, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${credentials}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: params,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Token request failed: ${response.status} ${errorText}`);
  }
  const data = await response.json();
  if (!data.access_token) {
    throw new Error('No access_token in token response');
  }
  return data.access_token;
}

async function createCustomPinJob(
  accessToken: string,
  accessName: string,
  pin: string,
  startDate: string,
  endDate: string,
  config: Required<IgloohomeConfig>
): Promise<CreatePinJobResult> {
  const url = `${config.apiRoot}/devices/${config.deviceId}/jobs/bridges/${config.bridgeId}`;
  const payload: any = {
    jobType: 4,
    jobData: {
      accessName,
      pin,
      pinType: config.pinType,
      startDate,
    },
  };
  if (endDate) payload.jobData.endDate = endDate;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const errorText = await response.text();
    if (response.status === 406) {
      return { pinExists: true, error: errorText };
    }
    return { error: `API request failed: ${response.status} ${errorText}` };
  }
  const data = await response.json();
  if (data.jobId) {
    return { jobId: data.jobId };
  } else {
    return { error: 'Unexpected response: ' + JSON.stringify(data) };
  }
}

export async function createPinJob(
  input: CreatePinJobInput,
  configOverrides: IgloohomeConfig = {}
): Promise<CreatePinJobResult> {
  const config = getConfig(configOverrides);
  try {
    const { date, startTime, endTime, pin: inputPin, timezone = 'Asia/Singapore' } = input;
    const { hour: startHour, minute: startMinute } = parseTime(startTime);
    let endHour: number, endMinute: number;
    if (!endTime || endTime.trim() === '') {
      endHour = startHour + 1;
      endMinute = startMinute;
      if (endHour >= 24) {
        endHour = 23;
        endMinute = 59;
      }
    } else {
      ({ hour: endHour, minute: endMinute } = parseTime(endTime));
    }
    const startIsoRaw = toZonedIso(date, startHour, startMinute, timezone);
    const endIsoRaw = toZonedIso(date, endHour, endMinute, timezone);
    const startIso = addMinutesZoned(startIsoRaw, -15, timezone);
    const endIso = addMinutesZoned(endIsoRaw, 15, timezone);
    const accessName = buildAccessName(startIso, endIso, timezone);
    const accessToken = await getAccessToken(config);
    let pin = inputPin || randomPin();
    validatePin(pin);
    let result: CreatePinJobResult;
    let attempts = 0;
    do {
      result = await createCustomPinJob(accessToken, accessName, pin, startIso, endIso, config);
      attempts++;
      if (result.pinExists) {
        pin = randomPin();
        validatePin(pin);
      }
    } while (result.pinExists && attempts < 20);
    if (result.jobId) {
      return {
        jobId: result.jobId,
        pin,
        accessName,
        startIso,
        endIso,
      };
    } else if (result.pinExists) {
      return { error: 'Failed to generate a unique PIN after 20 attempts.' };
    } else {
      return { error: result.error };
    }
  } catch (err: any) {
    return { error: err.message };
  }
}

// Export helpers for testing
export const _igloohomeTestHelpers = {
  randomPin,
  validatePin,
  parseTime,
  toZonedIso,
  addMinutesZoned,
  buildAccessName,
  getAccessToken,
  createCustomPinJob,
};
// Note: Requires 'luxon' as a dependency. Install with: npm install luxon 