import { Sequelize, Transaction } from 'sequelize';
import { getSequelize } from '@database/models';

export interface TransactionOptions {
  transaction?: Transaction;
  authId?: string;
}

export const withTransaction = async <T>(
  callback: (transaction: Transaction, options?: TransactionOptions) => Promise<T>,
  options: TransactionOptions = {}
): Promise<T> => {
  const { transaction: externalTransaction } = options;

  const sequelize = getSequelize();
  if (!sequelize) {
    throw new Error('Sequelize instance not initialized');
  }

  if (externalTransaction) {
    return await callback(externalTransaction, options);
  }

  return await sequelize.transaction(async (transaction) => {
    return await callback(transaction, { ...options, transaction });
  });
};