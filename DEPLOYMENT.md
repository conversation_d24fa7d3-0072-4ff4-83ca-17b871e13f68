# ViFit NodeJS API Deployment Guide

This guide covers how to deploy the ViFit NodeJS API both locally (using Docker) and to AWS Fargate (for staging and production environments).

---

## 1. Local Docker Deployment

### **A. Prerequisites**
- Docker installed
- `.env` file with all required environment variables in your project root
- (Optional) Docker network `devel` if you want to join a custom network

### **B. Build the Docker Image**
```bash
docker build -t vifit/nodejs-api:latest .
```

### **C. Run the Container (with .env file and custom network)**
```bash
# Using --env-file (recommended for most setups)
docker run --env-file .env --network devel -p 3000:3000 vifit/nodejs-api:latest

# OR, if your app uses dotenv to load .env at runtime:
docker run -v $(pwd)/.env:/app/.env --network devel -p 3000:3000 vifit/nodejs-api:latest
```
- The app will be available at [http://localhost:3000](http://localhost:3000)
- Make sure the `devel` network exists:
  ```bash
  docker network create devel  # if not already created
  ```

---

## 2. AWS Fargate Deployment (Staging/Production)

### **A. Prerequisites**
- AWS CLI installed and authenticated (SSO or credentials)
- Docker installed
- AWS ECR repository created for your image
- AWS ECS cluster, VPC, subnets, and security groups set up
- **AWS Secrets Manager secret containing ECS config variables (must already exist)**
- AWS Secrets Manager secrets created for environment variables
- Application Load Balancer (ALB) and Target Group (for API service)

### **B. Build and Push Docker Image for Fargate**
```bash
# Always build for linux/amd64 for Fargate compatibility
docker buildx create --use  # Only needed once per machine

docker buildx build --platform linux/amd64 -t <AWS_ACCOUNT_ID>.dkr.ecr.ap-southeast-1.amazonaws.com/vifit/nodejs-api:latest --push .
```

### **C. Generate ECS Service JSON Files**

- The script expects the name of an existing AWS Secrets Manager secret containing your ECS config (as a JSON object with keys like `ECS_CLUSTER_NAME`, `ECS_SERVICE_NAME`, etc.).
- Example secret content:
  ```json
  {
    "ECS_CLUSTER_NAME": "vifit-cluster",
    "TARGET_GROUP_ARN": "arn:aws:elasticloadbalancing:ap-southeast-1:...:targetgroup/...",
    "SUBNET_1": "subnet-xxxx",
    "SUBNET_2": "subnet-yyyy",
    "SUBNET_3": "subnet-zzzz",
    "SECURITY_GROUP": "sg-xxxx"
  }
  ```
- To generate the ECS service JSON:
  ```bash
  ./generate-ecs-service-json.sh api vifit-ecs-config   # For API service
  ./generate-ecs-service-json.sh cron vifit-ecs-config  # For cron service
  ```
  - The script will load variables from the AWS secret and prompt for any missing ones.

### **D. Generate Task Definition JSON (if needed)**
- Use your deployment script (e.g., `deploy-to-fargate.sh`) to generate and register the task definition for the correct environment (staging/production):
  ```bash
  ./deploy-to-fargate.sh api <AWS_ACCOUNT_ID> <SECRET_NAME>
  ./deploy-to-fargate.sh cron <AWS_ACCOUNT_ID> <SECRET_NAME>
  ```
  - Example for staging:
    ```bash
    ./deploy-to-fargate.sh api ************ vifit-api-env-staging
    ./deploy-to-fargate.sh cron ************ vifit-api-env-staging
    ```
  - Example for production:
    ```bash
    ./deploy-to-fargate.sh api ************ vifit-api-env-prod
    ./deploy-to-fargate.sh cron ************ vifit-api-env-prod
    ```

### **E. Deploy the ECS Service**
```bash
aws ecs create-service --cli-input-json file://ecs-service-api.json --region ap-southeast-1
aws ecs create-service --cli-input-json file://ecs-service-cron.json --region ap-southeast-1
```
- Or use `aws ecs update-service` to update an existing service.

### **F. Health Check Setup (API Service with ALB)**
- In the AWS Console, go to **EC2 → Target Groups → [Your Target Group] → Health checks**
- Set the health check path to `/api/public/health` and port to `traffic port` (3000)

### **G. View Logs**
- All logs are sent to CloudWatch Logs (see `/ecs/vifit-nodejs-api` log group)
- You can view logs in the AWS Console or with the AWS CLI:
  ```bash
  aws logs tail /ecs/vifit-nodejs-api --follow --region ap-southeast-1
  ```

---

## **Summary Table**

| Environment | Build Command | Deploy Script Example | Service JSON Generation |
|-------------|--------------|----------------------|------------------------|
| Local       | `docker build -t vifit/nodejs-api:latest .` | `docker run ...` | N/A |
| Staging     | `docker buildx build --platform linux/amd64 ...` | `./deploy-to-fargate.sh api ************ vifit-api-env-staging` | `./generate-ecs-service-json.sh api vifit-api-ecs-config-staging...` |
| Production  | `docker buildx build --platform linux/amd64 ...` | `./deploy-to-fargate.sh api ************ vifit-api-env-prod` | `./generate-ecs-service-json.sh api vifit-api-ecs-config-prod` |

---

## **Troubleshooting**
- **Fargate exec format error:** Always build with `--platform linux/amd64`.
- **ALB health check fails:** Ensure your app responds 200 on `/api/public/health` and security groups are correct.
- **Logs not visible:** Check CloudWatch Logs group `/ecs/vifit-nodejs-api`.
- **Environment variables not set:** Ensure your secrets in AWS Secrets Manager are correct and referenced in your task definition.
- **ECS config not loaded:** Make sure the correct secret name is passed to the script and the secret contains all required keys.

---

For more details, see the scripts and templates in this repo, or ask for help! 