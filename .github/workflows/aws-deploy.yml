name: CI and CD to AWS ECS (Immutable images)

on:
  push:
    branches:
      - main
      - production
  workflow_dispatch:

permissions:
  id-token: write
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref_name }}
  cancel-in-progress: false

env:
  AWS_REGION: ap-southeast-1
  ECR_REPOSITORY_NAME: vifit/nodejs-api

jobs:
  build_test:
    name: Build and Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: npm

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build

      - name: Test
        run: npm test -- --ci

  build_and_push_image:
    name: Build and Push Docker image to ECR (latest + sha)
    needs: build_test
    runs-on: ubuntu-latest
    outputs:
      image_uri: ${{ steps.vars.outputs.IMAGE_URI }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials (OIDC)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Compute image tags
        id: vars
        run: |
          echo "IMAGE_TAG_SHA=sha-${GITHUB_SHA}" >> "$GITHUB_OUTPUT"
          echo "REGISTRY=${{ steps.login-ecr.outputs.registry }}" >> "$GITHUB_OUTPUT"
          echo "IMAGE_URI=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY_NAME }}:sha-${GITHUB_SHA}" >> "$GITHUB_OUTPUT"

      - name: Build and push (linux/amd64)
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          platforms: linux/amd64
          tags: |
            ${{ steps.vars.outputs.IMAGE_URI }}
            ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY_NAME }}:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy:
    name: Deploy to ECS (Approval Gated, immutable TD)
    needs: build_and_push_image
    runs-on: ubuntu-latest

    # Gate via Environments with Required reviewers
    environment:
      name: ${{ github.ref == 'refs/heads/production' && 'production' || 'staging' }}

    steps:
      - name: Configure AWS credentials (OIDC)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_TO_ASSUME }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Install jq
        run: |
          sudo apt-get update
          sudo apt-get install -y jq

      - name: Resolve current task definition
        id: cur
        env:
          ECS_CLUSTER_NAME: ${{ secrets.ECS_CLUSTER_NAME }}
          ECS_SERVICE_NAME: ${{ secrets.ECS_SERVICE_NAME }}
        run: |
          set -euo pipefail
          TD_ARN=$(aws ecs describe-services \
            --cluster "$ECS_CLUSTER_NAME" \
            --services "$ECS_SERVICE_NAME" \
            --query 'services[0].taskDefinition' --output text)
          echo "TD_ARN=$TD_ARN" >> "$GITHUB_OUTPUT"

      - name: Render new task definition with updated image
        id: render
        env:
          TD_ARN: ${{ steps.cur.outputs.TD_ARN }}
          IMAGE_URI: ${{ needs.build_and_push_image.outputs.image_uri }}
        run: |
          set -euo pipefail
          aws ecs describe-task-definition --task-definition "$TD_ARN" --query 'taskDefinition' > td.json

          # Replace image for all containers (adjust if you need specific container only)
          jq --arg IMAGE "$IMAGE_URI" '
            .containerDefinitions |= (map(.image = $IMAGE)) |
            del(.taskDefinitionArn,
                .revision,
                .status,
                .requiresAttributes,
                .compatibilities,
                .registeredAt,
                .registeredBy)
          ' td.json > td-new.json

          echo "Rendered new task definition using image: $IMAGE_URI"

      - name: Register task definition
        id: reg
        run: |
          set -euo pipefail
          NEW_TD_ARN=$(aws ecs register-task-definition \
            --cli-input-json file://td-new.json \
            --query 'taskDefinition.taskDefinitionArn' --output text)
          echo "NEW_TD_ARN=$NEW_TD_ARN" >> "$GITHUB_OUTPUT"
          echo "Registered TD: $NEW_TD_ARN"

      - name: Update service to new task definition
        env:
          ECS_CLUSTER_NAME: ${{ secrets.ECS_CLUSTER_NAME }}
          ECS_SERVICE_NAME: ${{ secrets.ECS_SERVICE_NAME }}
          NEW_TD_ARN: ${{ steps.reg.outputs.NEW_TD_ARN }}
        run: |
          set -euo pipefail
          aws ecs update-service \
            --cluster "$ECS_CLUSTER_NAME" \
            --service "$ECS_SERVICE_NAME" \
            --task-definition "$NEW_TD_ARN" \
            --force-new-deployment

      - name: Wait for service stability
        env:
          ECS_CLUSTER_NAME: ${{ secrets.ECS_CLUSTER_NAME }}
          ECS_SERVICE_NAME: ${{ secrets.ECS_SERVICE_NAME }}
        run: |
          aws ecs wait services-stable \
            --cluster "$ECS_CLUSTER_NAME" \
            --services "$ECS_SERVICE_NAME"

# How this works:
# - main → environment "staging" (approval required)
# - production → environment "production" (approval required)
# - Build job pushes :latest and :sha-${GITHUB_SHA}
# - Deploy job registers a new task definition revision with the :sha image and updates the service
#
# Required configuration:
# - Repository/Environment secrets:
#   - AWS_ROLE_TO_ASSUME (IAM role ARN for OIDC; least-priv)
#   - ECS_CLUSTER_NAME (env secret, per environment)
#   - ECS_SERVICE_NAME (env secret, per environment)
# - ECR repo: <ACCOUNT_ID>.dkr.ecr.ap-southeast-1.amazonaws.com/vifit/nodejs-api
# - ECS service should already exist (one-time provisioning can be done separately)
