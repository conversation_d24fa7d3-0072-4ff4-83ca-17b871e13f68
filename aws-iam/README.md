# ViFit GitHub Actions IAM Roles

This directory contains the IAM role configurations for ViFit's GitHub Actions CI/CD pipeline.

## Overview

The CI/CD pipeline uses OpenID Connect (OIDC) to authenticate with AWS, eliminating the need for long-lived access keys. Two separate roles are created for different environments:

- **Staging Role**: `vifit-github-actions-staging-role` (for `main` branch deployments)
- **Production Role**: `vifit-github-actions-production-role` (for `production` branch deployments)

## Files

- `vifit-github-actions-staging-role.json` - Trust policy for staging role
- `vifit-github-actions-staging-policy.json` - Permissions policy for staging role
- `vifit-github-actions-production-role.json` - Trust policy for production role
- `vifit-github-actions-production-policy.json` - Permissions policy for production role
- `create-github-actions-roles.sh` - Script to create both roles
- `setup-github-oidc.sh` - <PERSON><PERSON>t to set up GitHub OIDC provider

## Quick Setup

### Prerequisites

1. AWS CLI configured with appropriate permissions
2. GitHub repository with Actions enabled
3. Your AWS Account ID
4. Your GitHub organization/username

### Step 1: Set up GitHub OIDC Provider (One-time setup)

```bash
./setup-github-oidc.sh
```

### Step 2: Create IAM Roles

```bash
./create-github-actions-roles.sh <AWS_ACCOUNT_ID> <GITHUB_ORG>
```

Example:
```bash
./create-github-actions-roles.sh ************ mycompany
```

### Step 3: Configure GitHub Secrets

Add the following secrets to your GitHub repository:

#### Repository-level secrets:
- No repository-level AWS secrets needed (OIDC handles authentication)

#### Environment-specific secrets:

**For `staging` environment:**
- `AWS_ROLE_TO_ASSUME`: `arn:aws:iam::YOUR_ACCOUNT_ID:role/vifit-github-actions-staging-role`
- `ECS_CLUSTER_NAME`: Your staging ECS cluster name
- `ECS_SERVICE_NAME`: Your staging ECS service name

**For `production` environment:**
- `AWS_ROLE_TO_ASSUME`: `arn:aws:iam::YOUR_ACCOUNT_ID:role/vifit-github-actions-production-role`
- `ECS_CLUSTER_NAME`: Your production ECS cluster name
- `ECS_SERVICE_NAME`: Your production ECS service name

## Security Features

### Least Privilege Access
- Each role has minimal permissions required for CI/CD operations
- Staging and production roles are separate with environment-specific constraints
- ECR permissions are scoped to the vifit repository
- ECS permissions are scoped to the vifit-cluster

### Branch Protection
- **Staging role**: Can be assumed from any branch (for development flexibility)
- **Production role**: Can only be assumed from the `production` branch

### Resource Constraints
- ECS operations are limited to the `vifit-cluster`
- Log operations are scoped to `/ecs/vifit-*` log groups
- IAM PassRole is limited to vifit execution and task roles

## Permissions Breakdown

### ECR Permissions
- `ecr:GetAuthorizationToken` - Get login token
- `ecr:BatchCheckLayerAvailability` - Check if layers exist
- `ecr:GetDownloadUrlForLayer` - Download image layers
- `ecr:BatchGetImage` - Pull images
- `ecr:InitiateLayerUpload` - Start image push
- `ecr:UploadLayerPart` - Upload image layers
- `ecr:CompleteLayerUpload` - Complete layer upload
- `ecr:PutImage` - Push complete image

### ECS Permissions
- `ecs:DescribeServices` - Get service information
- `ecs:DescribeTaskDefinition` - Get task definition details
- `ecs:RegisterTaskDefinition` - Create new task definition
- `ecs:UpdateService` - Update service with new task definition
- `ecs:DescribeClusters` - Get cluster information
- `ecs:ListTasks` - List running tasks
- `ecs:DescribeTasks` - Get task details

### IAM Permissions
- `iam:PassRole` - Allow ECS to use execution and task roles

### CloudWatch Logs Permissions
- `logs:CreateLogGroup` - Create log groups
- `logs:CreateLogStream` - Create log streams
- `logs:PutLogEvents` - Write log events
- `logs:DescribeLogGroups` - List log groups
- `logs:DescribeLogStreams` - List log streams

## Troubleshooting

### Common Issues

1. **Role assumption fails**
   - Verify GitHub OIDC provider is set up correctly
   - Check that the repository name in the trust policy matches your actual repository
   - Ensure the branch name is correct for production deployments

2. **ECR push fails**
   - Verify ECR repository exists: `vifit/nodejs-api`
   - Check that the role has ECR permissions

3. **ECS deployment fails**
   - Verify ECS cluster exists: `vifit-cluster`
   - Check that the service names in GitHub secrets match actual ECS services
   - Ensure task execution roles exist and can be passed

### Verification Commands

Check if roles exist:
```bash
aws iam get-role --role-name vifit-github-actions-staging-role
aws iam get-role --role-name vifit-github-actions-production-role
```

List role policies:
```bash
aws iam list-role-policies --role-name vifit-github-actions-staging-role
aws iam list-role-policies --role-name vifit-github-actions-production-role
```

## Updating Roles

To update role permissions, modify the policy JSON files and run:

```bash
aws iam put-role-policy \
    --role-name vifit-github-actions-staging-role \
    --policy-name vifit-github-actions-staging-policy \
    --policy-document file://vifit-github-actions-staging-policy.json
```

## Cleanup

To remove the roles:

```bash
aws iam delete-role-policy --role-name vifit-github-actions-staging-role --policy-name vifit-github-actions-staging-policy
aws iam delete-role --role-name vifit-github-actions-staging-role

aws iam delete-role-policy --role-name vifit-github-actions-production-role --policy-name vifit-github-actions-production-policy
aws iam delete-role --role-name vifit-github-actions-production-role
```
