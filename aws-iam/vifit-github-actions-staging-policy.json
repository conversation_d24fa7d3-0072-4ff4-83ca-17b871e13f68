{"Version": "2012-10-17", "Statement": [{"Sid": "ECRPermissions", "Effect": "Allow", "Action": ["ecr:GetAuthorizationToken", "ecr:BatchCheckLayerAvailability", "ecr:GetDownloadUrlForLayer", "ecr:BatchGetImage", "ecr:InitiateLayerUpload", "ecr:UploadLayerPart", "ecr:CompleteLayerUpload", "ecr:PutImage"], "Resource": "*"}, {"Sid": "ECSPermissions", "Effect": "Allow", "Action": ["ecs:DescribeServices", "ecs:DescribeTaskDefinition", "ecs:RegisterTaskDefinition", "ecs:UpdateService", "ecs:DescribeClusters", "ecs:ListTasks", "ecs:DescribeTasks"], "Resource": "*", "Condition": {"StringEquals": {"ecs:cluster": "arn:aws:ecs:ap-southeast-1:ACCOUNT_ID:cluster/vifit-cluster"}}}, {"Sid": "ECSWaitPermissions", "Effect": "Allow", "Action": ["ecs:DescribeServices"], "Resource": "*"}, {"Sid": "IAMPassRolePermissions", "Effect": "Allow", "Action": ["iam:PassRole"], "Resource": ["arn:aws:iam::ACCOUNT_ID:role/vifit-*-execution-role", "arn:aws:iam::ACCOUNT_ID:role/vifit-*-task-role"]}, {"Sid": "LogsPermissions", "Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents", "logs:DescribeLogGroups", "logs:DescribeLogStreams"], "Resource": ["arn:aws:logs:ap-southeast-1:ACCOUNT_ID:log-group:/ecs/vifit-*", "arn:aws:logs:ap-southeast-1:ACCOUNT_ID:log-group:/ecs/vifit-*:*"]}]}