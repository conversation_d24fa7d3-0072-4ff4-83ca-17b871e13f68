#!/bin/bash

# Script to create GitHub Actions IAM roles for ViFit CI/CD
# Usage: ./create-github-actions-roles.sh <AWS_ACCOUNT_ID> <GITHUB_ORG>

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if required parameters are provided
if [ $# -lt 2 ]; then
    echo -e "${RED}Usage: $0 <AWS_ACCOUNT_ID> <GITHUB_ORG>${NC}"
    echo -e "${YELLOW}Example: $0 ************ myorg${NC}"
    exit 1
fi

AWS_ACCOUNT_ID=$1
GITHUB_ORG=$2
AWS_REGION="ap-southeast-1"

echo -e "${BLUE}🚀 Creating GitHub Actions IAM roles for ViFit CI/CD${NC}"
echo -e "${YELLOW}AWS Account ID: ${AWS_ACCOUNT_ID}${NC}"
echo -e "${YELLOW}GitHub Organization: ${GITHUB_ORG}${NC}"
echo -e "${YELLOW}AWS Region: ${AWS_REGION}${NC}"
echo ""

# Function to substitute placeholders in JSON files
substitute_placeholders() {
    local file=$1
    sed -i.bak "s/ACCOUNT_ID/${AWS_ACCOUNT_ID}/g" "$file"
    sed -i.bak "s/YOUR_GITHUB_ORG/${GITHUB_ORG}/g" "$file"
    rm "${file}.bak"
}

# Create staging role
echo -e "${YELLOW}📋 Creating staging role: vifit-github-actions-staging-role${NC}"

# Substitute placeholders in staging files
cp vifit-github-actions-staging-role.json vifit-github-actions-staging-role-temp.json
cp vifit-github-actions-staging-policy.json vifit-github-actions-staging-policy-temp.json
substitute_placeholders vifit-github-actions-staging-role-temp.json
substitute_placeholders vifit-github-actions-staging-policy-temp.json

# Create the staging role
aws iam create-role \
    --role-name vifit-github-actions-staging-role \
    --assume-role-policy-document file://vifit-github-actions-staging-role-temp.json \
    --description "GitHub Actions role for ViFit staging environment CI/CD" \
    --region $AWS_REGION

# Create and attach the staging policy
aws iam put-role-policy \
    --role-name vifit-github-actions-staging-role \
    --policy-name vifit-github-actions-staging-policy \
    --policy-document file://vifit-github-actions-staging-policy-temp.json

echo -e "${GREEN}✅ Staging role created successfully${NC}"

# Create production role
echo -e "${YELLOW}📋 Creating production role: vifit-github-actions-production-role${NC}"

# Substitute placeholders in production files
cp vifit-github-actions-production-role.json vifit-github-actions-production-role-temp.json
cp vifit-github-actions-production-policy.json vifit-github-actions-production-policy-temp.json
substitute_placeholders vifit-github-actions-production-role-temp.json
substitute_placeholders vifit-github-actions-production-policy-temp.json

# Create the production role
aws iam create-role \
    --role-name vifit-github-actions-production-role \
    --assume-role-policy-document file://vifit-github-actions-production-role-temp.json \
    --description "GitHub Actions role for ViFit production environment CI/CD" \
    --region $AWS_REGION

# Create and attach the production policy
aws iam put-role-policy \
    --role-name vifit-github-actions-production-role \
    --policy-name vifit-github-actions-production-policy \
    --policy-document file://vifit-github-actions-production-policy-temp.json

echo -e "${GREEN}✅ Production role created successfully${NC}"

# Clean up temporary files
rm -f *-temp.json

# Display role ARNs
echo ""
echo -e "${GREEN}🎉 IAM roles created successfully!${NC}"
echo ""
echo -e "${BLUE}Role ARNs:${NC}"
echo -e "${YELLOW}Staging:    arn:aws:iam::${AWS_ACCOUNT_ID}:role/vifit-github-actions-staging-role${NC}"
echo -e "${YELLOW}Production: arn:aws:iam::${AWS_ACCOUNT_ID}:role/vifit-github-actions-production-role${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo -e "1. Set up GitHub OIDC provider (if not already done)"
echo -e "2. Configure GitHub repository secrets:"
echo -e "   - For staging environment: AWS_ROLE_TO_ASSUME = arn:aws:iam::${AWS_ACCOUNT_ID}:role/vifit-github-actions-staging-role"
echo -e "   - For production environment: AWS_ROLE_TO_ASSUME = arn:aws:iam::${AWS_ACCOUNT_ID}:role/vifit-github-actions-production-role"
echo -e "3. Test the CI/CD pipeline"
echo ""
