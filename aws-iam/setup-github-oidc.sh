#!/bin/bash

# Script to set up GitHub OIDC provider in AWS (one-time setup)
# This needs to be run once per AWS account

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔐 Setting up GitHub OIDC provider in AWS${NC}"

# Check if OIDC provider already exists
EXISTING_PROVIDER=$(aws iam list-open-id-connect-providers --query 'OpenIDConnectProviderList[?contains(Arn, `token.actions.githubusercontent.com`)].Arn' --output text)

if [ -n "$EXISTING_PROVIDER" ]; then
    echo -e "${YELLOW}⚠️  GitHub OIDC provider already exists: $EXISTING_PROVIDER${NC}"
    echo -e "${GREEN}✅ No action needed${NC}"
    exit 0
fi

echo -e "${YELLOW}📋 Creating GitHub OIDC provider...${NC}"

# Create the OIDC provider
PROVIDER_ARN=$(aws iam create-open-id-connect-provider \
    --url https://token.actions.githubusercontent.com \
    --client-id-list sts.amazonaws.com \
    --thumbprint-list 6938fd4d98bab03faadb97b34396831e3780aea1 \
    --query 'OpenIDConnectProviderArn' \
    --output text)

echo -e "${GREEN}✅ GitHub OIDC provider created successfully${NC}"
echo -e "${YELLOW}Provider ARN: $PROVIDER_ARN${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo -e "1. Run the create-github-actions-roles.sh script to create IAM roles"
echo -e "2. Configure GitHub repository secrets with the role ARNs"
echo ""
