#!/bin/bash

set -euo pipefail

if [ ! -d "flyway" ]; then
  echo "Directory 'flyway' does NOT exist!"
  echo "1. Check that you are in the project base directory."
  echo "2. Usage: flyway/flyway-docker.sh"
  exit 1
fi

docker run --rm -it \
  --network devel \
  -v "$(pwd)"/flyway/conf:/flyway/conf:ro \
  -v "$(pwd)"/flyway/drivers:/flyway/drivers:ro \
  -v "$(pwd)"/flyway/sql:/flyway/sql:ro \
  --entrypoint /bin/bash \
  flyway/flyway:11
