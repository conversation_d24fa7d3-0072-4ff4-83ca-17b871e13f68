-- Insert dummy credit packages
INSERT INTO credit01package (
    id,
    name,
    currency,
    price,
    credits,
    bonus_credits,
    status,
    created_at,
    created_by,
    updated_at,
    updated_by
) VALUES
-- Basic Package SGD
('PKG001', 'Basic Package', 'SGD', 100.00, 1000.00, 0.00, 1, NOW(), 'system', NOW(), 'system'),
-- Standard Package SGD
('PKG002', 'Standard Package', 'SGD', 200.00, 2000.00, 200, 1, NOW(), 'system', NOW(), 'system'),
-- Premium Package SGD
('PKG003', 'Premium Package', 'SGD', 500.00, 5000.00, 750.00, 1, NOW(), 'system', NOW(), 'system'),
-- VIP Package SGD
('PKG004', 'VIP Package', 'SGD', 1000.00, 10000.00, 2000.00, 1, NOW(), 'system', NOW(), 'system');

-- Insert dummy exchange rates
-- Base rates: 1 SGD = 10 credits (purchase), 1 SGD = 11 credits (booking)
INSERT INTO credit02exchange_rate (
    id,
    currency,
    purchase_rate,
    booking_rate,
    effective_date,
    created_at,
    created_by,
    updated_at,
    updated_by
) VALUES
-- Singapore Dollar (Base currency)
('RATE001', 'SGD', 10, 11, CURDATE(), NOW(), 'system', NOW(), 'system');

