-- Step 1: Alter the evt02sessions table to add new columns
ALTER TABLE evt02sessions
    ADD COLUMN m_instructor_feedback LONGTEXT DEFAULT NULL COMMENT 'Feedback from member to instructor' AFTER m_feedback,
    ADD COLUMN m_studio_feedback LONGTEXT DEFAULT NULL COMMENT 'Feedback from member to studio' AFTER m_instructor_feedback,
    ADD COLUMN is_private TINYINT DEFAULT 0 COMMENT 'Flag to indicate if feedback is private' AFTER m_studio_feedback;

-- Step 2: Migrate existing m_feedback data to m_instructor_feedback
UPDATE evt02sessions
SET m_instructor_feedback = m_feedback
WHERE m_has_feedback;