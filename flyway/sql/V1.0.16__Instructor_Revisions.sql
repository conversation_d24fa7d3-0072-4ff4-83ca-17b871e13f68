select * from instr01instructors;

/*
alter table instr01instructors drop column onboarded;
*/

alter table instr01instructors
  add column onboarded tinyint default '0' after reg_at;
alter table instr01instructors
  add index(onboarded);

drop view if exists pub02instructors;
create view pub02instructors
  as select * from instr01instructors;

drop view if exists `vw_usr03fave_instrs`;
create view `vw_usr03fave_instrs`
  as
  select f.user_id, i.* from instr01instructors i
    inner join usr03fave_instrs f on (i.id = f.instr_id);
