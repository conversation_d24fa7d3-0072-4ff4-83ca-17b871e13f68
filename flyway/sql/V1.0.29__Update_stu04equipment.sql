-- Step 1: Add a new column for is_private
ALTER TABLE stu04equipment
ADD COLUMN is_private TINYINT DEFAULT 0 AFTER descr;

-- Step 2: Update is_private to true (1) where privacy contains 'private'
UPDATE stu04equipment
SET is_private = 1
WHERE privacy LIKE '%private%';

-- Step 3: Drop the privacy column
ALTER TABLE stu04equipment DROP COLUMN privacy;

-- Step 4: Rename is_private to privacy
ALTER TABLE stu04equipment
CHANGE COLUMN is_private privacy TINYINT DEFAULT 0;

-- Verify the changes
SELECT * FROM stu04equipment;