/*
drop table if exists evt03cancel_req;
*/
CREATE TABLE `evt03cancel_req` (
  `id`         varchar(50) NOT NULL,
  `user_id`    varchar(50) NOT NULL,
  `event_id`   varchar(50) NOT NULL,
  `at`         datetime NOT NULL,
  `free`       tinyint DEFAULT '0',
  `deleted`    tinyint DEFAULT '0',
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> (`user_id`),
  <PERSON>E<PERSON> (`event_id`),
  <PERSON>E<PERSON> (`deleted`)
) ENGINE=InnoDB;

/*
alter table evt01events drop column cancel_free;
*/
alter table evt01events
  add column cancel_free tinyint default null after cancelled_by_id;
alter table evt01events
  add index(cancel_free);
