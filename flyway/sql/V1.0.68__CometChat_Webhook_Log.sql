-- Create table for logging CometChat webhook events
CREATE TABLE cometchat01webhook_log (
  id                    VARCHAR(50) NOT NULL PRIMARY KEY,
  `trigger`             VARCHAR(100) NOT NULL,
  category              VARCHAR(100),
  message_id            VARCHAR(100),
  sender_uid            VARCHAR(100),
  sender_user_type      VARCHAR(50),
  receiver_uid          VARCHAR(100),
  receiver_user_type    VARCHAR(50),
  receiver_type         VARCHAR(50),
  conversation_id       VARCHAR(100),
  message_type          VARCHAR(50),
  message_text          TEXT,
  action                VARCHAR(50),
  payload               LONGTEXT NOT NULL,
  processed             BOOLEAN NOT NULL DEFAULT FALSE,
  error                 TEXT,
  created_at            DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  processed_at          DATETIME,
  INDEX idx_trigger (`trigger`),
  INDEX idx_category (category),
  INDEX idx_message_id (message_id),
  INDEX idx_sender_uid (sender_uid),
  INDEX idx_sender_user_type (sender_user_type),
  INDEX idx_receiver_uid (receiver_uid),
  INDEX idx_receiver_user_type (receiver_user_type),
  INDEX idx_conversation_id (conversation_id),
  INDEX idx_action (action),
  INDEX idx_processed (processed),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci; 