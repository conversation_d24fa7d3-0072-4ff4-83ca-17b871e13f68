-- Add cancel_by and cancel_reason columns to evt03cancel_req
ALTER TABLE `evt03cancel_req`
  ADD COLUMN `req_by` varchar(50) NOT NULL DEFAULT '' AFTER `id`,
  ADD COLUMN `reason` text DEFAULT NULL AFTER `free`,
  ADD COLUMN `approved` tinyint DEFAULT '0' AFTER `reason`,
  ADD COLUMN `approved_at` datetime DEFAULT NULL AFTER `approved`,
  ADD COLUMN `approved_by` varchar(255) DEFAULT NULL AFTER `approved_at`;

-- Add indexes for new columns
ALTER TABLE `evt03cancel_req`
  ADD INDEX (`req_by`),
  ADD INDEX (`approved`);

-- Update existing records to set cancel_by as 'Member' since all existing records are member cancellations
UPDATE `evt03cancel_req`
SET
    `req_by` = 'Member',
    `approved` = 1,
    `approved_at` = `at`,
    `approved_by` = `created_by`
WHERE `req_by` = '';