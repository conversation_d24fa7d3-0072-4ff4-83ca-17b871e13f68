-- Create table for logging SMS OTP events
CREATE TABLE sms01otp_log (
  id            BIGINT AUTO_INCREMENT PRIMARY KEY,
  user_id       VARCHAR(50) NOT NULL,
  phone_number  VARCHAR(50) NOT NULL,
  action        ENUM('send', 'verify') NOT NULL,
  status        VARCHAR(32) NOT NULL, -- e.g. success, failed
  twilio_sid    VARCHAR(64),
  code          VARCHAR(16), -- for verify attempts
  raw_data      TEXT, -- raw data from twilio
  error         TEXT,
  created_at    DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at    DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
);