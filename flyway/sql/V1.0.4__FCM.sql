-- drop table if exists fcm01tokens;
CREATE TABLE `fcm01tokens` (
  `user_id`    varchar(50) NOT NULL,
  `token`      longtext,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB;

select * from fcm01tokens;

-- drop table if exists fcm02config;
CREATE TABLE `fcm02config` (
  `user_id`        varchar(50) NOT NULL,
  `blackout_start` time NOT NULL DEFAULT '21:00:00',
  `blackout_end`   time NOT NULL DEFAULT '07:00:00',
  `created_at`     datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`     varchar(255) NOT NULL,
  `updated_at`     datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`     varchar(255) DEFAULT NULL,
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB;

select * from fcm02config;

-- drop table if exists fcm03messages;
CREATE TABLE `fcm03messages` (
  `id`         bigint NOT NULL AUTO_INCREMENT,
  `user_id`    varchar(50) NOT NULL,
  `category`   varchar(255) DEFAULT NULL,
  `title`      varchar(255) NOT NULL,
  `body`       varchar(255) NOT NULL,
  `go`         varchar(500) DEFAULT NULL,
  `at`         datetime NOT NULL,
  `sched_at`   datetime NOT NULL,
  `status`     varchar(25) NOT NULL,
  `attempts`   int DEFAULT '0',
  `read`       tinyint DEFAULT '0',
  `read_at`    datetime DEFAULT NULL,
  `fcm_response` text,
  `deleted`    tinyint DEFAULT '0',
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY (`user_id`),
  KEY (`at`),
  KEY (`sched_at`),
  KEY (`status`),
  KEY (`attempts`),
  KEY (`read`),
  KEY (`deleted`)
) ENGINE=InnoDB;

select * from fcm03messages;
