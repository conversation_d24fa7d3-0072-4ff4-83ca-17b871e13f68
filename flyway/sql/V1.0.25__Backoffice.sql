-- alter table stu01studios drop column pend_approval;
-- alter table stu02users drop column main_user;
-- alter table instr01instructors drop column pend_approval;

alter table stu01studios add column pend_approval tinyint not null default 0 after active;
alter table stu01studios add index pend_approval(pend_approval);

alter table stu01studios add column main_user_id varchar(50) default null after id;
alter table stu01studios add index main_user_id(main_user_id);

alter table stu02users add column main_user tinyint not null default 0 after studio_id;
alter table stu02users add index main_user(main_user);

alter table instr01instructors add column pend_approval tinyint not null default 0 after onboarded;
alter table instr01instructors add index pend_approval(pend_approval);

-- desc stu01studios;
-- desc stu02users;
-- desc instr01instructors;

-- show create table stu01studios;
-- show create table instr01instructors;

-- drop table if exists pub01studios;
-- drop table if exists pub02instructors;

drop view if exists pub01studios;
CREATE TABLE `pub01studios` (
  `id` varchar(50) NOT NULL,
  `main_user_id` varchar(50) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `descr` longtext,
  `contact_no` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `address` longtext NOT NULL,
  `place_id` longtext NOT NULL,
  `geocode` longtext,
  `lat` varchar(100) DEFAULT NULL,
  `lng` varchar(100) DEFAULT NULL,
  `latlng` varchar(100) DEFAULT NULL,
  `instrless` tinyint NOT NULL DEFAULT '0',
  `facebook` varchar(255) DEFAULT NULL,
  `instagram` varchar(255) DEFAULT NULL,
  `usage` longtext,
  `pictures` longtext NOT NULL,
  `videos` longtext NOT NULL,
  `active` tinyint DEFAULT '0',
  /*
  `pend_approval` tinyint NOT NULL DEFAULT '0',
  */
  `deleted` tinyint DEFAULT '0',
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `active` (`active`),
  KEY `deleted` (`deleted`),
  KEY `instrless` (`instrless`),
  KEY `latlng` (`latlng`),
  -- KEY `pend_approval` (`pend_approval`),
  KEY `main_user_id` (`main_user_id`)
) ENGINE=InnoDB;
-- select * from pub01studios;

drop view if exists pub02instructors;
CREATE TABLE `pub02instructors` (
  `id` varchar(50) NOT NULL,
  `fbase_uid` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `descr` longtext,
  `facebook` varchar(255) DEFAULT NULL,
  `instagram` varchar(255) DEFAULT NULL,
  `certs` longtext,
  `specs` longtext,
  `pictures` longtext NOT NULL,
  `reg_at` datetime NOT NULL,
  `onboarded` tinyint DEFAULT '0',
  /*
  `pend_approval` tinyint NOT NULL DEFAULT '0',
  */
  `deleted` tinyint DEFAULT '0',
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fbase_uid` (`fbase_uid`),
  KEY `deleted` (`deleted`),
  KEY `onboarded` (`onboarded`)
  -- KEY `pend_approval` (`pend_approval`)
) ENGINE=InnoDB;
-- select * from pub02instructors;

-- desc stu01studios;
-- desc instr01instructors;
-- desc evt01events;
-- alter table stu01studios drop column bo_remarks;
-- alter table instr01instructors drop column bo_remarks;
-- alter table evt01events drop column bo_remarks;

alter table stu01studios add column bo_remarks longtext after pend_approval;
alter table instr01instructors add column bo_remarks longtext after pend_approval;
alter table evt01events add column bo_remarks longtext after i_noshow_remarks;

-- drop table if exists adm02studio_stars;
CREATE TABLE `adm02studio_stars` (
  `admin_id`  varchar(50) NOT NULL,
  `studio_id` varchar(50) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  PRIMARY KEY (`admin_id`, `studio_id`)
) ENGINE=InnoDB;

-- drop table if exists adm03instr_stars;
CREATE TABLE `adm03instr_stars` (
  `admin_id`  varchar(50) NOT NULL,
  `instr_id`  varchar(50) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  PRIMARY KEY (`admin_id`, `instr_id`)
) ENGINE=InnoDB;

-- drop table if exists adm04booking_stars;
CREATE TABLE `adm04booking_stars` (
  `admin_id`   varchar(50) NOT NULL,
  `booking_id` varchar(50) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  PRIMARY KEY (`admin_id`, `booking_id`)
) ENGINE=InnoDB;

-- drop view if exists stu01studio_main_user;
create view stu01studio_main_user
as
select
  u.name as main_user,
  u.fbase_email as main_user_email,
  u.job_title as main_user_job_title,
  u.contact_no as main_user_contact_no,
  s.* from stu01studios s
  left join stu02users u on (u.id = s.main_user_id);

-- drop view if exists evt01booking_details;
create view evt01booking_details
as
select
  -- m.id as member_id,
  m.display_name as member_display_name,
  m.full_name as member_full_name,
  -- s.id as studio_id,
  s.name as studio_name,
  -- e.id as equip_id,
  e.name as equip_name,
  -- i.id as instr_id,
  i.name as instr_name,
  b.*
  from evt01events b
  inner join usr01members m on (m.id = b.member_id)
  inner join pub01studios s on (s.id = b.studio_id)
  inner join stu04equipment e on (e.id = b.equip_id)
  left  join pub02instructors i on (i.id = b.instr_id)
  where b.type = 'Booking' and b.deleted = 0;
