/*
drop table if exists usr04req_delete;
*/
CREATE TABLE `usr04req_delete` (
  `id`          bigint NOT NULL AUTO_INCREMENT,
  `at`          datetime NOT NULL,
  `fbase_uid`   varchar(50) NOT NULL,
  `fbase_email` varchar(255) NOT NULL,
  `completed`   tinyint DEFAULT '0',
  `created_at`  datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`  varchar(255) NOT NULL,
  `updated_at`  datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`  varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ix_fbase` (`fbase_uid`),
  KEY `ix_completed` (`completed`)
) ENGINE=InnoDB;
