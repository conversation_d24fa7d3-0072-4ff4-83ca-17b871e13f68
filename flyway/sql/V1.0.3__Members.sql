-- drop table if exists `usr01members`;
CREATE TABLE `usr01members` (
  `id`              varchar(50) NOT NULL,
  `fbase_uid`       varchar(50) NOT NULL,
  `fbase_email`     varchar(255) NOT NULL,
  `full_name`       varchar(255) NOT NULL,
  `display_name`    varchar(50) NOT NULL,
  `gender`          varchar(50) NOT NULL,
  `mobile_ctry`     varchar(5) NOT NULL,
  `mobile_no`       varchar(50) NOT NULL,
  `emerg_cont`      varchar(255) NOT NULL,
  `emerg_cont_ctry` varchar(5) NOT NULL,
  `emerg_cont_no`   varchar(50) NOT NULL,
  `reg_at`          datetime NOT NULL,
  `deleted`         tinyint DEFAULT '0',
  `deleted_at`      datetime DEFAULT NULL,
  `deleted_by`      varchar(255) DEFAULT NULL,
  `created_at`      datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`      varchar(255) NOT NULL,
  `updated_at`      datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`      varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY (`fbase_uid`),
  UNIQUE KEY (`fbase_email`),
  KEY (`deleted`)
) ENGINE=InnoDB;

select * from usr01members;

-- ---

-- drop table if exists `usr02fave_studios`;
CREATE TABLE `usr02fave_studios` (
  `user_id`         varchar(50) NOT NULL,
  `studio_id`       varchar(50) NOT NULL,
  `created_at`      datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`      varchar(255) NOT NULL,
  `updated_at`      datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`      varchar(255) DEFAULT NULL,
  PRIMARY KEY (`user_id`, studio_id)
) ENGINE=InnoDB;

select * from usr02fave_studios;

drop view if exists `vw_usr02fave_studios`;
create view `vw_usr02fave_studios`
  as
  select f.user_id, s.* from stu01studios s
    inner join usr02fave_studios f on (s.id = f.studio_id);

select * from vw_usr02fave_studios;

-- ---

-- drop table if exists `usr03fave_instrs`;
CREATE TABLE `usr03fave_instrs` (
  `user_id`         varchar(50) NOT NULL,
  `instr_id`        varchar(50) NOT NULL,
  `created_at`      datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`      varchar(255) NOT NULL,
  `updated_at`      datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`      varchar(255) DEFAULT NULL,
  PRIMARY KEY (`user_id`, instr_id)
) ENGINE=InnoDB;

select * from usr03fave_instrs;

drop view if exists `vw_usr03fave_instrs`;
create view `vw_usr03fave_instrs`
  as
  select f.user_id, i.* from instr01instructors i
    inner join usr03fave_instrs f on (i.id = f.instr_id);

select * from vw_usr03fave_instrs;
