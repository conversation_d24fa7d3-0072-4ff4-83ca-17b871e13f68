-- Add new fields to credit01package table
ALTER TABLE credit01package
ADD COLUMN first_time_only TINYINT NOT NULL DEFAULT 0 COMMENT '1=first-time purchase only, 0=not restricted' AFTER bonus_credits,
ADD COLUMN instructor_only TINYINT NOT NULL DEFAULT 0 COMMENT '1=instructor only, 0=available to all' AFTER first_time_only,
ADD COLUMN valid_from DATETIME DEFAULT NULL COMMENT 'Start date of package availability' AFTER instructor_only,
ADD COLUMN valid_to DATETIME DEFAULT NULL COMMENT 'End date of package availability' AFTER valid_from,
ADD COLUMN purchase_limit INT DEFAULT NULL COMMENT 'Maximum number of purchases per member' AFTER valid_to,
ADD COLUMN sequence INT DEFAULT NULL COMMENT 'Sequence number of the package' AFTER purchase_limit;

-- Create package purchase tracking table
CREATE TABLE credit04package_purchase (
    id VARCHAR(50) NOT NULL,
    member_id VARCHAR(50) NOT NULL,
    package_id VARCHAR(50) NOT NULL,
    purchase_count INT NOT NULL DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY uk_member_package (member_id, package_id),
    INDEX idx_package_id (package_id),
    INDEX idx_member_id (member_id)
) ENGINE=InnoDB;