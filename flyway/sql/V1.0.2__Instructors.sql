-- drop table if exists `instr01instructors`;
CREATE TABLE `instr01instructors` (
  `id`              varchar(50) NOT NULL,
  `fbase_uid`       varchar(50) NOT NULL,
  `name`            varchar(255) NOT NULL,
  `descr`           longtext DEFAULT NULL,
  `facebook`        varchar(255) DEFAULT NULL,
  `instagram`       varchar(255) DEFAULT NULL,
  `certs`           longtext DEFAULT NULL,
  `specs`           longtext DEFAULT NULL,
  `pictures`        longtext not null,
  `reg_at`          datetime NOT NULL,
  `deleted`         tinyint DEFAULT '0',
  `deleted_at`      datetime DEFAULT NULL,
  `deleted_by`      varchar(255) DEFAULT NULL,
  `created_at`      datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`      varchar(255) NOT NULL,
  `updated_at`      datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`      varchar(255) DEFAULT NULL,
  PRIMARY <PERSON> (`id`),
  UNIQUE KEY (`fbase_uid`),
  KEY (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- insert into instr01instructors
--   (id, fbase_uid, name, descr,
--    facebook, instagram, certs, specs,
--    pictures,
--    reg_at, created_by)
-- values
--   ('dQVy7PxHfQYNGxtz2zaVTvFv44w2', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2',
--    'Test instr 1', 'Test instr 1 desc',
--    'fb01', 'insta01', 'Test certs 1', 'Test specs 1',
--    '[]',
--    now(), 'flyway'),
--   ('instr02', 'instr02',
--    'Test instr 2', 'Test instr 2 desc',
--    'fb02', 'insta02', 'Test certs 2', 'Test specs 2',
--    '[]',
--    now(), 'flyway');

select * from instr01instructors;

-- drop table if exists `instr02rates`;
CREATE TABLE `instr02rates` (
  `id`         varchar(50)   NOT NULL,
  `instr_id`   varchar(50)   NOT NULL,
  `type`       varchar(10)   NOT NULL,
  `start`      time          NOT NULL,
  `end`        time          NOT NULL,
  `price`      varchar(10)   NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY (`instr_id`),
  KEY (`type`)
) ENGINE=InnoDB;

-- insert into instr02rates
--   (id, instr_id, type, start, end, price, created_by)
-- values
--   ('r101', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2', 'ALL', '09:00', '18:00', '100.00', 'flyway'),
--   ('r102', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2', 'ALL', '18:00', '21:00', '200.00', 'flyway'),
--   --
--   ('r201', 'instr02', 'WD',  '09:00', '18:00', '100.00', 'flyway'),
--   ('r202', 'instr02', 'WD',  '18:00', '21:00', '200.00', 'flyway'),
--   ('r203', 'instr02', 'WE',  '09:00', '18:00', '150.00', 'flyway'),
--   ('r204', 'instr02', 'WE',  '18:00', '21:00', '250.00', 'flyway'),
--   ('r205', 'instr02', 'PH',  '09:00', '18:00', '100.00', 'flyway'),
--   ('r206', 'instr02', 'PH',  '18:00', '21:00', '200.00', 'flyway');

select * from instr02rates;

-- drop table if exists `instr03work_hours`;
CREATE TABLE `instr03work_hours` (
  `id`         varchar(50)   NOT NULL,
  `instr_id`   varchar(50)   NOT NULL,
  `type`       varchar(10)   NOT NULL,
  `start`      time          NOT NULL,
  `end`        time          NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY (`instr_id`),
  KEY (`type`)
) ENGINE=InnoDB;

-- insert into instr03work_hours
--   (id, instr_id, type, start, end, created_by)
-- values
--   ('r101', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2', 'ALL', '09:00', '18:00', 'flyway'),
--   ('r102', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2', 'ALL', '18:00', '21:00', 'flyway'),
--   --
--   ('r201', 'instr02', 'WD',  '09:00', '18:00', 'flyway'),
--   ('r202', 'instr02', 'WD',  '18:00', '21:00', 'flyway'),
--   ('r203', 'instr02', 'WE',  '09:00', '18:00', 'flyway'),
--   ('r204', 'instr02', 'WE',  '18:00', '21:00', 'flyway'),
--   ('r205', 'instr02', 'PH',  '09:00', '18:00', 'flyway'),
--   ('r206', 'instr02', 'PH',  '18:00', '21:00', 'flyway');

select * from instr03work_hours;

-- drop table if exists `instr04mobility`;
CREATE TABLE `instr04mobility` (
  `id`          varchar(50)   NOT NULL,
  `instr_id`    varchar(50)   NOT NULL,
  `dow`         int           NOT NULL,
  `location_id` varchar(50)   NOT NULL,
  `distance`    bigint        NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY (`instr_id`),
  KEY (`dow`),
  KEY (`location_id`)
) ENGINE=InnoDB;

-- insert into instr04mobility
--   (id, instr_id, dow, location_id, distance, created_by)
-- values
--   ('m101', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2', '1', 'loc1', 5, 'flyway'),
--   ('m102', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2', '2', 'loc1', 5, 'flyway'),
--   ('m103', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2', '3', 'loc1', 5, 'flyway'),
--   ('m104', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2', '4', 'loc1', 5, 'flyway'),
--   ('m105', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2', '5', 'loc1', 5, 'flyway'),
--   ('m106', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2', '6', 'loc1', 5, 'flyway'),
--   ('m107', 'dQVy7PxHfQYNGxtz2zaVTvFv44w2', '7', 'loc1', 5, 'flyway');

select * from instr04mobility;

-- ---

-- drop table if exists `instr05pref_studios`;
CREATE TABLE `instr05pref_studios` (
  `user_id`     varchar(50) NOT NULL,
  `studio_id`   varchar(50) NOT NULL,
  `created_at`  datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`  varchar(255) NOT NULL,
  `updated_at`  datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`  varchar(255) DEFAULT NULL,
  PRIMARY KEY (`user_id`, studio_id)
) ENGINE=InnoDB;

select * from instr05pref_studios;

drop view if exists `vw_instr05pref_studios`;
create view `vw_instr05pref_studios`
  as
  select f.user_id, s.* from stu01studios s
    inner join instr05pref_studios f on (s.id = f.studio_id);

select * from vw_instr05pref_studios;

CREATE TABLE `instr06pictures` (
  `id`          varchar(50) NOT NULL,
  `user_id`     varchar(50) NOT NULL,
  `path`        longtext NOT NULL,
  `created_at`  datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`  varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY (`user_id`)
) ENGINE=InnoDB;

select * from instr06pictures;
