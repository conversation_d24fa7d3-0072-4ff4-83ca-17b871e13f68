ALTER TABLE instr07blackouts
  ADD start_at VARCHAR(20) GENERATED ALWAYS AS
  (concat(
    start_date,
    coalesce(
      concat(' ', date_format(start_time, '%H:%i')),
      ''
    )
  )) AFTER start_time,
  ADD end_at VARCHAR(20) GENERATED ALWAYS AS
  (concat(
    end_date,
    coalesce(
      concat(' ', date_format(end_time, '%H:%i')),
      ''
    )
  )) AFTER end_time;

ALTER TABLE instr07blackouts
    ADD INDEX (start_at),
    ADD INDEX (end_at);
