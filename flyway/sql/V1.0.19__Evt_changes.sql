select * from evt01events;
desc evt01events;

/*
alter table evt01events drop column i_noshow;
alter table evt01events drop column i_noshow_at;
alter table evt01events drop column i_noshow_by_id;
alter table evt01events drop column i_noshow_remarks;
*/

alter table evt01events
  add column i_noshow tinyint default '0' after cancel_reason,
  add column i_noshow_at datetime default null after i_noshow,
  add column i_noshow_by_id varchar(50) default null after i_noshow_at,
  add column i_noshow_remarks text after i_noshow_by_id;

alter table evt01events add index (i_noshow);

-- ---

/*
alter table evt01events drop column booking_fees;
*/

alter table evt01events
  add column booking_fees longtext after payment_req_id;

-- ---

select * from evt02sessions;
desc evt02sessions;

/*
alter table evt02sessions drop column completed;
alter table evt02sessions drop column m_has_feedback;
alter table evt02sessions drop column m_feedback;
alter table evt02sessions drop column m_no_feedback;
*/

alter table evt02sessions
  add column completed tinyint default '0' after record,
  add column m_has_feedback tinyint default '0' after record,
  add column m_feedback text after m_has_feedback,
  add column m_no_feedback tinyint default '0' after m_feedback;

alter table evt02sessions add index (completed);
alter table evt02sessions add index (m_has_feedback);
alter table evt02sessions add index (m_no_feedback);
