--
-- Public Holidays
--
-- drop table if EXISTS app02phs;
CREATE TABLE `app02phs` (
  `id`          bigint NOT NULL AUTO_INCREMENT,
  `country`     varchar(5) NOT NULL,
  `date`        date NOT NULL,
  `name`        varchar(255) NOT NULL,
  `created_at`  datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`  varchar(255) NOT NULL,
  `updated_at`  datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`  varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY (`country`),
  KEY (`date`)
) ENGINE=InnoDB;

insert into app02phs
  (country, date, name, created_by)
values
  ('SG', '2024-12-25', 'Christmas',         'db'),
  ('SG', '2025-01-01', 'New Year',          'db'),
  ('SG', '2025-01-29', 'Chinese New Year',  'db'),
  ('SG', '2025-01-30', 'Chinese New Year',  'db'),
  ('SG', '2025-03-31', '<PERSON>',   'db'),
  ('SG', '2025-04-18', 'Good Friday',       'db'),
  ('SG', '2025-05-01', 'Labour Day',        'db'),
  ('SG', '2025-05-12', 'Vesak Day' ,        'db'),
  ('SG', '2025-06-07', 'Hari Raya Haji' ,   'db'),
  ('SG', '2025-08-09', 'National Day' ,     'db'),
  ('SG', '2025-10-20', 'Deepavali' ,        'db'),
  ('SG', '2025-12-25', 'Christmas Day' ,    'db'),
  ('SG', '2026-01-01', 'New Year' ,         'db');

select * from app02phs;

--
-- Distance cache
--
-- drop table if EXISTS app03dist_cache;
CREATE TABLE `app03dist_cache` (
  `latlng_from` varchar(200) NOT NULL,
  `latlng_to`   varchar(200) NOT NULL,
  `distance`    double NOT NULL,
  `created_at`  datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at`  datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  KEY (`latlng_from`),
  KEY (`latlng_to`)
) ENGINE=InnoDB;

--
-- Generated columns for evt01
--

-- alter table evt01events drop start_at;
-- alter table evt01events drop end_at;

alter table evt01events
  add start_at varchar(20) generated always as
  (concat(
    start_date,
    coalesce(
      concat(' ', date_format(start_time, '%H:%i')),
      ''
    )
  )) after start_time;
alter table evt01events add index (start_at);

alter table evt01events
  add end_at varchar(20) generated always as
  (concat(
    end_date,
    coalesce(
      concat(' ', date_format(end_time, '%H:%i')),
      ''
    )
  )) after end_time;
alter table evt01events add index (end_at);
