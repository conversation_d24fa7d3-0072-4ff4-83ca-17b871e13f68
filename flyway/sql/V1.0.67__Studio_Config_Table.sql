-- Create a flexible config table for per-studio 3rd party integration settings
CREATE TABLE stu07studio_config (
  id           BIGINT AUTO_INCREMENT PRIMARY KEY,
  studio_id    VARCHAR(50) NOT NULL,
  config_key   VARCHAR(64) NOT NULL,
  config_value TEXT,
  created_by   <PERSON><PERSON><PERSON><PERSON>(64) DEFAULT NULL,
  updated_by   <PERSON><PERSON><PERSON><PERSON>(64) DEFAULT NULL,
  created_at   DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at   DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uniq_studio_key (studio_id, config_key),
  KEY idx_studio_id (studio_id)
); 