-- drop table if exists `stu01studios`;
CREATE TABLE `stu01studios` (
  `id`         varchar(50) NOT NULL,
  `name`       varchar(255) NOT NULL,
  `descr`      longtext DEFAULT NULL,
  `contact_no` varchar(255) NOT NULL,
  `email`      varchar(255) NOT NULL,
  `address`    longtext NOT NULL,
  `place_id`   longtext NOT NULL,
  `geocode`    longtext DEFAULT NULL,
  `facebook`   varchar(255) DEFAULT NULL,
  `instagram`  varchar(255) DEFAULT NULL,
  `usage`      longtext DEFAULT NULL,
  `active`     tinyint DEFAULT '0',
  `deleted`    tinyint DEFAULT '0',
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) DEFAULT NULL,
  <PERSON><PERSON><PERSON><PERSON> (`id`),
  <PERSON><PERSON><PERSON> (`active`),
  <PERSON><PERSON><PERSON> (`deleted`)
) ENGINE=InnoDB;

-- drop table if exists `stu02users`;
CREATE TABLE `stu02users` (
  `id`          varchar(50) NOT NULL,
  `fbase_uid`   varchar(50) NOT NULL,
  `fbase_email` varchar(255) NOT NULL,
  `studio_id`   varchar(50) NOT NULL,
  `name`        varchar(255) NOT NULL,
  `job_title`   varchar(255) NOT NULL,
  `contact_no`  varchar(255) NOT NULL,
  `admin`       tinyint DEFAULT '0',
  `active`      tinyint DEFAULT '0',
  `created_at`  datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`  varchar(255) NOT NULL,
  `updated_at`  datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`  varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY (`fbase_uid`),
  UNIQUE KEY (`fbase_email`),
  KEY (`studio_id`),
  KEY (`admin`),
  KEY (`active`)
) ENGINE=InnoDB;

alter table stu01studios add column lat varchar(100) default null after `geocode`;
alter table stu01studios add column lng varchar(100) default null after `lat`;
alter table stu01studios add column latlng varchar(100) default null after `lng`;
alter table stu01studios add index (latlng);

alter table stu01studios add column instrless tinyint not null default '0' after `latlng`;
alter table stu01studios add index (instrless);

alter table stu01studios add column pictures longtext not null after `usage`;
update stu01studios set pictures = '[]', updated_at = updated_at where pictures = '';

alter table stu01studios add column videos longtext not null after `pictures`;
update stu01studios set videos = '[]', updated_at = updated_at where videos = '';

select * from stu01studios;

-- drop table if exists `stu03equip_types`;
CREATE TABLE `stu03equip_types` (
  `id`          varchar(50) NOT NULL,
  `name`        varchar(255) NOT NULL,
  `deleted`     tinyint DEFAULT '0',
  `deleted_at`  datetime DEFAULT NULL,
  `deleted_by`  varchar(255) DEFAULT NULL,
  `created_at`  datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`  varchar(255) NOT NULL,
  `updated_at`  datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`  varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY (`deleted`)
) ENGINE=InnoDB;

insert into stu03equip_types
  (id, name, created_by)
values
  ('t01', 'Reformer',        'flyway'),
  ('t02', 'Tower',           'flyway'),
  ('t03', 'Pilates chair',   'flyway'),
  ('t04', 'Cadillac table',  'flyway'),
  ('t05', 'Ladder barrel',   'flyway'),
  ('t06', 'Arc barrel',      'flyway'),
  ('t07', 'Spine corrector', 'flyway'),
  ('t08', 'Floor mat',       'flyway'),
  ('t09', 'Others',          'flyway');

-- alter table stu03equip_types drop column sort;
alter table stu03equip_types add column sort int not null default '0' after name;
update stu03equip_types set sort = substr(id, 3, 1) where id like 't0%';

-- alter table stu03equip_types drop index name;
alter table stu03equip_types add unique (name);

select * from stu03equip_types;

-- drop table if exists `stu04equipment`;
CREATE TABLE `stu04equipment` (
  `id`          varchar(50) NOT NULL,
  `studio_id`   varchar(50) NOT NULL,
  `type_id`     varchar(50) NOT NULL,
  `color`       varchar(10) NOT NULL,
  `code`        varchar(50) NOT NULL,
  `short_name`  varchar(100) NOT NULL,
  `name`        varchar(255) NOT NULL,
  `descr`       longtext DEFAULT NULL,
  `privacy`     varchar(255) DEFAULT NULL,
  `start_date`  DATE DEFAULT NULL,
  `end_date`    DATE DEFAULT NULL,
  `deleted`     tinyint DEFAULT '0',
  `deleted_at`  datetime DEFAULT NULL,
  `deleted_by`  varchar(255) DEFAULT NULL,
  `created_at`  datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`  varchar(255) NOT NULL,
  `updated_at`  datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`  varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY (`studio_id`),
  KEY (`type_id`),
  KEY (`deleted`)
) ENGINE=InnoDB;

-- insert into stu04equipment
--   (id, studio_id, type_id, color, code, short_name, name, privacy, created_by)
-- values
--   ('e01', 'stu001', 't01', '#000111', 'RF001', 'RF 001', 'Reformer A', 'Open Area',   'flyway'),
--   ('e02', 'stu001', 't02', '#000222', 'TW001', 'TW 001', 'Tower A',    'Open Area',   'flyway'),
--   ('e03', 'stu001', 't02', '#000333', 'TW002', 'TW 002', 'Tower B',    'Corner Area', 'flyway');

select * from stu04equipment;

-- drop table if exists `stu05equip_avail`;
CREATE TABLE `stu05equip_avail` (
  `id`          varchar(50) NOT NULL,
  `equip_id`    varchar(50) NOT NULL,
  `type`        varchar(10) NOT NULL,
  `start`       TIME NOT NULL,
  `end`         TIME NOT NULL,
  `price`       varchar(50) NOT NULL,
  `created_at`  datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`  varchar(255) NOT NULL,
  `updated_at`  datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`  varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY (`equip_id`),
  KEY (`type`)
) ENGINE=InnoDB;

-- insert into stu05equip_avail
--   (id, equip_id, type, start, end, price, created_by)
-- values
--   ('a101', 'e01', 'ALL', '09:00', '18:00', '100.00', 'flyway'),
--   ('a102', 'e01', 'ALL', '18:00', '21:00', '200.00', 'flyway'),
--   --
--   ('a201', 'e02', 'WD',  '09:00', '18:00', '100.00', 'flyway'),
--   ('a202', 'e02', 'WD',  '18:00', '21:00', '200.00', 'flyway'),
--   ('a203', 'e02', 'WE',  '09:00', '18:00', '150.00', 'flyway'),
--   ('a204', 'e02', 'WE',  '18:00', '21:00', '250.00', 'flyway'),
--   ('a205', 'e02', 'PH',  '09:00', '18:00', '100.00', 'flyway'),
--   ('a206', 'e02', 'PH',  '18:00', '21:00', '200.00', 'flyway');

select * from stu05equip_avail;

-- drop view if exists `vw_stu04equipment`;
create view `vw_stu04equipment`
  as
  select e.*, t.name as type_name from stu04equipment e
    inner join stu03equip_types t on (t.id = e.type_id);

select * from vw_stu04equipment;

-- drop view if exists `vw_stu05equip_avail`;
create view `vw_stu05equip_avail`
  as
  select a.*, e.studio_id from stu05equip_avail a
    inner join stu04equipment e on (e.id = a.equip_id);

select * from vw_stu05equip_avail;

-- drop table if exists `stu06pictures`;
CREATE TABLE `stu06pictures` (
  `id`          varchar(50) NOT NULL,
  `user_id`     varchar(50) NOT NULL,
  `path`        longtext NOT NULL,
  `created_at`  datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`  varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY (`user_id`)
) ENGINE=InnoDB;

select * from stu06pictures;

-- drop table if exists `evt01events`;
CREATE TABLE `evt01events` (
  `id`              varchar(50) NOT NULL,
  `studio_id`       varchar(50) NOT NULL,
  `type`            varchar(50) NOT NULL,
  `booking_ref`     varchar(50) DEFAULT NULL,
  `name`            varchar(200) DEFAULT NULL,
  `int_remarks`     longtext DEFAULT NULL,
  `start_date`      date NOT NULL,
  `start_time`      time default NULL,
  `end_date`        date NOT NULL,
  `end_time`        time default NULL,
  `full_day`        tinyint DEFAULT '0',
  `color`           varchar(10) NOT NULL,
  `equip_id`        varchar(50) DEFAULT NULL,
  `instr_id`        varchar(50) DEFAULT NULL,
  `member_id`       varchar(50) DEFAULT NULL,
  `cancelled`       tinyint DEFAULT '0',
  `cancelled_at`    datetime DEFAULT NULL,
  `cancelled_by_id` varchar(50) DEFAULT NULL,
  `cancel_reason`   text DEFAULT NULL,
  `deleted`         tinyint DEFAULT '0',
  `deleted_at`      datetime DEFAULT NULL,
  `deleted_by`      varchar(255) DEFAULT NULL,
  `created_at`      datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`      varchar(255) NOT NULL,
  `updated_at`      datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`      varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY (`studio_id`),
  KEY (`type`),
  KEY (`booking_ref`),
  KEY (`start_date`),
  KEY (`end_date`),
  KEY (`equip_id`),
  KEY (`instr_id`),
  KEY (`member_id`),
  KEY (`cancelled`),
  KEY (`cancelled_by_id`),
  KEY (`deleted`)
) ENGINE=InnoDB;

/*
- Booking
- EquipmentBlocked
- StudioBlocked
*/
-- insert into evt01events
--   (id, studio_id, type,
--    name, start_date, end_date, full_day, color, created_by)
-- values
--   ('evt01', 'stu001', 'StudioBlocked',
--    'Annual D&D', '2024-12-12', '2024-12-12', true, '#000111', 'flyway'),
--   ('evt02', 'stu001', 'StudioBlocked',
--    'Christmas',  '2024-12-25', '2024-12-25', true, '#000111', 'flyway');

-- insert into evt01events
--   (id, studio_id, type,
--    name, start_date, start_time, end_date, end_time, color, created_by)
-- values
--   ('evt11', 'stu001', 'StudioBlocked',
--    'Aircon Maintenance', '2024-12-03', '09:00', '2024-12-03', '12:00', '#111000', 'flyway');
