-- Create terms and conditions table
CREATE TABLE app06terms (
    id VARCHAR(50) PRIMARY KEY,
    version INT NOT NULL AUTO_INCREMENT,
    content TEXT NOT NULL,
    status BOOLEAN NOT NULL DEFAULT true,
    created_by VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by <PERSON><PERSON><PERSON><PERSON>(50),
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted BOOLEAN NOT NULL DEFAULT false,
    deleted_by VARCHAR(50),
    deleted_at DATETIME,
    UNIQUE KEY uk_version (version, deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial terms and conditions
INSERT INTO app06terms (
    id, content, status, created_by
) VALUES (
    UUID(),
    'Welcome to ViFit! By using our mobile application, you agree to these terms and conditions...',
    true,
    'system'
); 