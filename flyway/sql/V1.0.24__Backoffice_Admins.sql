CREATE TABLE `adm01admins` (
  `id` varchar(50) NOT NULL,
  `fbase_uid`      varchar(50) NOT NULL,
  `fbase_email`    varchar(255) NOT NULL,
  `name`           varchar(255) NOT NULL,
  `notes_int`      text,
  `last_at`        datetime NOT NULL,
  `last_by_uid`    varchar(50) NOT NULL,
  `last_by`        varchar(255) NOT NULL,
  `deleted`        tinyint DEFAULT '0',
  `deleted_at`     datetime DEFAULT NULL,
  `deleted_by`     varchar(255) DEFAULT NULL,
  `created_at`     datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`     varchar(255) NOT NULL,
  `updated_at`     datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`     varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `fbase_uid` (`fbase_uid`),
  UNIQUE KEY `fbase_email` (`fbase_email`),
  KEY `ix_deleted` (`deleted`)
) ENGINE=InnoDB;
