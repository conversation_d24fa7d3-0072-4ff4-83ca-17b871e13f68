-- drop table if exists app04exercises;
CREATE TABLE `app04exercises` (
  `id`          bigint NOT NULL AUTO_INCREMENT,
  `no`          varchar(255) default NULL,
  `category`    varchar(255) default NULL,
  `name`        varchar(255) default NULL,
  `start_pos`   text,
  `action_type` varchar(255) default NULL,
  `action_name` text,
  `reps_min`    int default NULL,
  `reps_max`    int default NULL,
  `focus`       text,
  `essence`     text,
  `mod_name`    varchar(255) default NULL,
  `mod_descr`   text,
  `created_at`  datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by`  varchar(255) NOT NULL,
  `updated_at`  datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by`  varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;
-- select * from app03exercises;
