-- Create evt04tutorial_feedback table for public tutorial feedback submissions
CREATE TABLE evt04tutorial_feedback (
  id INT AUTO_INCREMENT PRIMARY KEY,
  booking_ref VARCHAR(64) NOT NULL,
  member_id VARCHAR(64) NOT NULL,
  video VARCHAR(255) NOT NULL,
  rating INT NOT NULL,
  feedback TEXT NOT NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(64) DEFAULT NULL,
  INDEX idx_booking_ref (booking_ref),
  INDEX idx_member_id (member_id)
);