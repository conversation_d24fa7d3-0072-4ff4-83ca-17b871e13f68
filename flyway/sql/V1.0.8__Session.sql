-- drop table if exists evt02sessions;
CREATE TABLE `evt02sessions` (
  `event_id`   varchar(50) NOT NULL,
  `instr_id`   varchar(50) DEFAULT NULL,
  `member_id`  varchar(50) DEFAULT NULL,
  `state`      longtext default NULL,
  `target`     longtext default NULL,
  `plan`       longtext default NULL,
  `plan_cfm`   tinyint default '0',
  `record`     longtext default NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) NOT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`event_id`),
  <PERSON>E<PERSON> (`instr_id`),
  KEY (`member_id`)
) ENGINE=InnoDB;

/*
insert into evt02sessions (
  event_id, instr_id, member_id, state, target,
  created_by
) select
    id, instr_id, member_id, '', '', 'flyway'
from evt01events
where type = 'Booking' and booking_status = 'Confirmed' and deleted is false;
*/

select * from evt02sessions;
