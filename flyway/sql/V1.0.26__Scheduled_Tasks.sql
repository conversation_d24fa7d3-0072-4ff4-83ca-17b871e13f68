-- select * from evt01events;
-- alter table evt01events drop column notified_complete;
-- alter table evt01events drop column notified_feedback;

alter table evt01events
  add column notified_complete tinyint default '0' after i_noshow_remarks;
alter table evt01events
  add column notified_feedback tinyint default '0' after notified_complete;

alter table evt01events add index notified_complete(notified_complete);
alter table evt01events add index notified_feedback(notified_feedback);

-- re-create view
-- ref: V1.0.25__Backoffice.sql
drop view if exists evt01booking_details;
create view evt01booking_details
as
select
  -- m.id as member_id,
  m.display_name as member_display_name,
  m.full_name as member_full_name,
  -- s.id as studio_id,
  s.name as studio_name,
  -- e.id as equip_id,
  e.name as equip_name,
  -- i.id as instr_id,
  i.name as instr_name,
  b.*
  from evt01events b
  inner join usr01members m on (m.id = b.member_id)
  inner join pub01studios s on (s.id = b.studio_id)
  inner join stu04equipment e on (e.id = b.equip_id)
  left  join pub02instructors i on (i.id = b.instr_id)
  where b.type = 'Booking' and b.deleted = 0;
