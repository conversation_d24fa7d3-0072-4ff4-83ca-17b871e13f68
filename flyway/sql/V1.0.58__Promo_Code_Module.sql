-- V1.0.58__Promo_Code_Module.sql
-- Promo Code Module: Campaigns, Codes, and Usage Tracking

-- 1. Promotion Table
CREATE TABLE promo01promotion (
    id              BIGINT AUTO_INCREMENT PRIMARY KEY,
    name            <PERSON><PERSON><PERSON><PERSON>(128) NOT NULL,
    description     TEXT,
    type            ENUM('booking', 'topup') NOT NULL,
    value           INT NOT NULL,
    start_date      DATETIME NOT NULL,
    end_date        DATETIME NOT NULL,
    per_user_limit  INT DEFAULT NULL,
    global_limit    INT DEFAULT NULL,
    status          ENUM('active', 'inactive') NOT NULL DEFAULT 'inactive',
    created_by      VA<PERSON>HA<PERSON>(50) NOT NULL,
    created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by      VARCHA<PERSON>(50) DEFAULT NULL,
    updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_by      VARCHA<PERSON>(50) DEFAULT NULL,
    deleted_at      DATETIME DEFAULT NULL
);

-- 2. Promo Code Table
CREATE TABLE promo02promo_code (
    id                BIGINT AUTO_INCREMENT PRIMARY KEY,
    promotion_id      BIGINT NOT NULL,
    code              VARCHAR(64) NOT NULL UNIQUE,
    code_limit        INT DEFAULT NULL,
    assigned_user_id  VARCHAR(50) DEFAULT NULL,
    created_by        VARCHAR(50) NOT NULL,
    created_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by        VARCHAR(50) NULL,
    updated_at        DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at        DATETIME DEFAULT NULL,
    deleted_by        VARCHAR(50) DEFAULT NULL,
    status            ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    INDEX idx_promotion_id (promotion_id)
);

-- 3. Promo Code Usage Table
CREATE TABLE promo03promo_code_usage (
    id              BIGINT AUTO_INCREMENT PRIMARY KEY,
    promo_code_id   BIGINT NOT NULL,
    user_id         VARCHAR(50) NOT NULL,
    used_at         DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    event_id        VARCHAR(50) DEFAULT NULL, -- Link to booking/event
    ctrx_id         VARCHAR(50) DEFAULT NULL, -- Link to topup/credit transaction
    INDEX idx_code_user (promo_code_id, user_id),
    INDEX idx_event_id (event_id),
    INDEX idx_ctrx_id (ctrx_id)
);