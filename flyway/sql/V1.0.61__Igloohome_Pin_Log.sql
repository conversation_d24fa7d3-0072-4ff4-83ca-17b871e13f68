-- Create table for logging Igloohome create pin API calls
CREATE TABLE pin01igloohome_log (
  id           BIGINT AUTO_INCREMENT PRIMARY KEY,
  booking_id   VARCHAR(50) NOT NULL,
  member_id    VARCHAR(50) NOT NULL,
  job_id       VARCHAR(100),
  pin          VARCHAR(20),
  access_name  VA<PERSON>HA<PERSON>(255),
  start_iso    VARCHAR(40),
  end_iso      VARCHAR(40),
  api_response TEXT,
  error        TEXT,
  created_at   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by   VARCHAR(255) NOT NULL,
  INDEX idx_booking_id (booking_id),
  INDEX idx_member_id (member_id)
) ENGINE=InnoDB; 