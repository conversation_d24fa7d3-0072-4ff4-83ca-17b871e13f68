CREATE TABLE app07country_info (
  code VARCHAR(2) PRIMARY KEY,
  name VA<PERSON>HAR(64) NOT NULL,
  timezone VARCHAR(64) NOT NULL,
  currency VARCHAR(3) NOT NULL,
  currency_name VARCHAR(32) NOT NULL,
  symbol VARCHAR(8) NOT NULL
);

INSERT INTO app07country_info (code, name, timezone, currency, currency_name, symbol) VALUES
('SG', 'Singapore', 'Asia/Singapore', 'SGD', 'Singapore Dollar', '$'),
('MY', 'Malaysia', 'Asia/Kuala_Lumpur', 'MYR', 'Malaysian Ringgit', 'RM'),
('US', 'United States', 'America/New_York', 'USD', 'US Dollar', '$'),
('GB', 'United Kingdom', 'Europe/London', 'GBP', 'Pound Sterling', '£'),
('JP', 'Japan', 'Asia/Tokyo', 'JPY', 'Yen', '¥'); 