-- re-create evt01booking_details view

drop view if exists evt01booking_details;
create view evt01booking_details
as
select
  m.display_name as member_display_name,
  m.full_name as member_full_name,
  s.name as studio_name,
  e.name as equip_name,
  i.name as instr_name,
  b.*,
  case
    when cr.id is not null and not cr.approved then 'pendingCancel'
    when b.cancelled = 1 and b.cancelled_with_refund = 1 then 'refunded'
    when b.cancelled = 1 then 'cancelled'
    when start_at > now() then 'upcoming'
    when start_at <= now() and end_at >= now() then 'ongoing'
    when end_at < now() then 'ended'
    else 'upcoming'
  end as status,
  cr.id as cancel_req_id,
  cr.req_by as cancel_req_by,
  cr.user_id as cancel_req_user_id,
  cr.free as cancel_req_free,
  cr.reason as cancel_req_reason,
  cr.approved as cancel_req_approved,
  cr.approved_at as cancel_req_approved_at,
  cr.approved_by as cancel_req_approved_by,
  cr.at as cancel_req_at
  from evt01events b
  inner join usr01members m on (m.id = b.member_id)
  inner join pub01studios s on (s.id = b.studio_id)
  inner join stu04equipment e on (e.id = b.equip_id)
  left  join pub02instructors i on (i.id = b.instr_id)
  left join evt03cancel_req cr on (cr.event_id = b.id and cr.approved = 0 and cr.deleted = 0 and cr.req_by in ('Studio', 'Instructor'))
  where b.type = 'Booking' and b.deleted = 0;