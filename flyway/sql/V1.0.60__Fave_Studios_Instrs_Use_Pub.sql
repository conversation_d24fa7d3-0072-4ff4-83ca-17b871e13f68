-- Migration: Update favorite studios/instructors views to use public tables

-- Update vw_usr02fave_studios to join with pub01studios
DROP VIEW IF EXISTS `vw_usr02fave_studios`;
CREATE VIEW `vw_usr02fave_studios` AS
  SELECT f.user_id, s.* FROM pub01studios s
    INNER JOIN usr02fave_studios f ON (s.id = f.studio_id);

-- Update vw_usr03fave_instrs to join with pub02instructors
DROP VIEW IF EXISTS `vw_usr03fave_instrs`;
CREATE VIEW `vw_usr03fave_instrs` AS
  SELECT f.user_id, i.* FROM pub02instructors i
    INNER JOIN usr03fave_instrs f ON (i.id = f.instr_id); 