-- Member credits table
CREATE TABLE usr05credit (
    member_id VARCHAR(50) NOT NULL,
    current_credits DECIMAL(10,2) NOT NULL DEFAULT 0,
    credit_expired_date DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(255) NOT NULL,
    PRIMARY KEY (member_id)
) ENGINE=InnoDB;

-- Credit packages table
-- status: 1=active, 0=inactive
CREATE TABLE credit01package (
    id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    credits DECIMAL(10,2) NOT NULL,
    bonus_credits DECIMAL(10,2) NOT NULL DEFAULT 0,
    status TINYINT NOT NULL DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(255) NOT NULL,
    PRIMARY KEY (id)
) ENGINE=InnoDB;

-- Exchange rates table
CREATE TABLE credit02exchange_rate (
    id VARCHAR(50) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    purchase_rate DECIMAL(10,4) NOT NULL,
    booking_rate DECIMAL(10,4) NOT NULL,
    effective_date DATE NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(255) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY uk_currency_date (currency, effective_date)
) ENGINE=InnoDB;

-- Credit transactions table
-- type: 0=topup, 1=booking, 2=refund, 3=expiry, 4=renewal
-- status: 0=success (others)， 1=pendingPayment, 2=confirmPayment, 3=cancelledPayment
CREATE TABLE credit03transaction (
    id VARCHAR(50) NOT NULL,
    member_id VARCHAR(50) NOT NULL,
    type TINYINT NOT NULL COMMENT '0=topup, 1=booking, 2=refund, 3=expiry, 4=renewal',
    amount DECIMAL(10,2) NOT NULL,
    status TINYINT NOT NULL COMMENT '0=success, 1=pendingPayment, 2=confirmPayment, 3=cancelledPayment',
    transaction_ref varchar(50) DEFAULT NULL,
    payment_req_id VARCHAR(50) DEFAULT NULL,
    payment_id VARCHAR(50) DEFAULT NULL,
    package_id VARCHAR(50) DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(255) DEFAULT NULL,
    deleted TINYINT DEFAULT '0',
    deleted_at DATETIME DEFAULT NULL,
    deleted_by VARCHAR(255) DEFAULT NULL,
    PRIMARY KEY (id),
    INDEX idx_member_id (member_id),
    INDEX idx_payment_req_id (payment_req_id),
    INDEX idx_payment_id (payment_id)
) ENGINE=InnoDB;