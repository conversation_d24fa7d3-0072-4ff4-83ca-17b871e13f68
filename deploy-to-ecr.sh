#!/bin/bash

# Usage:
#   ./deploy-to-ecr.sh <AWS_ACCOUNT_ID> [AWS_REGION] [IMAGE_TAG]
# Or set AWS_ACCOUNT_ID, AWS_REGION, IMAGE_TAG as environment variables.

# Default configuration
AWS_REGION="${2:-${AWS_REGION:-ap-southeast-1}}"  # 2nd arg, or env, or default
AWS_ACCOUNT_ID="${1:-$AWS_ACCOUNT_ID}"           # 1st arg or env
ECR_REPOSITORY_NAME="vifit/nodejs-api"
IMAGE_TAG="${3:-${IMAGE_TAG:-latest}}"           # 3rd arg, or env, or default

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🚀 Starting ECR deployment...${NC}"

# Check if AWS_ACCOUNT_ID is set
if [ -z "$AWS_ACCOUNT_ID" ]; then
    read -p "Enter AWS Account ID: " AWS_ACCOUNT_ID
    if [ -z "$AWS_ACCOUNT_ID" ]; then
        echo -e "${RED}❌ AWS Account ID is required.${NC}"
        exit 1
    fi
fi

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Set ECR repository URI
ECR_REPOSITORY_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}"

echo -e "${YELLOW}📦 Building Docker image...${NC}"
docker build -t ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} .

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Docker build failed${NC}"
    exit 1
fi

echo -e "${YELLOW}🔐 Logging into ECR...${NC}"
aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${ECR_REPOSITORY_URI}

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ ECR login failed${NC}"
    exit 1
fi

echo -e "${YELLOW}🏷️  Tagging image...${NC}"
docker tag ${ECR_REPOSITORY_NAME}:${IMAGE_TAG} ${ECR_REPOSITORY_URI}:${IMAGE_TAG}

echo -e "${YELLOW}⬆️  Pushing to ECR...${NC}"
docker push ${ECR_REPOSITORY_URI}:${IMAGE_TAG}

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Successfully pushed to ECR!${NC}"
    echo -e "${GREEN}📋 Image URI: ${ECR_REPOSITORY_URI}:${IMAGE_TAG}${NC}"
else
    echo -e "${RED}❌ Push to ECR failed${NC}"
    exit 1
fi 