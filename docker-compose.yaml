services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vifit-nodejs
    command: npm run dev
    env_file:
      - .env
    ports:
      - "3000:3000"
    networks:
      - devel
    depends_on:
      - mysql8

  mysql8:
    image: mysql:8
    container_name: mysql8
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: p@ssw0rd
      MYSQL_DATABASE: vifit_dev
      MYSQL_USER: cmpl
      MYSQL_PASSWORD: cmpl
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - devel

volumes:
  mysql_data:

networks:
  devel:
    name: devel
    external: true
