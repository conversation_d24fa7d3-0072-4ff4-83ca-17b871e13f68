#!/bin/bash

# Usage: ./deploy-to-fargate.sh [api|cron] [AWS_ACCOUNT_ID] [SECRET_NAME]
SERVICE_TYPE=${1:-api}
AWS_ACCOUNT_ID=${2:-************}
SECRET_NAME=${3:-vifit-api-env-staging}

AWS_REGION="ap-southeast-1"
ECR_REPOSITORY_NAME="vifit/nodejs-api"
ECS_CLUSTER_NAME="vifit-cluster"

if [ "$SERVICE_TYPE" = "cron" ]; then
  TASK_DEFINITION_TEMPLATE="task-definition-cron.template.json"
  TASK_DEFINITION_FILE="task-definition-cron.json"
  TASK_DEFINITION_FAMILY="vifit-nodejs-cron"
  ECS_SERVICE_NAME="vifit-nodejs-cron-service"
else
  TASK_DEFINITION_TEMPLATE="task-definition-api.template.json"
  TASK_DEFINITION_FILE="task-definition-api.json"
  TASK_DEFINITION_FAMILY="vifit-nodejs-api"
  ECS_SERVICE_NAME="vifit-nodejs-api-service"
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 ViFit NodeJS API - ECS Fargate Deployment [${SERVICE_TYPE}] [${AWS_ACCOUNT_ID}] [${SECRET_NAME}]${NC}"
echo -e "${BLUE}=============================================${NC}"

# Function to check command existence
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}❌ $1 is not installed. Please install it first.${NC}"
        exit 1
    fi
}

# Check prerequisites
echo -e "${YELLOW}🔍 Checking prerequisites...${NC}"
check_command "aws"
check_command "docker"
check_command "jq"
check_command "envsubst"

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Set ECR repository URI
ECR_REPOSITORY_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY_NAME}"

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Step 1: Build and push to ECR
echo -e "${YELLOW}📦 Step 1: Building and pushing to ECR...${NC}"

# Ensure buildx is available and use linux/amd64 for Fargate compatibility
docker buildx version &>/dev/null || (echo -e "${YELLOW}🔧 Enabling Docker Buildx...${NC}" && docker buildx create --use)

docker buildx build --platform linux/amd64 -t ${ECR_REPOSITORY_NAME}:latest --load .

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Docker build failed${NC}"
    exit 1
fi

echo -e "${YELLOW}🔐 Logging into ECR...${NC}"
aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${ECR_REPOSITORY_URI}

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ ECR login failed${NC}"
    exit 1
fi

echo -e "${YELLOW}🏷️  Tagging and pushing image...${NC}"
docker tag ${ECR_REPOSITORY_NAME}:latest ${ECR_REPOSITORY_URI}:latest
docker push ${ECR_REPOSITORY_URI}:latest

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Push to ECR failed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Image pushed to ECR successfully${NC}"

# Step 2: Create ECS cluster if it doesn't exist
echo -e "${YELLOW}📋 Step 2: Creating ECS cluster...${NC}"
aws ecs create-cluster --cluster-name ${ECS_CLUSTER_NAME} --region ${AWS_REGION} 2>/dev/null || echo -e "${YELLOW}⚠️  Cluster already exists${NC}"

# Step 3: Create CloudWatch log group
echo -e "${YELLOW}📝 Step 3: Creating CloudWatch log group...${NC}"
aws logs create-log-group --log-group-name "/ecs/${TASK_DEFINITION_FAMILY}" --region ${AWS_REGION} 2>/dev/null || echo -e "${YELLOW}⚠️  Log group already exists${NC}"

# Step 4: Register task definition
echo -e "${YELLOW}📋 Step 4: Registering task definition...${NC}"

export AWS_ACCOUNT_ID SECRET_NAME
envsubst < ${TASK_DEFINITION_TEMPLATE} > ${TASK_DEFINITION_FILE}

aws ecs register-task-definition --cli-input-json file://${TASK_DEFINITION_FILE} --region ${AWS_REGION}

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Task definition registration failed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Task definition registered successfully${NC}"

# Step 5: Create or update ECS service
echo -e "${YELLOW}🔄 Step 5: Creating/updating ECS service...${NC}"

# Check if service exists
SERVICE_EXISTS=$(aws ecs describe-services --cluster ${ECS_CLUSTER_NAME} --services ${ECS_SERVICE_NAME} --region ${AWS_REGION} --query 'services[0].status' --output text 2>/dev/null)

if [ "$SERVICE_EXISTS" = "ACTIVE" ]; then
    echo -e "${YELLOW}🔄 Updating existing service...${NC}"
    aws ecs update-service --cluster ${ECS_CLUSTER_NAME} --service ${ECS_SERVICE_NAME} --task-definition ${TASK_DEFINITION_FAMILY} --region ${AWS_REGION}
else
    echo -e "${YELLOW}🆕 Creating new service...${NC}"
    # Note: You'll need to update ecs-service.json with actual values before running this
    # aws ecs create-service --cli-input-json file://ecs-service.json --region ${AWS_REGION}
    echo -e "${YELLOW}⚠️  Please update ecs-service-${SERVICE_TYPE}.json with your actual values and run:${NC}"
    echo -e "${BLUE}   aws ecs create-service --cli-input-json file://ecs-service-${SERVICE_TYPE}.json --region ${AWS_REGION}${NC}"
fi

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo -e "${GREEN}📋 Service URL: https://your-alb-domain.com${NC}"
echo -e "${GREEN}📊 Monitor logs: https://console.aws.amazon.com/cloudwatch/home?region=${AWS_REGION}#logsV2:log-groups/log-group/%2Fecs%2F${TASK_DEFINITION_FAMILY}${NC}" 