# Vifit Backend API

A modern Node.js backend service for fitness studio management and booking system.

## 🎯 Requirements

- Node v22.x
- MySQL 8.4.x
- Docker & Docker Compose (recommended)

## 📁 Project Layout

```
├── src/ # Source code
│ ├── specs/ # OpenAPI specifications
│ ├── booking-api/ # Booking system endpoints
│ ├── instructor-api/ # Instructor management
│ ├── member-api/ # Member services
│ ├── public-api/ # Public endpoints
│ ├── database/ # Database models and migrations
│ │ ├── models/ # Sequelize models
│ │ ├── migrations/ # Sequelize migrations
│ │ └── seeders/ # Database seeders
│ └── config/ # Configuration files
├── flyway/ # Legacy database migrations
├── contrib/ # Development tools
├── conf/ # Configuration files
└── http-client/ # API test templates
```

## 🚀 Quick Start

### 1. Database Setup

Create development network
```
docker network create devel
```

Start MySQL container
```
docker compose up -d mysql8
```

Run Flyway migrations (if needed), follow the instructions in Development Tools - Database Migrations section.
```
./flyway/flyway-docker.sh

flyway >> flyway migrate
```

### 2. Install Dependencies
```
npm install

npm install -g typescript
```
### 3. Ngrok Reverse Proxy Setup - for HitPay Webhook

Register ngrok account and install ngrok
```
brew install ngrok
```

Set ngrok authtoken
```
ngrok config add-authtoken {ngrok-authtoken}
```

Run ngrok
```
ngrok http 3000
```


### 4. Configure Environment
```
cp .env.example .env
```

Copy `.env.example` as `.env`, and edit accordingly.

`DATABASE_xxx` for database settings.

`MOCK_AUTH_xxx` only if using mockAuth during development (see `init-openapi.ts`).

`CORS_ALLOWED_ORIGINS` for allowed CORS origins (studios, backoffice).

`FIREBASE_APP_CREDENTIALS_xxx`
- 3 sets (ie, 2 Firebase projects): one for mobile app, two for studios and backoffice.
- For mobile app, Firebase Authentication Sign-In Providers: Google, Apple.
- For studios and backoffice, Firebase Authentication Sign-In Provider: Email/Password.
- Generate the credentials from Firebase Console > Project Settings > Firebase Admin SDK > Generate new private key.
- Each set of credentials is a JSON file.

`GOOGLE_MAPS_API_KEY`
- https://developers.google.com/maps/documentation/javascript/get-api-key
- Enable Google Maps JavaScript API.
  - Geocoding API
  - Maps SDK for Android
  - Maps SDK for iOS
  - Places API

`AWS_xxx` for AWS Services Uses
 - Simple Email Service (SES).
 - S3 Bucket Storage
 - Secret Manager
   - `AWS_SECRETS_NAME` & `AWS_RDS_SECRETS_NAME` only for production env uses.

`HITPAY_xxx` for HitPay.
- Get API key and salt from HitPay. (Ask your team to get it)
- Documentation: https://docs.hitpayapp.com/apis/overview
- `HITPAY_WEBHOOK_URL` is the ngrok URL (Get from ngrok command above).
  - https://{ngrok-id}.ngrok-free.app generated by ngrok.

`PIC_URL_PREFIX` for NodeJS API(s) Endpoint.

`TAWK_API_KEY` for Tawk.to.

Set `NODE_ENV=production` to generate log files. Log setup in `logger.ts`.

### 5. Start Development Server
```
# Development mode
npm run dev
```

### 6. Scheduled Tasks
```
npm run cron
```

## 🛠 Development Tools

### OpenAPI Integration

- Specifications located in `src/specs/`
- Each API endpoint maps to a route handler:
  ```
  operationId → src/{api-name}/routes/{operation-id}.ts
  ```
- Example: `instructor-api/exists` → `src/instructor-api/routes/exists.ts`

### Docker Development Environment

API development (with port mapping)
```
./node22-docker-serve.sh
```

Scheduled task development
```
./node22-docker-bash.sh
```

### Database Migrations

#### Flyway Migrations (Legacy)
Run Flyway migrations
```
./flyway/flyway-docker.sh
```

inside the container
```
flyway migrate
```

#### Sequelize Migrations & Seeders (Current)

The project uses Sequelize for database management alongside Flyway.

**Generate Migration:**
```bash
npm run make:migration migration_name (e.g., create-trx01banks, add-column-to-trx01banks)
```

**Run Migrations:**
```bash
npm run migrate
```

**Rollback Migration:**
```bash
npm run migrate:rollback
```

**Generate Seeder:**
```bash
npm run make:seeder seeder_name (e.g., banks)
```

**Run Seeders:**
```bash
# Run all seeders
npm run seed

# Run specific seeder
npm run seed:one seeder_name (e.g., YYYYMMDDHHMMSS-seeder-name.js)
```

**Sequelize Configuration:**
- Models: `src/database/models/`
- Migrations: `src/database/migrations/`
- Seeders: `src/database/seeders/`
- Associations: `src/database/models/_associations.ts`

**Model Associations:**
Associations are defined in `src/database/models/_associations.ts` to avoid circular dependencies. This file is loaded after all models are initialized.

Example association:
```typescript
// Bank -> Country (Many-to-One)
Bank.belongsTo(Country, {
  foreignKey: 'countryCode',
  targetKey: 'code',
  as: 'countryInfo'
});

// Country -> Bank (One-to-Many)
Country.hasMany(Bank, {
  foreignKey: 'countryCode',
  sourceKey: 'code',
  as: 'banks'
});
```

### Working with Sequelize Models

#### Creating New Models
1. Create a new model file in `src/database/models/`
2. Follow the existing pattern (see `bank.ts` or `country.ts`)
3. Add associations in `src/database/models/_associations.ts`

## 🔒 Authentication & Security

### Firebase Setup

1. Create two Firebase projects:
   - Mobile App Project
     - Enable Google Sign-In
     - Enable Apple Sign-In
   - Studios Project
     - Enable Email/Password Sign-In

2. Generate Service Account Keys:
   - Firebase Console → Project Settings → Service Accounts
   - Generate Private Keys
   - Save in `/conf` directory

## 📊 Data Management

### Exercise Data Import
- Source data in `contrib/exercises-data/`
- Follow import instructions below (on Mac)
  - First install Homebrew if you don't have it
  ```
  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
  ```

  - Then install Maven
  ```
  brew install maven
  ```

  - Change directory and copy .env.example to .env and edit .env
  ```
  cd contrib/exercises-data/vifit
  cp .env.example .env
  ```

  - Edit pom.xml to change Java version
  ```
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!-- Change this line to match your Java version -->
    <maven.compiler.release>17</maven.compiler.release>
  </properties>
  ```

  - Run Maven build
  ```
  mvn clean package
  ```

  - Run the import script
  ```
  mvn exec:java -Dexec.mainClass="com.connectedmachines.vifit.cli.ImportExercisesData"
  ```

### API Testing
- Use IntelliJ HTTP Client plugin
- Test templates in `http-client/`
  - `booking-api.http`
  - `instructor-api.http`
  - `member-api.http`
  - `public-api.http`
  - `studio-cms.http`
